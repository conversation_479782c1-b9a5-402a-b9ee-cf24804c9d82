<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
	</cfscript>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			this.link.copyReport = buildCurrentLink(arguments.event,"copyReport") & "&mode=stream";
			this.link.showMyReports = buildCurrentLink(arguments.event,"showMyReports");
			this.link.showAllReports = buildCurrentLink(arguments.event,"showAllReports");

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>					
	</cffunction>		

	<cffunction name="showMyReports" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.data = listReports(arguments.event,false)>
		
		<cfreturn local.data>	
	</cffunction>

	<cffunction name="showAllReports" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
			
		<cfset local.data = listReports(arguments.event,true)>
		
		<cfreturn local.data>	
	</cffunction>

	<cffunction name="listReports" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="showAllReports" type="boolean" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.dsp = arguments.showAllReports ? "all" : "my">

		<cfset local.savedReportsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=savedReportsJSON&meth=getSavedReports&mode=stream&dsp=#local.dsp#&srid=#this.siteResourceID#">
		<cfset local.savedReportsAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=savedReportsJSON&meth=getSavedReportsAuditLog&mode=stream">

		<cfset local.qryReportTypes = getReportTypes(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.qryReportCreators = getReportCreators(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reports.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copyReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">
		
		<cfset CreateObject("component","model.admin.reports.report").copyReport(event=arguments.event)>
	</cffunction>
		
	<cffunction name="deleteReport" access="public" output="false" returntype="struct">
		<cfargument name="rptId" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="rpt_deleteSavedReport" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.rptId#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getReportTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryReportTypes" datasource="#application.dsn.memberCentral.dsn#">
			select 
				distinct sr.toolTypeID, toolType, toolDesc as typeName
			from 
				dbo.rpt_SavedReports sr
				inner join dbo.admin_toolTypes tt on 
					tt.toolTypeID = sr.toolTypeID 
					and sr.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			order by toolDesc
		</cfquery>

		<cfreturn local.qryReportTypes>
	</cffunction>	

	<cffunction name="getReportCreators" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryReportCreators" datasource="#application.dsn.memberCentral.dsn#">
			select distinct m2.memberID, m2.firstName, m2.middleName, m2.lastName, m2.lastname + ', ' + m2.firstName as createdByName
			from dbo.rpt_SavedReports sr
			inner join membercentral.dbo.ams_members as m on m.memberID = sr.memberID
			inner join membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
			where sr.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			order by createdByName
		</cfquery>

		<cfreturn local.qryReportCreators>
	</cffunction>	

	<cffunction name="doStatusChange" access="public" output="false" returntype="struct" hint="Change Status From Grid List">
		<cfargument name="mcproxy_orgID" type="numeric">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="reportID" type="numeric">
		<cfargument name="status" type="string">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">,
					@reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.reportID#">,
					@status char(1) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.status#">;

				UPDATE dbo.rpt_SavedReports
				SET [Status] = @status
				WHERE siteID = @siteID
				AND reportID = @reportID;

				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				SELECT '{ "c":"auditLog", "d": {
					"AUDITCODE":"RPT",
					"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#"> AS VARCHAR(10)) + ',
					"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
					"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#"> as varchar(20)) + ',
					"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
					"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars('Saved Report ' + r.reportName + ' [' + tt.toolDesc + ']' + ' has been ' + CASE WHEN @status = 'A' THEN 'activated' ELSE 'inactivated' END + '.'),'"','\"') + '" } }'
				FROM dbo.rpt_SavedReports AS r
				INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
				WHERE r.reportID = @reportID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	

		<cfset local.data = structNew()>
		<cfset local.data.success = true>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageAutoRunReports" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.scheduledReportsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=savedReportsJSON&meth=getScheduledReports&mode=stream";
			local.reportsForCalendarLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=savedReportsJSON&meth=getReportsForCalendar&mode=stream";
			local.editScheduledReportLink = buildCurrentLink(arguments.event,"editScheduledReport") & "&mode=direct";
			
			savecontent variable="local.data" {
				include template="dsp_autoRunReports.cfm";
			}

			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="editScheduledReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.saveScheduledReportLink = buildCurrentLink(arguments.event,"saveScheduledReport") & "&mode=stream";
			
			local.qryScheduledReport = queryExecute("
				SELECT sr.itemID, sr.reportID, sr.reportAction, sr.fileName, sr.toEmail, sr.emailbody,
					sr.interval, sr.intervalTypeID, sr.nextRunDate, sr.endRunDate
				FROM dbo.rpt_scheduledReports AS sr
				WHERE sr.siteID = :siteID
				AND sr.itemID = :itemID
			",
				{ 
					siteID = { value=arguments.event.getValue('mc_siteInfo.siteID'), cfsqltype="cf_sql_integer" },
					itemID = { value=arguments.event.getValue('itemID',0), cfsqltype="cf_sql_integer" }
				},
				{ datasource=application.dsn.membercentral.dsn }
			);

			local.qrySavedReports = queryExecute("
				SELECT sr.reportID, tt.toolDesc, tt.toolType, sr.reportName
				FROM dbo.rpt_SavedReports AS sr
				INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = sr.toolTypeID
					AND sr.siteID = :siteID
					AND tt.includeInAllReportsGrid = 1
				INNER JOIN dbo.admin_siteTools AS ast ON ast.toolTypeID = sr.toolTypeID
					AND ast.siteID = sr.siteID
				WHERE sr.[status] = 'A'
				ORDER BY tt.toolType, sr.reportName, tt.toolDesc
			",
				{ siteID = { value=arguments.event.getValue('mc_siteInfo.siteID'), cfsqltype="cf_sql_integer" }	},
				{ datasource=application.dsn.membercentral.dsn }
			);

			local.qryScheduledTaskIntervalTypes = queryExecute("
				SELECT intervalTypeID, [name]
				FROM dbo.scheduledTaskIntervalTypes
				WHERE SQLDatepart in ('dd','mm','yy')
				ORDER BY [name]
			", {}, { datasource=application.dsn.membercentral.dsn });

			arguments.event.paramValue('rptRunFormats',local.qryScheduledReport.reportAction);
			local.arrSupportedFormats = [];
			
			for (local.thisFormat in arguments.event.getValue('rptRunFormats')) {
				switch(local.thisFormat) {
					case 'customcsv': 
						arrayAppend(local.arrSupportedFormats, { "value":"customcsv", "text":"CSV" });
						break;
					case 'pdf': 
						arrayAppend(local.arrSupportedFormats, { "value":"pdf", "text":"PDF" });
						break;
				}
			}

			local.interval = local.qryScheduledReport.recordCount GT 0 ? local.qryScheduledReport.interval : 1;
			local.intervalTypeID = local.qryScheduledReport.intervalTypeID;
			if (NOT local.qryScheduledReport.recordCount)
				local.intervalTypeID = local.qryScheduledTaskIntervalTypes.filter(function(thisRow){ return arguments.thisRow.name EQ 'days' }).intervalTypeID;

			local.nextRunDate = len(local.qryScheduledReport.nextRunDate) 
								? DateTimeFormat(local.qryScheduledReport.nextRunDate,'m/d/yyyy - h:nn tt') 
								: DateFormat(DateAdd("d",1,now()),"m/d/yyyy") & " - 6:00 AM";
								
			local.endRunDate = len(local.qryScheduledReport.endRunDate) 
								? DateTimeFormat(local.qryScheduledReport.endRunDate,'m/d/yyyy - h:nn tt') 
								: "";

			savecontent variable="local.data" {
				include template="frm_scheduledReport.cfm";
			}

			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="saveScheduledReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.itemID = int(val(arguments.event.getValue('itemID',0)));

			if (len(arguments.event.getValue('schedReportToEmail',''))) {
				local.schedReportToEmail = replace(replace(arguments.event.getValue('schedReportToEmail'),',',';','ALL'),' ','','ALL');
				local.arrEmails = listToArray(local.schedReportToEmail,';');
				for (local.i=1; local.i lte arrayLen(local.arrEmails); local.i++) {
					if (len(local.arrEmails[local.i]) and not isValid("regex",local.arrEmails[local.i],application.regEx.email)) {
						arrayDeleteAt(local.arrEmails,local.i);
					}
				}
				arguments.event.setValue('schedReportToEmail',arrayToList(local.arrEmails,';'));
			}
		</cfscript>

		<cfset local.schedReportFileName = ReReplace(arguments.event.getTrimValue('schedReportFileName'),"[^A-Za-z0-9\.\-\_\s]","_","ALL")>
		
		<cfset local.fileExt = "txt">
		<cfif arguments.event.getValue('schedReportAction') EQ "customcsv">
			<cfset local.fileExt = "csv">
		<cfelseif arguments.event.getValue('schedReportAction') EQ "pdf">
			<cfset local.fileExt = "pdf">
		</cfif>

		<cfif right(local.schedReportFileName,4) NEQ ".#local.fileExt#">
			<cfset local.schedReportFileName = local.schedReportFileName & ".#local.fileExt#">
		</cfif>

		<cfif local.itemID GT 0>
			<cfstoredproc procedure="rpt_updateScheduledReport" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.itemID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReport')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('schedReportAction')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#local.schedReportFileName#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('schedReportToEmail')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('schedReportEmailBody')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('schedReportNextRunDate'),' - ',' '))#">
				<cfif len(arguments.event.getValue('schedReportEndRunDate')) eq 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
				<cfelse>
					<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('schedReportEndRunDate'),' - ',' '))#">
				</cfif>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReportInterval')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReportIntervalType')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			</cfstoredproc>
		<cfelse>
			<cfstoredproc procedure="rpt_createScheduledReport" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReport')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('schedReportAction')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#local.schedReportFileName#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('schedReportToEmail')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('schedReportEmailBody')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('schedReportNextRunDate'),' - ',' '))#">
				<cfif len(arguments.event.getValue('schedReportEndRunDate')) eq 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
				<cfelse>
					<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.event.getValue('schedReportEndRunDate'),' - ',' '))#">
				</cfif>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReportInterval')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedReportIntervalType')#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
				<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.itemID">
			</cfstoredproc>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					<cfif val(arguments.event.getValue('itemID',0)) GT 0>
						top.reloadScheduledReports(#arguments.event.getValue('schedReport')#,true);
					<cfelse>
						top.location.reload();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteScheduledReport" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="itemID" type="numeric" required="true">

		<cfset var qryDeleteScheduledReport = "">

		<cfquery name="qryDeleteScheduledReport" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @itemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@reportID int, @editingEnabled bit = 0, @msg varchar(max), @nowDate datetime = GETDATE();
				
				SELECT @reportID = r.reportID,
					@msg = 'Schedule of [Every ' + CAST(sch.interval as varchar(10)) + ' ' + CASE WHEN sch.interval = 1 THEN it.[singular] ELSE it.[name] END + '] removed from ' + r.reportName + ' [' + tt.toolDesc + '].'
				FROM rpt_scheduledReports AS sch
				INNER JOIN dbo.scheduledTaskIntervalTypes AS it ON it.intervalTypeID = sch.intervalTypeID
				INNER JOIN dbo.rpt_SavedReports AS r ON r.reportID = sch.reportID
				INNER JOIN dbo.admin_toolTypes tt ON tt.toolTypeID = r.toolTypeID
				WHERE sch.itemID = @itemID;
				
				DELETE FROM dbo.rpt_scheduledReports
				WHERE itemID = @itemID
				AND siteID = @siteID;

				IF NOT EXISTS (SELECT 1 FROM dbo.rpt_scheduledReports WHERE siteID = @siteID AND reportID = @reportID) BEGIN
					UPDATE dbo.rpt_SavedReports
					SET isReadOnly = 0,
						readOnlyUpdateDate = @nowDate
					WHERE siteID = @siteID
					AND reportID = @reportID
					AND isReadOnly = 1;

					IF @@ROWCOUNT > 0 BEGIN
						SET @editingEnabled = 1;
						SET @msg = @msg + char(13) + char(10) + 'Additionally, the saved report has been marked as editable again.';
					END
				END

				INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
				VALUES ('{ "c":"auditLog", "d": {
					"AUDITCODE":"RPT",
					"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#"> AS VARCHAR(10)) + ',
					"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
					"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#"> as varchar(20)) + ',
					"ACTIONDATE":"' + CONVERT(varchar(20),@nowDate,120) + '",
					"MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msg),'"','\"') + '" } }');
				
				SELECT @editingEnabled AS editingEnabled;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "reload":qryDeleteScheduledReport.editingEnabled }>
	</cffunction>

	<cffunction name="checkReportSchedules" access="public" output="false" returntype="struct" hint="Check if a report has scheduled deliveries">
		<cfargument name="reportID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { success=false, hasSchedules=false }>

		<cftry>
			<cfquery name="local.qrySchedules" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT COUNT(*) as scheduleCount
				FROM dbo.rpt_scheduledReports
				WHERE reportID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reportID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.strReturn.success = true>
			<cfset local.strReturn.hasSchedules = local.qrySchedules.scheduleCount gt 0>
		<cfcatch type="Any">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>
</cfcomponent>