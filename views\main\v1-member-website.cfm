﻿<a name="v1-member-website"></a>
<section id="v1-member-website">
	<h3>/member/website</h3>
	<p>
		GET - Returns the member's website addresses<br/>
		PUT - Updates a member's website address<br/>
		DELETE - Removes a member's website address<br/>
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/website</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-info">
			Each website listed in the response will have the following data:
			<ul>
				<li><i>type</i> - name of the website type</li>
				<li><i>website</i> - the member's website for this website type</li>
				<li><i>api_id</i> - unique identifer of the website type</li>
				<li><i>x-api-uri</i> - the URI of the member website type</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/website HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":2,
        "website": [
            {
                "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri":"/v1/member/SAMPLE123456/website/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "type":"Website",
                "website":"http://www.mysite.org"
            },
            {
                "api_id":"YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "x-api-uri":"/v1/member/SAMPLE123456/website/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "type":"Company Website",
                "website":"http://www.mysite.org/MyPage"
            }
         ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/website/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			If the member has a website defined for this website type, the website listed in the response will have the same data as the /member/website GET call.<br/>
			If the member does not have a website defined for this website type, the response will be a 404 Member Website Not Found.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/website/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 WEBSITE TYPE NOT FOUND</td><td>invalid website api_id</td></tr>
			<tr><td class="rc">404 MEMBER WEBSITE NOT FOUND</td><td>member does not have a website for this website type</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "website":{
            "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri":"/v1/member/SAMPLE123456/website/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "type":"Website",
            "website":"http://www.mysite.org"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER WEBSITE NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member Website not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put method-example">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/website/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Required Request Details</div>
		<div class="jsonblock-info">
			The request must contain a JSON object in the body with one key, <i>website</i>.<br/>
			<div style="margin-left:30px;">
				<i>website</i> is the member's website address for this website type.
			</div>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/website/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 46

{
    "website": "http://www.mysite.org/MyPage"
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>no changes to process</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 WEBSITE TYPE NOT FOUND</td><td>invalid website api_id</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error updating website</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Company Website changed from http://www.mysite.org to http://www.mysite.org/MyPage",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (no changes)</div>
		<div class="jsonblock">
<pre class="prettyprint">
202 NOT UPDATED

{
    "data": {
        "result": "No changes to process.",
        "report": [
            "summary": [
                "1 data change was rejected and ignored.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [
                "LastName, NewFirstName (SAMPLE123456) - Company Website is not valid: http://mysite"
            ],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": []
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update member.",
        ...
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="delete method-example">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/website/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/member/SAMPLE123456/website/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>member did not have an website for this website type</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 WEBSITE TYPE NOT FOUND</td><td>invalid website api_id</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error deleting website</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Website <NAME_EMAIL> to [blank]",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>