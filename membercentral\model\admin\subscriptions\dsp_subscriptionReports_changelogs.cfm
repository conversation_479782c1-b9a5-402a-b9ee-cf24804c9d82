<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		let changeLogsTable;

		function initChangeLogsTable() {
			let domString = "<'row'<'d-flex col-sm-5 col-md-5'<'mt-2'l><'d-flex flex-wrap p-1 m-2'>><'col-sm-7 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			changeLogsTable = $('##changeLogsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 25,
				"lengthMenu": [ 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": '#local.subscriberChangeLogsLink#',
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div><a href="javascript:editMember('+data.memberid+')" _target="blank">'+data.membername+'</a></div>';
								if(data.membercompany.length) renderData += '<div class="text-dim small">'+data.membercompany+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "16%"
					},
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div"><a href="javascript:showSubTree('+data.memberid+','+data.rootsubscriberid+')">'+data.description+'</a></div>';
								renderData += '<div>'+ data.substartdate +' - '+ data.subenddate +'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "40%"
					},
					{ "data": "previousstatus", "width": "14%", "className": "align-top" },
					{ "data": "updatedate", "width": "15%", "className": "align-top" },
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div><a href="##" title="Filter on this member" onclick="updateFilterAndRun(\'updtByMemberID\',' + data.actormemberid + ',\'' + data.actormembernumber + '\',\'' + data.actormembernameForJS + '\')"><i class="fa-solid fa-filter"></i></a> ' + data.actormembername + '</div><div>' + data.daterecorded + '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "15%"
					}
				],
				"order": [[3, 'desc']],
				"searching": false,
				"pagingType": "simple"
			});

			mca_generateVerboseFilterMessage('frmFilter4');
		}
		function reloadSubsChangeLogsTable(){
			changeLogsTable.draw();
		}
		function clearFilterChangeLogsGrid() {
			/* since reset() won't clear fields with default values */
			$('##frmFilter4 input[type="hidden"], ##frmFilter4 input[type="text"], ##fHasCard4').val('');
			$('##fSubPaymentStatus4, ##fFreq4, ##selfSubType4, ##selfSubscription4, ##fRevenueGL4, ##fSubStatusFrom, ##fSubStatusTo').val(0);
			$('##fSubStatus4').val('0');
			$('##selfRate4').empty().trigger("change");
			$('##aClearAssocType4').click();
			$('##aClearUpdtByType').click();
			filterChangeLogsGrid();
		}

		function generateCustomSubChangeLogsRadioFilterVerbose(filterField) {
			let label = "";
			let value = "";
			const fieldId = filterField.attr('id');

			if (filterField.is(':checked')) {
				switch (fieldId) {
					case 'assocTypeMember4':
						label = 'Associated With Member';
						value = $('##associatedMemberName4').val() + ' (' + $('##associatedMemberNum4').val() + ')';
						let incLinkedRecordFldID = $('input[name="linkedRecords4"]:checked').attr('id');
						if (incLinkedRecordFldID) value += ' [' + $(`label[for='${incLinkedRecordFldID}']`).text().trim() + ']';
					break;

					case 'assocTypeGroup4':
						label = 'Associated With Group';
						value = $('##associatedGroupName4').val();
					break;

					case 'updtByTypeMember':
						label = 'Updated By Member';
						value = $('##updtByMemberName').val() + ' (' + $('##updtByMemberNum').val() + ')';
						let incUpdByLinkedRecordFldID = $('input[name="updtByLinkedRecords"]:checked').attr('id');
						if (incUpdByLinkedRecordFldID) value += ' [' + $(`label[for='${incUpdByLinkedRecordFldID}']`).text().trim() + ']';
					break;

					case 'updtByTypeGroup':
						label = 'Updated By Group';
						value = $('##updtByGroupName').val();
					break;

					default:
						label = $(`label[for='${fieldId}']`).text().trim();
						value = filterField.val();
				}
			}

			return { label, value };
		}
		function filterChangeLogsGrid() {
			var saveFilterResult = function(r) {
				mca_generateVerboseFilterMessage('frmFilter4');
				reloadSubsChangeLogsTable();
			};

			var sID = $('##fSubStatus4').val();
			var stID = $('##fSubType4').val();
			if (stID.length == 0) stID = '0';
			var subID = $('##fSubscription4').val();
			if (subID.length == 0) subID = '0';
			var freqID = $('##fFreq4').val();
			var dtTSf = $('##fTermStartFrom4').val();
			var dtTSt = $('##fTermStartTo4').val();
			var dtTEf = $('##fTermEndFrom4').val();
			var dtTEt = $('##fTermEndTo4').val();
			var dtOffrExpF = $('##fOffrExpFrom4').val();
			var dtOffrExpT = $('##fOffrExpTo4').val();
			var spID = $('##fSubPaymentStatus4').val();
			var hasCard = $('##fHasCard4').val();
			var RevGL = $('##fRevenueGL4').val();

			var associatedMemberID = $('##associatedMemberID4').val();
			var linkedRecords = $('input[name="linkedRecords4"]:checked').val();
			var associatedGroupID = $('##associatedGroupID4').val();
			var associatedMemberName = $('##associatedMemberName4').val();
			var associatedMemberNum = $('##associatedMemberNum4').val();
			var associatedGroupName = $('##associatedGroupName4').val();

			var updtByMemberID = $('##updtByMemberID').val();
			var updtByLinkedRecords = $('input[name="updtByLinkedRecords"]:checked').val();
			var updtByGroupID = $('##updtByGroupID').val();
			var updtByMemberName = $('##updtByMemberName').val();
			var updtByMemberNum = $('##updtByMemberNum').val();
			var updtByGroupName = $('##updtByGroupName').val();

			var rateID = $('##fRate4').val();
			if (rateID.length == 0) rateID = '0';
			var subStatusFrom = $('##fSubStatusFrom').val();
			var subStatusTo = $('##fSubStatusTo').val();
			var activityFrom = $('##fActivityFrom').val();
			var activityTo = $('##fActivityTo').val();
			var recordedFrom = $('##fRecordedFrom').val();
			var recordedTo = $('##fRecordedTo').val();

			var objParams = { fType:'changeLog',spID:spID,stID:stID,subID:subID,rateID:rateID,freqID:freqID,dtTSf:dtTSf,dtTSt:dtTSt,dtTEf:dtTEf,dtTEt:dtTEt,sID:sID,
				fCard:hasCard,fRevGL:RevGL,associatedMemberID:associatedMemberID,associatedGroupID:associatedGroupID,associatedMemberName:associatedMemberName,
				associatedMemberNum:associatedMemberNum,associatedGroupName:associatedGroupName,linkedRecords:linkedRecords,updtByMemberID:updtByMemberID,
				updtByGroupID:updtByGroupID,updtByMemberName:updtByMemberName,updtByMemberNum:updtByMemberNum,updtByGroupName:updtByGroupName,
				updtByLinkedRecords:updtByLinkedRecords,dtOffrExpF:dtOffrExpF,dtOffrExpT:dtOffrExpT,subStatusFrom:subStatusFrom,
				subStatusTo:subStatusTo,activityFrom:activityFrom,activityTo:activityTo,recordedFrom:recordedFrom,recordedTo:recordedTo
			};
			TS_AJX('ADMSUBS','saveSubReportFilter',objParams,saveFilterResult,saveFilterResult,20000,saveFilterResult);
		}
		function loadChangeLogsTab() {
			mca_setupDatePickerRangeFields('fTermStartFrom4','fTermStartTo4');
			mca_setupDatePickerRangeFields('fTermEndFrom4','fTermEndTo4');
			mca_setupDatePickerRangeFields('fOffrExpFrom4','fOffrExpTo4');
			mca_setupDateTimePickerRangeFields('fActivityFrom','fActivityTo');
			mca_setupDateTimePickerRangeFields('fRecordedFrom','fRecordedTo');
			mca_setupCalendarIcons('frmFilter4');
			mca_setupSelect2();
			
			$('body').on('change', '##selfSubType4', function(e) {
				mca_callChainedSelect('selfSubType4', 'selfSubscription4', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
				$('##fSubType4').val($('##selfSubType4').val());
				$('##fSubscription4, ##fRate4').val('0');
				$('##selfRate4').empty().trigger("change");
			});

			$(document).on('change', '##fSubStatus4', function(e) {
				if($(this).val() == 'O'){
					$('.offrExpWrap4').removeClass('d-none');
				}else{
					$('.offrExpWrap4 input').val('');
					$('.offrExpWrap4').addClass('d-none');
				}
			});

			$('body').on('change', '##selfSubscription4', function(e) {
				mca_callChainedSelect('selfSubscription4', 'selfRate4', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				$('##fSubscription4').val($('##selfSubscription4').val());
				$('##fRate4').val('0');
			});

			$('body').on('change', '##selfRate4', function(e) {
				var r = $('##selfRate4').val() || '';
				if (r.length > 0) { $('##fRate4').val(r.toString()); } else { $('##fRate4').val(''); }
			});

			if ($('##associatedMemberID4').val() == 0 && $('##associatedGroupID4').val() == 0) $('##divAssociatedVal4').hide();
			if ($('##updtByMemberID').val() == 0 && $('##updtByGroupID').val() == 0) $('##divUpdtByVal').hide();

			$(".assocType4").on("click",function(){	
				var assocType = $('input:radio[name=assocType4]:checked').val();
				if (assocType != undefined) {
					if (assocType == "group") selectGroupFilter4();
					else selectMemberFilter4();
				}
			});

			$("##aClearAssocType4").on("click",function() {
				$(".assocType4").each(function(){
					$(this).attr("checked",false);
				});
				$('##associatedVal4').html("");
				$('##associatedMemberID4').val(0);
				$('##associatedGroupID4').val(0);
				$('##associatedMemberName4').val('');
				$('##associatedMemberNum4').val('');
				$('##associatedGroupName4').val('');
				$('##divAssociatedVal4').hide();
				$("##expandSearch4").hide();
			});

			$(".updtByType").on("click",function(){	
				var updtByType = $('input:radio[name=updtByType]:checked').val();
				if (updtByType != undefined) {
					if (updtByType == "group") selectUpdtByGroupFilter();
					else selectUpdtByMemberFilter();
				}
			});

			$("##aClearUpdtByType").on("click",function() {
				$(".updtByType").each(function(){
					$(this).attr("checked",false);
				});
				$('##updtByVal').html("");
				$('##updtByMemberID').val(0);
				$('##updtByGroupID').val(0);
				$('##updtByMemberName').val('');
				$('##updtByMemberNum').val('');
				$('##updtByGroupName').val('');
				$('##divUpdtByVal').hide();
				$("##updtByExpandSearch").hide();
			});
		}
		function selectGroupFilter4() {
			var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=associatedGroupID4&retFunction=top.updateGroupField4&dispTitle=' + escape('Filter Subscriber Change Logs by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscriber Change Logs by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectMemberFilter4() {
			var selhref = link_memSelectGotoLink+'&mode=direct&fldName=associatedMemberID4&retFunction=top.updateField4&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscriber Change Logs by Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function updateField4(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal4');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				$('##associatedMemberName4').val(mName);
				$('##associatedMemberNum4').val(mNum);
				fldName.html(mName + ' (' + mNum + ')');
				$('##expandSearch4').show();
				$('##associatedGroupID4').val(0);
				$('##divAssociatedVal4').show();
			} else {
				fldName.html('');
				$('##expandSearch4').hide();
				$('##divAssociatedVal4').hide();
			}
		}
		function updateGroupField4(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal4');
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##associatedGroupName4').val(newgPath);
				fldName.html(newgPath);
				$('##associatedMemberID4').val(0);
				$('##expandSearch4').hide();
				$('##divAssociatedVal4').show();
			} else {
				fldName.html('');
				$('##expandSearch4').hide();
				$('##divAssociatedVal4').hide();
			}
		}
		function selectUpdtByGroupFilter() {
			var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=updtByGroupID&retFunction=top.updateUpdtByGroupField&dispTitle=' + escape('Filter Subscriber Change Logs by Updated By Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscriber Change Logs by Updated By Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectUpdtByMemberFilter() {
			var selhref = link_memSelectGotoLink+'&mode=direct&fldName=updtByMemberID&retFunction=top.updateUpdtByMemberField&dispTitle=' + escape('Filter Subscriber Change Logs by Updated By Member');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscriber Change Logs by Updated By Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function updateUpdtByMemberField(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##updtByVal');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				$('##updtByMemberName').val(mName);
				$('##updtByMemberNum').val(mNum);
				fldName.html(mName + ' (' + mNum + ')');
				$('##updtByExpandSearch').show();
				$('##updtByGroupID').val(0);
				$('##divUpdtByVal').show();
			} else {
				fldName.html('');
				$('##updtByExpandSearch').hide();
				$('##divUpdtByVal').hide();
			}
		}
		function updateFilterAndRun(fldID, mID, mNum, mName) {
			$('##updtByTypeMember').click();
			updateUpdtByMemberField(fldID, mID, mNum, mName);
			filterChangeLogsGrid();
		}
		function updateUpdtByGroupField(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##updtByVal');
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##updtByGroupName').val(newgPath);
				fldName.html(newgPath);
				$('##updtByMemberID').val(0);
				$('##updtByExpandSearch').hide();
				$('##divUpdtByVal').show();
			} else {
				fldName.html('');
				$('##updtByExpandSearch').hide();
				$('##divUpdtByVal').hide();
			}
		}
		function startExportChangeLogs() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Export Subscriber Change Logs',
				iframe: true,
				contenturl: '#this.link.startExportSubscriptionsChangeLog#',
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'exportChangeLogsActionButtonHandler',
					extrabuttonlabel: 'Export',
					extrabuttoniconclass: 'fa-light fa-file-csv'
				}
			});
		}
		function exportChangeLogsActionButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.doExport();
		}

		$(document).ready(function(){
			loadChangeLogsTab();

			<cfif local.SubReportFilter.changeLogFilter.fSubType gt 0>
				$('##selfSubType4').val(#local.SubReportFilter.changeLogFilter.fSubType#);
				mca_callChainedSelect('fSubType4', 'selfSubscription4', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubReportFilter.changeLogFilter.fSubscription#, false, false);
				<cfif local.SubReportFilter.changeLogFilter.fSubscription gt 0>
					mca_callChainedSelect('fSubscription4', 'selfRate4', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubReportFilter.changeLogFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div class="toolButtonBar">
	<button class="btn btn-link p-0 mr-2" onclick="startExportChangeLogs();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to export change logs.">
		<i class="fa-regular fa-file-export"></i> Export Change Logs
	</button>
</div>

<div id="divFilterChangeLogsForm" class="mb-3" style="display:none;">
	<form name="frmFilter4" id="frmFilter4" onsubmit="filterChangeLogsGrid();return false;" data-filterwrapper="divFilterChangeLogsForm" data-verbosemsgwrapper="divSubChangeLogsFilterVerbose" data-customverbose-radio="generateCustomSubChangeLogsRadioFilterVerbose">
	<input type="hidden" name="fSubType4" id="fSubType4" value="#local.SubReportFilter.changeLogFilter.fSubType#">
	<input type="hidden" name="fSubscription4" id="fSubscription4" value="#local.SubReportFilter.changeLogFilter.fSubscription#">
	<input type="hidden" name="fRate4" id="fRate4" value="#local.SubReportFilter.changeLogFilter.fRate#">
	<input type="hidden" name="fRevenueGL4" id="fRevenueGL4" value="0">

	<div class="row mb-3">
		<div class="col">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Filter Change Logs
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="selfSubType4" id="selfSubType4" class="form-control">
										<option value="0">All Subscription Types</option>
										<cfloop query="local.qrySubTypes">
											<option value="#local.qrySubTypes.typeID#">#local.qrySubTypes.typeName#</option>
										</cfloop>
									</select>
									<label for="selfSubType4">Subscription Type</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="selfSubscription4" id="selfSubscription4" class="form-control">
										<option value="0">All Subscriptions</option>
									</select>
									<label for="selfSubscription4">Subscription</label>
								</div>
							</div>
							<div class="form-group">
								<div class="d-flex align-items-center mb-1">
									<span class="text-grey small mx-1">Quickly Select: </span>
									<a href="javascript:quickSelectSubRates('selfRate4',0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
									<a href="javascript:quickSelectSubRates('selfRate4',1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
								</div>
								<div class="form-label-group mb-2">
									<select name="selfRate4" id="selfRate4" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
									<label for="selfRate4">Rate</label>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartFrom4" id="fTermStartFrom4" value="#local.SubReportFilter.changeLogFilter.fTermStartFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartFrom4">Start Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartTo4" id="fTermStartTo4" value="#local.SubReportFilter.changeLogFilter.fTermStartTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartTo4">Start Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndFrom4" id="fTermEndFrom4" value="#local.SubReportFilter.changeLogFilter.fTermEndFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndFrom4">End Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndTo4" id="fTermEndTo4" value="#local.SubReportFilter.changeLogFilter.fTermEndTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndTo4">End Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row align-items-center offrExpWrap4 <cfif local.SubReportFilter.changeLogFilter.fSubStatus neq 'O'>d-none</cfif>">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fOffrExpFrom4" id="fOffrExpFrom4" value="#local.SubReportFilter.changeLogFilter.fOffrExpFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpFrom4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpFrom4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fOffrExpFrom4">Offer Expiration From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fOffrExpTo4" id="fOffrExpTo4" value="#local.SubReportFilter.changeLogFilter.fOffrExpTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpTo4"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpTo4');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fOffrExpTo4">Offer Expiration To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubStatus4" id="fSubStatus4" class="form-control">
										<option value="0">All Statuses</option>
										<cfloop query="local.qryStatuses">
											<option value="#local.qryStatuses.statusCode#" <cfif local.SubReportFilter.changeLogFilter.fSubStatus eq local.qryStatuses.statusCode>selected</cfif>>#local.qryStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubStatus4">Status</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubPaymentStatus4" id="fSubPaymentStatus4" class="form-control">
										<option value="0">All Activation Options</option>
										<cfloop query="local.qryPaymentStatuses">
											<option value="#local.qryPaymentStatuses.statusCode#" <cfif local.SubReportFilter.changeLogFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode>selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubPaymentStatus4">Activation Option</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fFreq4" id="fFreq4" class="form-control">
										<option value="0">All Frequencies</option>
										<cfloop query="local.qryFrequencies">
											<option value="#local.qryFrequencies.frequencyID#" <cfif local.SubReportFilter.changeLogFilter.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
										</cfloop>
									</select>
									<label for="fFreq4">Frequency</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fHasCard4" id="fHasCard4" class="form-control">
										<option value="">With or Without Pay Method Associated</option>
										<option value="Y" <cfif local.SubReportFilter.changeLogFilter.fHasCardOnFile eq "Y">selected</cfif>>With Pay Method Associated</option>
										<option value="N" <cfif local.SubReportFilter.changeLogFilter.fHasCardOnFile eq "N">selected</cfif>>With no Pay Method Associated</option>
									</select>
									<label for="fHasCard4">Pay Method</label>
								</div>
							</div>
						</div>
					</div>
					<div class="row my-2">
						<div class="col-auto col-lg-3 col-xl-2">
							Associated With:
						</div>
						<div class="col pl-0">
							<div class="form-check form-check-inline">
								<input type="radio" name="assocType4" id="assocTypeMember4" class="assocType4 form-check-input" value="member" <cfif local.SubReportFilter.changeLogFilter.associatedMemberID gt 0>checked</cfif>>
								<label class="form-check-label" for="assocTypeMember4">A Specific Member</label>
							</div>
							<div class="form-check form-check-inline">
								<input type="radio" name="assocType4" id="assocTypeGroup4" class="assocType4 form-check-input" value="group" <cfif local.SubReportFilter.changeLogFilter.associatedGroupID gt 0>checked</cfif>>
								<label class="form-check-label" for="assocTypeGroup4">A Specific Group</label>
							</div>
							<div id="divAssociatedVal4">
								<span id="associatedVal4" class="font-weight-bold">
									<cfset local.showExpandSearch = false>
									<cfif local.SubReportFilter.changeLogFilter.associatedMemberID gt 0>
										<cfset local.showExpandSearch = true>
										#local.SubReportFilter.changeLogFilter.associatedMemberName# (#local.SubReportFilter.changeLogFilter.associatedMemberNum#)
									<cfelseif local.SubReportFilter.changeLogFilter.associatedGroupID gt 0>
										#local.SubReportFilter.changeLogFilter.associatedGroupName#
									</cfif>
								</span>
								<a href="##" id="aClearAssocType4" class="ml-2">clear</a>
							</div>
							<input type="hidden" name="associatedMemberID4" id="associatedMemberID4" value="#local.SubReportFilter.changeLogFilter.associatedMemberID#">
							<input type="hidden" name="associatedMemberName4" id="associatedMemberName4" value="#local.SubReportFilter.changeLogFilter.associatedMemberName#">
							<input type="hidden" name="associatedMemberNum4" id="associatedMemberNum4" value="#local.SubReportFilter.changeLogFilter.associatedMemberNum#">
							<input type="hidden" name="associatedGroupID4" id="associatedGroupID4" value="#local.SubReportFilter.changeLogFilter.associatedGroupID#">
							<input type="hidden" name="associatedGroupName4" id="associatedGroupName4" value="#local.SubReportFilter.changeLogFilter.associatedGroupName#">

							<div class="row mt-2 mb-1" id="expandSearch4" <cfif NOT local.showExpandSearch >style="display:none"</cfif>>
								<div class="col-sm-12 pr-md-0 font-weight-bold my-2">
									Expand search to consider linked records
								</div>
								<div class="col-md-10 col-sm-12">
									<div class="form-check form-check-inline">
										<input type="radio" name="linkedRecords4" id="linkedRecordsAll4" value="all" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.changeLogFilter.linkedRecords EQ "all">checked="true"</cfif>>
										<label class="form-check-label" for="linkedRecordsAll4">Include children of the selected records in search</label>
									</div>
								</div>
								<div class="col-md-10 col-sm-12">
									<div class="form-check form-check-inline">
										<input type="radio" name="linkedRecords4" id="linkedRecordSelected4" value="selected" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.changeLogFilter.linkedRecords EQ "selected">checked="true"</cfif>>
										<label class="form-check-label" for="linkedRecordSelected4">Only include the specific selected records</label><br/>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row mb-3">
						<div class="col-auto col-lg-3 col-xl-2">
							Updated By:
						</div>
						<div class="col pl-0">
							<div class="form-check form-check-inline">
								<input type="radio" name="updtByType" id="updtByTypeMember" class="updtByType form-check-input" value="member" <cfif local.SubReportFilter.changeLogFilter.updtByMemberID gt 0>checked</cfif>>
								<label class="form-check-label" for="updtByTypeMember">A Specific Member</label>
							</div>
							<div class="form-check form-check-inline">
								<input type="radio" name="updtByType" id="updtByTypeGroup" class="updtByType form-check-input" value="group" <cfif local.SubReportFilter.changeLogFilter.updtByGroupID gt 0>checked</cfif>>
								<label class="form-check-label" for="updtByTypeGroup">A Specific Group</label>
							</div>
							<div id="divUpdtByVal">
								<span id="updtByVal" class="font-weight-bold">
									<cfset local.showUpdtByExpandSearch = false>
									<cfif local.SubReportFilter.changeLogFilter.updtByMemberID gt 0>
										<cfset local.showUpdtByExpandSearch = true>
										#local.SubReportFilter.changeLogFilter.updtByMemberName# (#local.SubReportFilter.changeLogFilter.updtByMemberNum#)
									<cfelseif local.SubReportFilter.changeLogFilter.updtByGroupID gt 0>
										#local.SubReportFilter.changeLogFilter.updtByGroupName#
									</cfif>
								</span>
								<a href="##" id="aClearUpdtByType" class="ml-2">clear</a>
							</div>
							<input type="hidden" name="updtByMemberID" id="updtByMemberID" value="#local.SubReportFilter.changeLogFilter.updtByMemberID#">
							<input type="hidden" name="updtByMemberName" id="updtByMemberName" value="#local.SubReportFilter.changeLogFilter.updtByMemberName#">
							<input type="hidden" name="updtByMemberNum" id="updtByMemberNum" value="#local.SubReportFilter.changeLogFilter.updtByMemberNum#">
							<input type="hidden" name="updtByGroupID" id="updtByGroupID" value="#local.SubReportFilter.changeLogFilter.updtByGroupID#">
							<input type="hidden" name="updtByGroupName" id="updtByGroupName" value="#local.SubReportFilter.changeLogFilter.updtByGroupName#">

							<div class="row mt-2 mb-1" id="updtByExpandSearch" <cfif NOT local.showUpdtByExpandSearch >style="display:none"</cfif>>
								<div class="col-sm-12 pr-md-0 font-weight-bold my-2">
									Expand search to consider linked records
								</div>
								<div class="col-md-10 col-sm-12">
									<div class="form-check form-check-inline">
										<input type="radio" name="updtByLinkedRecords" id="updtByLinkedRecordsAll" value="all" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.changeLogFilter.updtByLinkedRecords EQ "all">checked="true"</cfif>>
										<label class="form-check-label" for="updtByLinkedRecordsAll">Include children of the selected records in search</label>
									</div>
								</div>
								<div class="col-md-10 col-sm-12">
									<div class="form-check form-check-inline">
										<input type="radio" name="updtByLinkedRecords" id="updtByLinkedRecordSelected" value="selected" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.changeLogFilter.updtByLinkedRecords EQ "selected">checked="true"</cfif>>
										<label class="form-check-label" for="updtByLinkedRecordSelected">Only include the specific selected records</label><br/>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="fSubStatusFrom" id="fSubStatusFrom" class="form-control">
												<option value="0">All Statuses</option>
												<cfloop query="local.qryStatuses">
													<option value="#local.qryStatuses.statusCode#" <cfif local.SubReportFilter.changeLogFilter.fSubStatusFrom eq local.qryStatuses.statusCode>selected</cfif>>#local.qryStatuses.statusName#</option>
												</cfloop>
											</select>
											<label for="fSubStatusFrom">Status Changed From</label>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<select name="fSubStatusTo" id="fSubStatusTo" class="form-control">
												<option value="0">All Statuses</option>
												<cfloop query="local.qryStatuses">
													<option value="#local.qryStatuses.statusCode#" <cfif local.SubReportFilter.changeLogFilter.fSubStatusTo eq local.qryStatuses.statusCode>selected</cfif>>#local.qryStatuses.statusName#</option>
												</cfloop>
											</select>
											<label for="fSubStatusTo">Status Changed To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fActivityFrom" id="fActivityFrom" value="#local.SubReportFilter.changeLogFilter.fActivityFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fActivityFrom"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fActivityFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fActivityFrom">Activity Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fActivityTo" id="fActivityTo" value="#local.SubReportFilter.changeLogFilter.fActivityTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fActivityTo"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fActivityTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fActivityTo">Activity Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fRecordedFrom" id="fRecordedFrom" value="#local.SubReportFilter.changeLogFilter.fRecordedFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fRecordedFrom"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fRecordedFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fRecordedFrom">Date Recorded From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fRecordedTo" id="fRecordedTo" value="#local.SubReportFilter.changeLogFilter.fRecordedTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fRecordedTo"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fRecordedTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fRecordedTo">Date Recorded To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="card-footer p-2 text-right">
					<button type="button" name="btnResetFilterSubs4" class="btn btn-sm btn-secondary" onclick="clearFilterChangeLogsGrid();">Clear Filters</button>
					<button type="submit" name="btnFilterSubs4" class="btn btn-sm btn-primary">
						<i class="fa-light fa-filter"></i> Filter Change Logs
					</button>
					<button type="button" class="btnReApplyFilter d-none" onclick="reloadSubsChangeLogsTable();"></button><!--- hidden button used by verbose fn to refresh datatable --->
				</div>
			</div>
		</div>
	</div>
	</form>
</div>

<div id="divSubChangeLogsFilterVerbose" style="display:none;"></div>
<table id="changeLogsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Name</th>
			<th>Description</th>
			<th>Previous Status</th>
			<th>Activity Date</th>
			<th>Updated By</th>
		</tr>
	</thead>
</table>
</cfoutput>