<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456 HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "member": {
            "company":"Law Office of Jane Doe",
            "datecreated":"August, 05 2017 09:57:31 -0700",
            "datelastupdated":"August, 09 2017 19:09:36 -0700",
            "firstname":"Jane",
            "lastname":"Doe",
            "membernumber":"SAMPLE123456",
            "mcaccountstatus":"Active",
            "mcaccounttype":"User",
            "mcrecordtype":"Individual",
            "middlename":"",
            "prefix":"Mrs.",
            "professionalsuffix":"",
            "suffix":"",
            "username":"JaneDoe",
            "x-photo-uri":"https://www.yourwebsite.org/memberphotos/sample123456.jpg"
            ...
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>