<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/webhook/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. You only need to provide the keys for data you wish to update.<br/>
		<div style="margin-left:30px;">
			<ul>
                <li><i>payload-uri</i> - full payload url of the webhook</li>
                <li>
                    <i>event</i> is an object of events to trigger the webhook. Eligible subkeys are:<br/>
                    <i>membercreate,memberupdate,memberdelete,membermerge,membergroupchange,subscriptionstatuschange</i><br/>
                    <i>membergroupchange</i> supports <i>group_api_id</i> subkey to manage subscribed groups<br/>
                    <i>subscriptionstatuschange</i> supports below subkeys to manage subscribed statuses<br/>
                    <i>type_api_id</i> is an array of subscription type api_id<br/>
                    <i>subscription_api_id</i> is an array of subscription api_id<br/>
                    <i>rate_api_id</i> is an array of subscription rate api_id<br/>
                    <i>status</i> is an array of subscription statuses<br/>
                    <i>activationstatus</i> is the activation status of the subscription<br/>
                </li>
            </ul>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/webhook/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 91

{
    "payload-uri": "https://www.mywebhookreceiver.com",
    "event": {
        "membercreate": {
            "enabled": true
        },
        "memberupdate": {
            "enabled": true
        },
        "membermerge": {
            "enabled": true
        },
        "memberdelete": {
            "enabled": false
        },
        "membergroupchange": {
            "enabled": true,
            "group_api_id": ["AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA","BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB"]
        },
        "subscriptionstatuschange": {
            "enabled": true,
            "status": [
                {
                    "status": ["Expired","Deleted"]
                },
                {
                    "type_api_id": ["CCCCCCCC-CCCC-CCCC-CCCC-CCCCCCCCCCCC","DDDDDDDD-DDDD-DDDD-DDDD-DDDDDDDDDDDD"],
                    "status": ["Renewal Not Sent"]
                },
                {
                    "type_api_id": ["EEEEEEEE-EEEE-EEEE-EEEE-EEEEEEEEEEEE"],
                    "subscription_api_id": ["FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF"],
                    "rate_api_id": ["GGGGGGGG-GGGG-GGGG-GGGG-GGGGGGGGGGGG","HHHHHHHH-HHHH-HHHH-HHHH-HHHHHHHHHHHH","JJJJJJJJ-JJJJ-JJJJ-JJJJ-JJJJJJJJJJJJ"],
                    "activationstatus": "Activation Requirement Met",
                    "status": ["Accepted","Active","Billed"]
                }
            ]
        }
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 WEBHOOK NOT FOUND</td><td>webhook not found</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>unable to update webhook</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "webhook": {
            "payload-uri": "https://www.mywebhookreceiver.com",
            "api_id": "WWWWWWWW-WWWW-WWWW-WWWW-WWWWWWWWWWWW",
            "x-api-uri": "/v1/webhook/WWWWWWWW-WWWW-WWWW-WWWW-WWWWWWWWWWWW",
            "event": {
                "membercreate": {
                    "enabled": true
                },
                "memberupdate": {
                    "enabled": true
                },
                "membermerge": {
                    "enabled": true
                },
                "memberdelete": {
                    "enabled": false
                },
                "membergroupchange": {
                    "enabled": true,
                    "group": [
                        {
                            "api_id": "AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA",
                            "grouppath": "All Members",
                            "x-api-uri": "/v1/group/AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA",
                            "systemgroup": 0,
                            "badgetextcolor": "#ffffff",
                            "x-parent-api-uri": "",
                            "badgebackgroundcolor": "#3b3e66",
                            "alertifpopulated": 0,
                            "membercount": 239,
                            "manualassignments": 1,
                            "protected": 0,
                            "parentapi_id": "",
                            "groupcode": "",
                            "description": "",
                            "group": "All Members"
                        },
                        {
                            "api_id": "BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB",
                            "grouppath": "Former Members",
                            "x-api-uri": "/v1/group/BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB",
                            "systemgroup": 0,
                            "badgetextcolor": "#ffffff",
                            "x-parent-api-uri": "",
                            "badgebackgroundcolor": "#3b3e66",
                            "alertifpopulated": 0,
                            "membercount": 173,
                            "manualassignments": 0,
                            "protected": 0,
                            "parentapi_id": "",
                            "groupcode": "",
                            "description": "",
                            "group": "Former Members"
                        }
                    ]
                },
                "subscriptionstatuschange": {
                    "enabled": true,
                    "status": [
                        {
                            "status": "Deleted, Expired",
                            "subscription": "Type: All Subscription Types; Activation Status: All Activation Options;"
                        },
                        {
                            "status": "Renewal Not Sent",
                            "subscription": "Type: Sections, Membership; Subscription: All Subscriptions; Activation Status: All Activation Options;"
                        },
                        {
                            "status": "Accepted, Active, Billed",
                            "subscription": "Type: Membership; Subscription: Annual Membership; Rate: Associate, Less than 5 years, Student; Activation Status: Activation Requirement Met;"
                        },
                    ]
                }
            },
            "sqsqueue": ""
        },
        "result": "Webhook updated."
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update webhook.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>