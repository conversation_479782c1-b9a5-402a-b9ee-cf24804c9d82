<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/event</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li>
			<i>search</i> is an object of subkeys used to limit the events returned. Eligible subkeys are:<br/>
				<i>registereddatefrom</i> is a date to limit registrations to on or after this date<br/>
				<i>registereddateto</i> is a date to limit registrations to on or before this date<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each event listed in the response will have the following data:
		<ul>
			<li><i>eventcode</i> - unique identifier of the event</li>
			<li><i>title</i> - name of the event</li>
			<li><i>subtitle</i> - sub title of the event</li>
			<li><i>startdate</i> - start date of the event</li>
			<li><i>enddate</i> - end date of the event</li>
			<li><i>alldayevent</i> - if 1, this is an all-day event and event times should be ignored.</li>
			<li><i>event_api_id</i> - api_id of the event</li>
			<li>
				<i>registrant</i> - array of registrant data for this registration. Each item in the array will contain:<br/>
					<i>x-api-uri</i> - the URI of the member's registration<br/>
					<i>attended</i> - if 1, the member attended the event<br/>
					<i>notes</i> - any registrant notes<br/>
					<i>isflagged</i> - if 1, the registrant is flagged<br/>
					<i>dateregistered</i> - date of registration<br/>
					<i>rate_api_id</i> - api_id of the rate<br/>
					<i>rate</i> - name of the rate<br/>
					<i>ratereportcode</i> - unique identifier of the rate
			</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/event HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/event HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 157

{
    "count": 5,
    "start": 10,
    "search": {
        "registereddatefrom": "1/1/<cfoutput>#year(now())#</cfoutput>",
        "registereddateto": "1/31/<cfoutput>#year(now())#</cfoutput>"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":11,
        "event":[
            {
                "eventcode": "ABCDEFGH",
                "title": "Fall Regional Conference",
                "subtitle": "Network and Collaborate",
                "startdate": "2019-03-01 17:00:00",
                "enddate": "2019-03-01 19:00:00",
                "alldayevent": 0,
                "event_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "registrant": [
                    {
                        "x-api-uri": "/v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/YYYYYYY",
                        "attended": 1,
                        "notes": "",
                        "isflagged": 0,
                        "dateregistered": "2019-01-15 08:48:32",
                        "rate_api_id": "ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                        "rate": "Sample Rate",
                        "ratereportcode": "LMNOPQ"
                    }
                    ...
                ]
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>