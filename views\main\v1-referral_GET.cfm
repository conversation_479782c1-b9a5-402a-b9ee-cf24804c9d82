<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li><i>search</i> is a structure of subkeys used to limit the referrals returned. Eligible subkeys are:<br/>
			<i>calldatefrom</i> - call date on or after this date<br/>
			<i>calldateto</i> - call date on or before this date<br/>
			<i>calluid</i> - the unique identifer of the call<br/>
			<i>clientfirstname</i> - client's first name<br/>
			<i>clientemail</i> - client's email<br/>
			<i>clientlastname</i> - client's last name<br/>
			<i>clientphone</i> - client's phone number<br/>
			<i>counselorfirstname</i> - counselor's first name<br/>
			<i>counselorlastname</i> - counselor's last name<br/>
			<i>feediscrepancystatus</i> - the fee discrepancy status<br/>
			<i>hasdocuments</i> - if the referral has documents (true or false)<br/>
			<i>istransferred</i> - if the referral is transferred (true or false)<br/>
			<i>lawyerfirstname</i> - lawyer's first name<br/>
			<i>lawyerlastname</i> - lawyer's last name<br/>
			<i>notefollowupdatefrom</i> - note follow up date on or after this date<br/>
			<i>notefollowupdateto</i> - note follow up date on or before this date<br/>
			<i>notefollowupstatus</i> - the follow up status of note (either pending or completed)<br/>
			<i>refdatefrom</i> - referral date on or after this date<br/>
			<i>refdateto</i> - referral date on or before this date<br/>
			<i>referralnum</i> - the system generated referral number<br/>
			<i>repfirstname</i> - representative's first name<br/>
			<i>replastname</i> - representative's last name<br/>
			<i>status</i> - the referral status. Use statusname from GET /referral/status for the valid options.<br/>
			<i>transferdatefrom</i> - transfer date on or after this date<br/>
			<i>transferdateto</i> - transfer date on or before this date<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/referral HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 29

{
    "count": 5,
    "start": 0,
    "search": {
        "refdateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>"
    }
}
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/referral HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 97

{
    "count": 5,
    "start": 10,
    "search": {
        "clientfirstname": "Michael",
        "clientlastname": "Smith",
        "email": "<EMAIL>",
        "lawyerfirstname": "John",
        "lawyerlastname": "Doe",
        "repfirstname": "David",
        "replastname": "Johnson",
        "status": "Open- Retained",
        "counselorfirstname": "William",
        "counselorlastname": "Davis",
        "refdatefrom": "<cfoutput>#dateFormat(now(),"m/d/yyyy")#</cfoutput>",
        "refdateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
        "calldatefrom": "<cfoutput>#dateFormat(now(),"m/d/yyyy")#</cfoutput>",
        "calldateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
        "notefollowupdatefrom": "<cfoutput>#dateFormat(now(),"m/d/yyyy")#</cfoutput>",
        "notefollowupdateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
        "notefollowupstatus": "Pending",
        "referralnum": "4010752",
        "calluid": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
        "phonenumber": "************",
        "feediscrepancystatus": "Confirmed Discrepancy",
        "istransferred": "1",
        "transferdatefrom": "<cfoutput>#dateFormat(now(),"m/d/yyyy")#</cfoutput>",
        "transferdateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
        "hasdocuments": "1"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 5,
        "referrals": [
            {
                "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri": "/v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-referraladmin-uri": "https://yourassociation.org/[path-to-referral]",
                "status": "Open - Retained",
                "clientfirstname": "Michael",
                "clientlastname": "Smith",
                "clientreferraldate": "<cfoutput>#dateFormat(DateAdd("d",-1,now()),"m/d/yyyy")#</cfoutput>",
                "lawyername": "John Doe",
                "membernumber": "SAMPLE123456",
                "islawyerreferral": 1,
                "isdeleted": 0
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>