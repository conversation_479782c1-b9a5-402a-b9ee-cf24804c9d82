<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/webhook/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/webhook/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>	
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
        <tr><td class="rc">404 WEBHOOK NOT FOUND</td><td>invalid webhook api_id</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "webhook": {
            "payload-uri": "http://www.mywebhookreceiver.com",
            "sqsqueue": "ABC123",
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/webhook/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "event": [
                "membercreate": {
                    "enabled": true
                },
                "memberupdate": {
                    "enabled": true
                },
                "membermerge": {
                    "enabled": false
                },
                "memberdelete": {
                    "enabled": false
                },
                "membergroupchange": {
                    "enabled": true,
                    "group": [
                        {
                            "group": "Public"
                            "groupcode": "Public",
                            "grouppath": "Public",
                            "description": "Visitors that are not logged in",
                            "systemgroup": 1,
                            "alertifpopulated": 0,
                            "manualassignments": 0,
                            "protected": 0,
                            "membercount": 133,
                            "badgebackgroundcolor": "#3b3e66",
                            "badgetextcolor": "#ffffff",
                            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                            "x-api-uri": "/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                            "parentapi_id": "",
                            "x-parent-api-uri": ""
                        },
                        ...
                    ]
                },
                "subscriptionstatuschange": {
                    "enabled": false,
                    "status": [
                        {
                        	"status": "Active, Billed",
                        	"subscription": "Type: Membership;"
                        },
                        ...
                    ]
                }
            ]
        } 
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
    <div class="jsonblock-head">Sample Response (failure)</div>
    <div class="jsonblock">
<pre class="prettyprint">
404 WEBHOOK NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Webhook not found."
    ]
}
</pre>
    </div>
</div>
<div style="clear:both;padding-top:10px;"></div>