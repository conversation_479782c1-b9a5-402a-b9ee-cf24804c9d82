﻿<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral/{api_id}/counselornote</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>note</i> - note description (required)<br/>
			<i>createddate</i> - created date and time (required)<br/>
			<i>followupdate</i> - follow up date (optional, this should be a future date)<br/>
			<i>followupstatus</i> - Pending or Completed (optional)<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/counselornote HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{
    "note": "Discuss case with Bob.",
    "createddate": "<cfoutput>#dateTimeFormat(now(),"m/d/yyyy h:mm a")#</cfoutput>",
    "followupdate": "<cfoutput>#dateFormat(DateAdd("d",3,now()),"m/d/yyyy")#</cfoutput>",
    "followupstatus": "Pending"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">201 CREATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 REFERRAL NOT FOUND</td><td>invalid referral api_id</td></tr>
			<tr><td class="rc">500 NOT CREATED</td><td>Unable to add counselor note.</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "count": 2,
        "counselornotes": [
            {
                "enteredby": "John Doe",
                "createddate": "<cfoutput>#dateTimeFormat(now(),"m/d/yyyy h:mm a")#</cfoutput>",
                "followupstatus": "Pending",
                "followupdate": "<cfoutput>#dateFormat(DateAdd("d",3,now()),"m/d/yyyy")#</cfoutput>",
                "description": "Discuss case with Bob."
            }
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to add counselor note.",
        "Invalid or missing note"
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>