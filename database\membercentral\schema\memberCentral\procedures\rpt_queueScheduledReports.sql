ALTER PROC dbo.rpt_queueScheduledReports

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @2MinFromNowDate datetime = DATEADD(MINUTE, 2, GETDATE()), @srItemID int;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='scheduledReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	
	IF OBJECT_ID('tempdb..#tmpReportsForQueue') IS NOT NULL 
		DROP TABLE #tmpReportsForQueue;
	CREATE TABLE #tmpReportsForQueue (srItemID int PRIMARY KEY, reportID int, reportStatus char(1));

	INSERT INTO #tmpReportsForQueue (srItemID, reportID, reportStatus)
	SELECT sch.itemID, sch.reportID, sr.[status]
	FROM dbo.rpt_scheduledReports as sch
	INNER JOIN dbo.rpt_SavedReports as sr ON sr.reportID = sch.reportID
	WHERE sch.isRunning = 0
	AND sch.nextRunDate <= @2MinFromNowDate;

	-- Queue only active reports for immediate execution
	INSERT INTO platformQueue.dbo.queue_scheduledReport (srItemID, reportID, resetByMemberID, isReset, statusID, dateAdded, dateUpdated)
	SELECT tmp.srItemID, tmp.reportID, null, 0, @statusReady, GETDATE(), GETDATE()
	FROM #tmpReportsForQueue as tmp
	WHERE tmp.reportStatus = 'A'
	AND NOT EXISTS (
		SELECT itemID
		FROM platformQueue.dbo.queue_scheduledReport
		where srItemID = tmp.srItemID
	);

	IF @@ROWCOUNT > 0
		EXEC dbo.sched_resumeTask @name='Process Scheduled Report Queue', @engine='MCLuceeLinux';

	-- advance dates for any inactive reports
	SELECT @srItemID = min(srItemID) FROM #tmpReportsForQueue WHERE reportStatus = 'I';
	WHILE @srItemID IS NOT NULL BEGIN
		EXEC dbo.rpt_setScheduledReportNextRunDate @srItemID=@srItemID;
		SELECT @srItemID = min(srItemID) FROM #tmpReportsForQueue WHERE reportStatus = 'I' and srItemID > @srItemID;
	END

	IF OBJECT_ID('tempdb..#tmpReportsForQueue') IS NOT NULL 
		DROP TABLE #tmpReportsForQueue;
    RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
