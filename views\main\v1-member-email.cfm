﻿<a name="v1-member-email"></a>
<section id="v1-member-email">
	<h3>/member/email</h3>
	<p>
		GET - Returns the member's email addresses<br/>
		PUT - Updates a member's email address<br/>
		DELETE - Removes a member's email address<br/>
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/email</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-info">
			Each email listed in the response will have the following data:
			<ul>
				<li><i>type</i> - name of the email type</li>
				<li><i>email</i> - the member's email for this email type</li>
				<li><i>api_id</i> - unique identifer of the email type</li>
				<li><i>x-api-uri</i> - the URI of the member email type</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/email HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 2,
        "email": [
            {
                "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri":"/v1/member/SAMPLE123456/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "type":"Email",
                "email":"<EMAIL>"
            },
            {
                "api_id":"YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "x-api-uri":"/v1/member/SAMPLE123456/email/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "type":"Alternate Email",
                "email":"<EMAIL>"
            }
         ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/email/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			If the member has an email defined for this email type, the email listed in the response will have the same data as the /member/email GET call.<br/>
			If the member does not have an email defined for this email type, the response will be a 404 Member Email Not Found.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 EMAIL TYPE NOT FOUND</td><td>invalid email api_id</td></tr>
			<tr><td class="rc">404 MEMBER EMAIL NOT FOUND</td><td>member does not have an email for this email type</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "email": {
            "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri":"/v1/member/SAMPLE123456/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "type":"Email",
            "email":"<EMAIL>"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER EMAIL NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member Email not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put method-example">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/email/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Required Request Details</div>
		<div class="jsonblock-info">
			The request must contain a JSON object in the body with one key, <i>email</i>.<br/>
			<div style="margin-left:30px;">
				<i>email</i> is the member's email address for this email type.
			</div>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 56

{
    "email": "<EMAIL>"
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>no changes to process</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 EMAIL TYPE NOT FOUND</td><td>invalid email api_id</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error updating email</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Alternate Email <NAME_EMAIL> to <EMAIL>",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (no changes)</div>
		<div class="jsonblock">
<pre class="prettyprint">
202 NOT UPDATED

{
    "data": {
        "result": "No changes to process.",
        "report": [
            "summary": [
                "1 data change was rejected and ignored.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [
                "LastName, NewFirstName (SAMPLE123456) - Alternate Email is not valid: samplealternateemail@org"
            ],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": []
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update member.",
        ...
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="delete method-example">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/email/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/member/SAMPLE123456/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>member did not have an email for this email type</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 EMAIL TYPE NOT FOUND</td><td>invalid email api_id</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error deleting email</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Alternate Email <NAME_EMAIL> to [blank]",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>