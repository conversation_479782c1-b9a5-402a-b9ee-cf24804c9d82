<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li><i>datelastupdated</i> is a date (m/d/yyyy) or datetime in 24-hour format (m/d/yyyy HH:mm) you'd like to limit members to. The default action is to not consider the date the member was last updated. This date is US Central Time.</li>
		<li>
			<i>search</i> is an object of subkeys used to limit the members returned.<br/>
			Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		<li>
			<i>result</i> is an array of subkeys that specifies what information to return for each member. Subkeys defined here will be returned in addition to the standard data described below.<br/>
			Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
			If your JSON object contains invalid result subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 61

{
    "count": 5,
    "start": 10,
    "datelastupdated": <cfoutput>"#month(now())#/1/#year(now())# 15:30"</cfoutput>
}
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 146

{
    "count": 5,
    "search": {
        "primary address_city": "Austin",
        "language": "Spanish"
    },
    "result": [
        "email",
        "primary address_state"
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":10,
        "members":[
            {
                "company":"Law Office of Jane Doe",
                "datecreated":"August, 05 2017 09:57:31 -0700",
                "datelastupdated":"August, 09 2017 19:09:36 -0700",
                "firstname":"Jane",
                "lastname":"Doe",
                "membernumber":"SAMPLE123456",
                "mcaccountstatus":"Active",
                "mcaccounttype":"User",
                "mcrecordtype":"Individual",
                "middlename":"",
                "prefix":"Mrs.",
                "professionalsuffix":"",
                "suffix":"",
                "username":"JaneDoe",
                "x-api-uri":"/v1/member/SAMPLE123456",
                "x-photo-uri":"https://www.yourwebsite.org/memberphotos/sample123456.jpg"
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>