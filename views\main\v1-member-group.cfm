<a name="v1-member-group"></a>
<section id="v1-member-group">
	<h3>/member/group</h3>
	<p>
		GET - Returns the member's group assignments<br/>
		PUT - Manually assigns the member to the group<br/>
		DELETE - Removes a member's manual assignment to the group<br/>
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/group</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			Each group listed in the response will have the following data:
			<ul>
				<li><i>group</i> - name of the group</li>
				<li><i>grouppath</i> - full path of the group</li>
				<li><i>description</i> - description of the group</li>
				<li><i>groupcode</i> - group code of the group, if defined</li>
				<li><i>api_id</i> - unique identifer of the group</li>
				<li><i>x-group-api-uri</i> - the URI of the group</li>
				<li><i>x-api-uri</i> - the URI of the group assignment</li>
				<li><i>directmanual</i> - if the member is manually assigned to this group</li>
				<li><i>directvirtual</i> - if the member is assigned to this group via a group assignment rule</li>
				<li><i>indirectmanual</i> - if the member is manually assigned to a lower group in the group path</li>
				<li><i>indirectvirtual</i> - if the member is assigned to a lower group via a group assignment rule</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/group HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":3,
        "group": [
            {
                "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri":"/v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-group-api-uri":"/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "grouppath":"My Test Groups \\ My Test Group A",
                "indirectmanual": 0,
                "directvirtual": 0,
                "indirectvirtual": 0,
                "directmanual": 1,
                "groupcode": "TestA",
                "description": "",
                "group": "My Test Group A"
            },
            {
                "api_id":"YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "x-api-uri":"/v1/member/SAMPLE123456/group/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "x-group-api-uri":"/v1/group/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "grouppath":"All Members and Staff",
                "indirectmanual": 1,
                "directvirtual": 0,
                "indirectvirtual": 0,
                "directmanual": 0,
                "groupcode": "membersstaff",
                "description": "Members and Staff",
                "group": "All Members and Staff"
            },
            {
                "api_id":"ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                "x-api-uri":"/v1/member/SAMPLE123456/group/ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                "x-group-api-uri":"/v1/group/ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                "grouppath":"Spanish Speakers",
                "indirectmanual": 0,
                "directvirtual": 0,
                "indirectvirtual": 1,
                "directmanual": 0,
                "groupcode": "",
                "description": "Members selecting Spanish as their language",
                "group": "Spanish Speakers"
            }
         ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/group/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			The group listed in the response will have the same data as the /member/group GET call.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 GROUP ASSIGNMENT NOT FOUND</td><td>member does not belong to the group</td></tr>
			<tr><td class="rc">404 GROUP NOT FOUND</td><td>invalid group api_id</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "group": {
            "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri":"/v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-group-api-uri":"/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "grouppath":"My Test Groups \\ My Test Group A",
            "indirectmanual": 0,
            "directvirtual": 0,
            "indirectvirtual": 0,
            "directmanual": 1,
            "groupcode": "TestA",
            "description": "",
            "group": "My Test Group A"
            }
        },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put method-example">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/group/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			If the member assignment is successful, the group listed in the response will have the same data as the /member/group GET call.<br/>
			<b>Note:</b> group assignments are cached; the response to this call may indicate "directmanual" is 0 until the cache is refreshed.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 GROUP ASSIGNMENT NOT FOUND</td><td>member does not belong to the group</td></tr>
			<tr><td class="rc">404 GROUP NOT FOUND</td><td>invalid group api_id</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error adding group assignment</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "group": {
            "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri":"/v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-group-api-uri":"/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "grouppath":"My Test Groups \\ My Test Group A",
            "indirectmanual": 0,
            "directvirtual": 0,
            "indirectvirtual": 0,
            "directmanual": 1,
            "groupcode": "TestA",
            "description": "",
            "group": "My Test Group A"
            }
        },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="delete method-example">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/group/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 GROUP ASSIGNMENT NOT FOUND</td><td>member does not belong to the group</td></tr>
			<tr><td class="rc">404 GROUP NOT FOUND</td><td>invalid group api_id</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error removing group assignment</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result":"Member manual group assignment deleted."
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>