<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/list/{listname}/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		The list membership listed in the response will have the same data as the /member/{membernumber}/list/{listname} GET call.
	</div>

	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/list/samplelist/0000000 HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">404 LIST NOT FOUND</td><td>invalid list name</td></tr>
		<tr><td class="rc">404 LIST MEMBERSHIP NOT FOUND</td><td>member has no membership for this list</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid api_id</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "list": {
            "listname": "samplelist",
            "membertype": "normal",
            "subtype": "mail",
            "name": "Jane Doe, Esq",
            "email": "<EMAIL>",
            "lockname": 1,
            "lockaddress": 0,
            "keepactive": 0,
            "api_id": 0000000,
            "x-api-uri": "/v1/member/SAMPLE123456/list/samplelist/0000000"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 LIST MEMBERSHIP NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "List Membership Not Found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>
