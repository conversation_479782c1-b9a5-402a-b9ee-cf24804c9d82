<cfcomponent output="false">

	<cffunction name="setPageStructure" access="public" returntype="void" output="false">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="siteid" type="string" required="true">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="templateTypeName" type="string" required="true">
		<cfargument name="ovrTemplate" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfif structKeyExists(session,"enableMobile") and session.enableMobile AND arguments.event.getValue('pg','main') NEQ 'admin'>
			<cfset local.enableMobile = 1>
		<cfelse>
			<cfset local.enableMobile = 0>
		</cfif>
		
		<!--- get page structure from db as xml. Pass in optional mode override. --->
		<cfquery name="local.qryPageStructure" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,1,0)#">

			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			exec dbo.cms_getPageStructureByName @siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">, 
				@pageName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">, 
				@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageid#">,
				@ovrMode=<cfif len(arguments.mode)><cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mode#"><cfelse>null</cfif>,
				@processMobileOverrides=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.enableMobile#">,
				@templateTypeName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.templateTypeName#">,
				@ovrTemplate=<cfif len(arguments.ovrTemplate)><cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ovrTemplate#"><cfelse>null</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.xmlPageStructure = XMLParse(local.qryPageStructure.pageStructureXML)>

		<!--- bypass 404 check if we are actually calling the 404 page. This prevents endless loop.  --->
		<!--- system wide 404 check in processPageStructure will handle case when 404 page is undefined/deleted.  --->
		<cfif arguments.pageName eq "404" or (isdefined("local.xmlPageStructure.pageStructure.page.pageZone") and arraylen(local.xmlPageStructure.pageStructure.page.pageZone))>
			<cfset processPageStructure(event=arguments.Event, xmlPageStructure=local.xmlPageStructure, memberID=arguments.memberID)>
		<cfelse>
			<cfset show404page(event=arguments.Event)>
		</cfif>
	</cffunction>

	<cffunction name="setResourcePageStructure" access="public" returntype="void" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="siteid" type="numeric" required="true">
		<cfargument name="resID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<!--- get page structure from db as xml. Pass in optional mode override. --->
		<cfquery name="local.qryPageStructure" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			exec dbo.cms_getCMSResourceByID 
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.resID#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.languageid#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mode#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.xmlPageStructure = XMLParse(local.qryPageStructure.pageStructureXML)>
		<cfif isdefined("local.xmlPageStructure.pageStructure.page.pageZone") and arraylen(local.xmlPageStructure.pageStructure.page.pageZone)>
			<cfset processPageStructure(event=arguments.Event, xmlPageStructure=local.xmlPageStructure, memberID=arguments.memberID)>
		<cfelse>
			<cfset show404page(Event=arguments.Event)>
		</cfif>
	</cffunction>

	<cffunction name="show404page" access="public" returntype="void" output="false">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>

		<cfif arguments.Event.valueExists("mc_pageDefinitionStruct")>
			<cfset arguments.Event.removeValue("mc_pageDefinition")>
		</cfif>
		<cfif arguments.Event.valueExists("mc_pageDefinitionStruct")>
			<cfset arguments.Event.removeValue("mc_pageDefinitionStruct")>
		</cfif>
		<cfif NOT IsDefined("session.cfcuser") OR NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.memberIDToPass = 0>
		<cfelse>
			<cfset local.memberIDToPass = session.cfcuser.memberdata.memberid>
		</cfif>

		<cfif arguments.Event.valueExists("mc_siteinfo")>
			<cfset setPageStructure(Event=arguments.Event, siteid=arguments.event.getValue('mc_siteinfo.siteid'), pageName="404", languageID=1, 
				memberID=local.memberIDToPass, mode="", templateTypeName="Page", ovrTemplate="")>
		</cfif>

		<cfheader statuscode="404" statustext="File not found">
		<cfheader name="X-Robots-Tag" value="noindex">
		<cfif NOT arguments.Event.valueExists("mc_siteinfo")>
			<cfabort>
		</cfif>
	</cffunction>

	<cffunction name="processPageStructure" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="xmlPageStructure" type="XML" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<!--- ------------ --->
			<!--- set metadata --->
			<!--- ------------ --->
			<cfset local.pageinfo = structNew()>
	
			<!--- if page is defined, use xml --->
			<cfif isDefined("arguments.xmlPageStructure.pageStructure.page")>
			
				<!--- use layout orgcode, sitecode, layoutFileName to generate layout path. If orgcode/sitecode are blank, replace with common layout path --->
				<cfif arguments.xmlPageStructure.pageStructure.page.xmlAttributes.templateTypeName eq "Application">
					<cfset local.tmp = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutOrgCode & '/' & arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutSiteCode & '/layouts/apps/' & arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutFileName>
				<cfelse>
					<cfset local.tmp = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutOrgCode & '/' & arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutSiteCode & '/layouts/' & arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutFileName>
				</cfif>
				<cfset local.pageinfo["layout"] = replaceNoCase(local.tmp,"//layouts","common/layouts")>
				<cfset local.pageinfo["layoutMode"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.layoutMode>
				<cfset local.pageinfo["templateID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.templateID>
				<cfset local.pageinfo["templateSiteResourceID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.templateSiteResourceID>
	
				<cfset local.pageinfo["siteID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.siteID>
				<cfset local.pageinfo["siteCode"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.siteCode>
				<cfset local.pageinfo["orgID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.orgID>
				<cfset local.pageinfo["orgCode"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.orgCode>
	
				<cfset local.pageinfo["pageID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageID>
				<cfset local.pageinfo["siteResourceID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageSiteResourceID>
				<cfset local.pageinfo["pageName"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageName>
				<cfset local.pageinfo["pageTitle"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageTitle>
				<cfset local.pageinfo["originalPageTitle"] = local.pageinfo["pageTitle"]>
				<cfset local.pageinfo["pageDescription"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageDesc>
				<cfset local.pageinfo["pageKeywords"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.keywords>
				<cfset local.pageinfo["allowReturnAfterLogin"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.allowReturnAfterLogin>
				<cfset local.pageinfo["pageLanguageID"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageLanguageID>
				<cfset local.pageinfo["pageVisibility"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.isVisible>
				<cfset local.pageinfo["sectionName"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionName>
				<cfset local.pageinfo["sectionCode"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionCode>
				<cfset local.pageinfo["sectionBreadcrumb"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionBreadcrumb>
				<cfset local.pageinfo["pageDirectives"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.pageDirectives>
				<cfset local.pageinfo["dateUnavailable"] = arguments.xmlPageStructure.pageStructure.page.xmlAttributes.dateUnavailable>
				
				<cfif local.pageinfo.pageName neq "admin" and arguments.event.getValue('mc_siteinfo.usesMenuSystem')>
					<cfset local.pageinfo["strMenus"] = application.objCMS.getMenuUsages(siteID=local.pageinfo["siteID"], templateID=local.pageinfo["templateID"], pageID=val(local.pageinfo["pageID"]), languageID=local.pageinfo["pageLanguageID"])>
				<cfelse>
					<cfset local.pageinfo["strMenus"] = structNew()>
				</cfif>
				<cfset local.pageinfo["strTemplateSettings"] = application.objSiteResource.getSiteResourceSettingsStruct(siteResourceID=local.pageinfo["templateSiteResourceID"])>

			<!--- else if page not defined, use defaults --->
			<cfelse>
	
				<!--- get root template for site --->
				<cfquery name="local.qryTemplateFileName" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,2,0)#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select templateFileName
					from dbo.cms_pageTemplates
					where templateID = dbo.fn_getRootTemplateID(<cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.pageinfo["layout"] = "#arguments.event.getValue('mc_siteinfo.orgcode')#/#arguments.event.getValue('mc_siteinfo.sitecode')#/layouts/#local.qryTemplateFileName.templateFileName#">
				<cfset local.pageinfo["layoutMode"] = "Normal">
				<cfset local.pageinfo["pageID"] = "0">
				<cfset local.pageinfo["pageName"] = "unavailable">
				<cfset local.pageinfo["pageTitle"] = "Page Not Found">
				<cfset local.pageinfo["originalPageTitle"] = local.pageinfo["pageTitle"]>
				<cfset local.pageinfo["pageDescription"] = "">
				<cfset local.pageinfo["pageKeywords"] = "">
				<cfset local.pageinfo["allowReturnAfterLogin"] = 0>
				<cfset local.pageinfo["pageLanguageID"] = session.mcstruct.languageid>
				<cfset local.pageinfo["pageVisibility"] = 0>
				<cfset local.pageinfo["sectionName"] = "">
				<cfset local.pageinfo["sectionCode"] = "">
				<cfset local.pageinfo["sectionBreadcrumb"] = "">
				<cfset local.pageinfo["pageDirectives"] = "">
				<cfset local.pageinfo["dateUnavailable"] = "">
				<cfset local.pageinfo["siteCode"] = arguments.event.getValue('mc_siteinfo.sitecode')>
	
			</cfif>
	
			<!--- create the page definition struct if not already done so --->
			<!--- if it doesn't exist, this must be toplevel page ... set main reference --->
			<cfif not arguments.Event.valueExists("mc_pageDefinitionStruct")>
				<cfset local.isTopLevel = true>
				<cfset arguments.Event.setValue("mc_pageDefinitionStruct",structNew())>
				<cfset arguments.Event.setValue("mc_toplevelPageDefinition",local.pageinfo)>
			<cfelse>
				<cfset local.isTopLevel = false>
			</cfif>
			
			<!--- set landingpage --->
			<cfset application.objWebsite.setLandingPage(event=arguments.event)>

			<!--- set pageinfo up to this point into event so the content functions can access it --->
			<!--- any changes to local.pageinfo will also be automatically put into the event by reference --->
			<cfset local.mc_pageDefinitionStruct = arguments.Event.getValue("mc_pageDefinitionStruct")>
			<cfset local.mc_pageDefinitionStruct[local.pageinfo.pageID] = local.pageinfo>
			
			<cfset arguments.Event.setValue("mc_pageDefinition",local.pageinfo)>
			
			<!--- query section tree --->
			<cfif local.pageinfo.pageName neq "admin" and isDefined("arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionID") and isnumeric(arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionID)>
				<cfquery name="local.pageinfo.qrySectionTreeUp" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @startSectionID int, @startSectionDepth int;
					SET @startSectionID = <cfqueryparam value="#arguments.xmlPageStructure.pageStructure.page.xmlAttributes.sectionID#" cfsqltype="CF_SQL_INTEGER">;

					SELECT @startSectionDepth = depth
					FROM dbo.cache_cms_recursivePageSections
					WHERE startSectionID = @startSectionID
					AND sectionID = @startSectionID

					SELECT ps.sectionID, ps.sectionCode, ps.sectionName, ps.parentSectionID, rps.depth - @startSectionDepth as Depth, ps.sectionBreadcrumb
					FROM dbo.cache_cms_recursivePageSections rps
					INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = rps.sectionID
					WHERE rps.startSectionID = @startSectionID
					order by rps.depth desc;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			<cfelse>
				<cfset local.pageinfo.qrySectionTreeUp = QueryNew("sectionID,sectionCode,sectionName,parentSectionID,Depth,sectionBreadcrumb","integer,varchar,varchar,integer,integer,varchar")>
			</cfif>

			<cfif local.isTopLevel>
				<cfset local.pageInfo.pageSlug = "">
				<cfif local.pageinfo.qrySectionTreeUp.recordcount>
					<cfset local.pageSlugPartArray = listToArray(valuelist(local.pageinfo.qrySectionTreeUp.sectionName))>
					<!--- Delete Root Category --->
					<cfset arrayDeleteAt(local.pageSlugPartArray, arrayLen(local.pageSlugPartArray))>
					
					<cfif arrayLen(local.pageSlugPartArray)>
						<cfloop index="local.x" from="1" to="#arrayLen(local.pageSlugPartArray)#" step="1">
							<cfset local.pageInfo.pageSlug = listPrepend(local.pageInfo.pageSlug, local.pageSlugPartArray[local.x], '/')>
						</cfloop>
					</cfif>
					<cfif len(local.pageInfo.pageSlug)>
						<cfset local.pageInfo.pageSlug = '/' & local.pageInfo.pageSlug>
					</cfif>
				</cfif>
				<cfset local.pageInfo.pageSlug = rereplace(lcase(local.pageInfo.pageSlug & '/' & local.pageinfo.pageName),"[^\w_\-/]+","-","all")>
				<cfset local.pageInfo.pageFullTitle = local.pageInfo.pageTitle>
			<cfelse>
				<!--- Update Page Title for passing to Google Analytics. modifying pageslug done by controllers for community and socialnetwork --->
				<cfset local.mc_toplevelPageDefinition = arguments.Event.getValue("mc_toplevelPageDefinition")>
				<cfset local.mc_toplevelPageDefinition.pageFullTitle = local.mc_toplevelPageDefinition.pageFullTitle & " - #local.pageInfo.pageTitle#">
			</cfif>

			<!--- --------- --->
			<!--- set zones --->
			<!--- --------- --->
			<cfset local.pageinfo["pageZones"] = StructNew()>
			<cfset local.pageinfo.pageZones["ToolBar"] = arrayNew(1)>
			<cfset local.pageinfo.pageZones["Main"] = arrayNew(1)>
	
			<!--- if zones are defined, use xml --->
			<cfset local.showNoRights = false>
			<cfset local.canViewSomethingInMainZone = false>
			<cfset local.itemsCheckedInMainZone = 0>
			<cfif structKeyExists(application.objSiteInfo.mc_siteinfo, local.pageinfo.siteCode)>
				<cfset local.SiteCodeToCheckForGroupPrint = local.pageinfo.siteCode>
			<cfelse>
				<cfset local.SiteCodeToCheckForGroupPrint = arguments.event.getValue('mc_siteinfo.sitecode')>
			</cfif>
			<cfif arguments.memberID>
				<cfset local.groupPrintID = application.objMember.getMemberGroupPrintID(memberID=arguments.memberid)>
			<cfelse>
				<cfset local.groupPrintID = application.objSiteInfo.getSiteInfo(local.SiteCodeToCheckForGroupPrint).publicGroupPrintID>
			</cfif>

			<cfif isdefined("arguments.xmlPageStructure.pageStructure.page.pageZone") and arraylen(arguments.xmlPageStructure.pageStructure.page.pageZone)>
				<cfloop from="1" to="#arrayLen(arguments.xmlPageStructure.pageStructure.page.pageZone)#" index="local.zid">
					<cfset local.zoneNode = arguments.xmlPageStructure.pageStructure.page.pageZone[local.zid]>
					<cfset local.pageinfo.pageZones[local.zoneNode.xmlAttributes.zoneName] = []>
					<cfloop from="1" to="#arrayLen(local.zoneNode.siteResource)#" index="local.rid">
						<cfset local.resourceNode = local.zoneNode.siteResource[local.rid]>
						<cfset local.tmpStrRes = {}>
						<cfset local.resourcePerms = {}>
						<cfif structKeyExists(local.resourceNode.xmlAttributes,"viewRightPrintID") and structKeyExists(local.resourceNode.xmlAttributes,"editContentRightPrintID") and val(local.groupPrintID) and val(local.resourceNode.xmlAttributes.viewRightPrintID) and val(local.resourceNode.xmlAttributes.editContentRightPrintID)>
							<cfset local.resourcePerms.view = application.objSiteResource.checkGroupPrintRightPrint(groupPrintID=local.groupPrintID, rightPrintID=local.resourceNode.xmlAttributes.viewRightPrintID)>
							<cfset local.resourcePerms.editContent = application.objSiteResource.checkGroupPrintRightPrint(groupPrintID=local.groupPrintID, rightPrintID=local.resourceNode.xmlAttributes.editContentRightPrintID)>
						<cfelseif structKeyExists(local.resourceNode.xmlAttributes,"siteResourceID")>
							<cfset local.resourcePerms = application.objSiteResource.buildRightAssignments(siteResourceID=local.resourceNode.xmlAttributes.siteResourceID, memberid=arguments.memberid, siteid=local.pageinfo["siteID"])>
						<cfelse>
							<cfset local.resourcePerms = structNew()>
						</cfif>
						
						<cfif local.zoneNode.xmlAttributes.zoneName eq "main">
							<cfset local.itemsCheckedInMainZone = local.itemsCheckedInMainZone + 1>
						</cfif>
						<cfif structKeyExists(local.resourcePerms,"view") and local.resourcePerms.view>
							<cftry>
								<cfif local.zoneNode.xmlAttributes.zoneName eq "main" and not local.canViewSomethingInMainZone>
									<cfset local.canViewSomethingInMainZone = true>
								</cfif>
								<cfswitch expression="#local.resourceNode.xmlAttributes.resourceClass#">
								<cfcase value="staticContent">
									<cfset local.tmpStrRes = getResource_staticContent(event=arguments.event, contentID=local.resourceNode.xmlAttributes.contentID, languageID=session.mcstruct.languageID)>
								</cfcase>
								<cfcase value="application">
									<cfif local.resourceNode.xmlAttributes.appWidgetInstanceID eq 0>
										<cfset local.tmpStrRes = getResource_application(event=arguments.event, appTypeName=local.resourceNode.xmlAttributes.appTypeName, siteResourceID=local.resourceNode.xmlAttributes.siteResourceID, appInstanceID=local.resourceNode.xmlAttributes.appInstanceID)>
									<cfelse>
										<cfset local.tmpStrRes = getResource_applicationWidget(
												event=arguments.event,
												appWidgetTypeName=local.resourceNode.xmlAttributes.appWidgetTypeName,
												appTypeName=local.resourceNode.xmlAttributes.appTypeName,
												siteResourceID=local.resourceNode.xmlAttributes.siteResourceID,
												appInstanceResourceID=local.resourceNode.xmlAttributes.appInstanceResourceID,
												appWidgetInstanceID=local.resourceNode.xmlAttributes.appWidgetInstanceID,
												appInstanceID=local.resourceNode.xmlAttributes.appInstanceID)>
									</cfif>
								</cfcase>
								</cfswitch>
							<cfcatch type="any">
								<cfset local.tmpStrRes = { data=application.objError.showAndSendError(event=arguments.event,cfcatch=cfcatch), view="echo" }>
							</cfcatch>
							</cftry>
							<cftry>
								<cfset local.tmpStrRes.resourceNodeAttributes = local.resourceNode.xmlAttributes>
								<cfset local.tmpStrRes.resourceNodeAttributes["allowed"] = (structKeyExists(local.resourcePerms,"view") and local.resourcePerms.view)> 
								<cfset local.tmpStrRes.resourceNodeAttributes["editContent"] = (structKeyExists(local.resourcePerms,"EditContent") and local.resourcePerms.EditContent)>
								<cfcatch type="any"></cfcatch>
							</cftry>
								
							<cfif structKeyExists(local.tmpStrRes,"data")>
								<cfset ArrayAppend(local.pageinfo.pageZones[local.zoneNode.xmlAttributes.zoneName],local.tmpStrRes)>
							</cfif>
						<cfelse>
							<cfif local.zoneNode.xmlAttributes.zoneName eq "main">
								<cfset local.showNoRights = true>
							</cfif>
						</cfif>
						<cfif arguments.Event.valueExists("mc_trigger404page")>
							<cfbreak>
						</cfif>
					</cfloop>
					<cfif arguments.Event.valueExists("mc_trigger404page")>
						<cfbreak>
					</cfif>
				</cfloop>
				<cfif arguments.Event.valueExists("mc_trigger404page")>
					<cfset arguments.Event.removeValue("mc_trigger404page")>
					<cfset show404page(Event = arguments.Event)>
				<cfelse>
					<cfif not application.objUser.isLoggedIn(cfcuser=session.cfcuser) and local.showNoRights and local.itemsCheckedInMainZone and not local.canViewSomethingInMainZone>
						<cfset local.pageinfo.redirectToLogin = true>
					</cfif>
					<cfif local.showNoRights>
						<!--- set default message --->
						<cfset local.tmpStrRes = { view="echo", data="You do not have permission to view this content." }>
							
						<!--- get norights content --->
						<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) is not 1>
							<cfset local.noRightsContentID = arguments.event.getValue('mc_siteinfo.noRightsNotLoggedInContentID')>
						<cfelse>
							<cfset local.noRightsContentID = arguments.event.getValue('mc_siteinfo.noRightsContentID')>
						</cfif>
						<cfset local.tmpContent = application.objCMS.getStaticContent(local.noRightsContentID,local.pageinfo.pageLanguageID)>
						<cfif len(local.tmpContent.rawContent)>
							<cfset local.tmpStrRes.data = local.tmpContent.rawContent>
						</cfif>
						<cfset ArrayPrepend(local.pageinfo.pageZones["Main"],local.tmpStrRes)>
						<cfif listFindNoCase("stream",local.pageinfo["layoutMode"])>
							<cfset local.pageinfo["layoutMode"] = "Full">
						</cfif>
					</cfif>

					<cfif len(local.pageinfo.pageDirectives)>
						<cfheader name="X-Robots-Tag" value="#local.pageinfo.pageDirectives.replaceAll(',',', ')#">
					</cfif>
					<cfif len(local.pageinfo.dateUnavailable)>
						<cfif local.pageinfo.dateUnavailable gt now()>
							<cfset local.utcDate = CreateObject("component","model.system.platform.tsTimeZone").convertTimeZone(local.pageinfo.dateUnavailable,'US/Central',"UTC")>
							<cfheader name="X-Robots-Tag" value="unavailable_after: #Datetimeformat(DateAdd("d", 1, local.utcDate), "ISO8601","utc")#">
						<cfelse>
							<cfheader name="X-Robots-Tag" value="noindex">
						</cfif>
					</cfif>

				</cfif>
			<!--- else if page not defined, and the call to the 404 page is also undefined --->
			<cfelse>
				<cfset local.pageinfo.pageZones["Main"][1] = get404ContentNode()>
				<cfset local.pageinfo["layoutMode"] = "Full">
				<cfheader statuscode="404" statustext="File not found">
				<cfheader name="X-Robots-Tag" value="noindex">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.SendError(cfcatch=cfcatch,objectToDump=local)>
			<cfrethrow>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="get404ContentNode" access="private" returntype="struct" output="false">
		<cfset var local = structNew()>
		
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.view = "echo">
		<cfsavecontent variable="local.returnStruct.data">
			<cfoutput>
				<div class="tsAppHeading">Page Not Found</div>
				<br/>
				<div class="tsAppBodyText">
					Sorry! This page is not available.
					<br/><br/>
					Go to the <a href="/">Homepage</a> or <a href="/?pg=search">search</a> for the page you were looking for.
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processSubPage" access="public" returntype="string" output="false">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="siteid" type="string" required="true">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<!--- Save the reference to currently active page definition before it is overwritten by this subpage request--->
		<cfif arguments.Event.valueExists("mc_pageDefinition")>
			<cfset local.preexistingPageDefinition = arguments.Event.getValue("mc_pageDefinition")>
		</cfif>
		
		<cfset setPageStructure(Event=arguments.event, siteid=arguments.siteID, pageName=arguments.pageName, languageID=arguments.languageid,
			memberID=arguments.memberid, mode=arguments.mode, templateTypeName='Application', ovrTemplate='')>

 		<cfif arguments.Event.getValue("mc_pageDefinition.layoutmode") eq "stream">
			<cfset arguments.Event.getCollection()['mc_pageDefinition']['layoutmode'] = "stream">
			<cfif isdefined("local.preexistingPageDefinition")>
				<cfset local.preexistingPageDefinition.layoutMode = "stream">
			</cfif>
			<cfset local.appTemplate = "/assets/common/layouts/apps/appStream">
 		<cfelse>
			<cfset local.appTemplate = "/assets/" & arguments.event.getValue('mc_pageDefinition.layout')>
		</cfif>
		<cfsavecontent variable="local.returnHTML">
			<cfinclude template="#local.appTemplate#.cfm">
		</cfsavecontent>

		<cfset arguments.Event.setValue("mc_pageDefinition",local.preexistingPageDefinition)>
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="getResourceInfo" access="public" returntype="query" output="false">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="currentSiteID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<!--- prevent bot driven bad siteResourceID from causing exceptions --->
		<cfif NOT isValid("integer",arguments.siteResourceID) or arguments.siteResourceID lt 0>
			<cfset local.qryResourceInfo = QueryNew("resourceType")>
		<cfelse>
			<!--- Needs to be expanded to make sure that resource owning site is in same network as current site --->
			<cfquery name="local.qryResourceInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select sr.*, srt.resourceType, srs.siteResourceStatusDesc, s.siteID, s.siteCode, o.orgID, o.orgCode
				from dbo.cms_siteResources sr
				inner join dbo.cms_siteResourceTypes srt on sr.resourceTypeID = srt.resourceTypeID
					and sr.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
				inner join dbo.cms_siteResourceStatuses srs on sr.siteResourceStatusID = srs.siteResourceStatusID
				inner join dbo.sites s on sr.siteID = s.siteID
				inner join dbo.organizations o on s.orgid = o.orgID

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>
		
		<cfreturn local.qryResourceInfo>
	</cffunction>
	
	<cffunction name="getResource_staticContent" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="contentID" type="numeric" required="yes">
		<cfargument name="languageID" type="numeric" required="yes">

		<cfset var local = structNew()>
	
		<cfquery name="local.qryPageStructure" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select contentID, contentTitle, contentDesc, rawContent, languageID, contentLanguageID, contentVersionID
			from dbo.fn_getContent(<cfqueryparam value="#arguments.contentID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.languageID#" cfsqltype="CF_SQL_INTEGER">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.tmpContent = StructNew()>
		<cfset local.tmpContent["view"] = "echo">
		<cfset local.tmpContent["data"] = local.qryPageStructure.rawContent>
		<cfset local.tmpContent.contentAttributes = structNew()>
		<cfset local.tmpContent.contentAttributes.languageID = local.qryPageStructure.languageID>
		<cfset local.tmpContent.contentAttributes.contentID = local.qryPageStructure.contentID>
		<cfset local.tmpContent.contentAttributes.contentLanguageID = local.qryPageStructure.contentLanguageID>
		<cfset local.tmpContent.contentAttributes.contentTitle = local.qryPageStructure.contentTitle>
		<cfset local.tmpContent.contentAttributes.contentDesc = local.qryPageStructure.contentDesc>
		<cfset local.tmpContent.contentAttributes.contentVersionID = local.qryPageStructure.contentVersionID>
		
		<cfreturn local.tmpContent>
	</cffunction>

	<cffunction name="getResource_application" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="appTypeName" type="string" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="appInstanceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.pageinfo = structNew()>
		
		<!--- run app's default event --->
		<cfset local.strApp = CreateObject("component","model.#arguments.appTypeName#.#arguments.appTypeName#").runDefaultEvent(event=arguments.event, appInstanceID=arguments.appInstanceID, siteResourceID=arguments.siteResourceID)>

		<cfreturn local.strApp>
	</cffunction>

	<cffunction name="getResource_applicationWidget" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any">
		<cfargument name="appWidgetTypeName" type="string" required="yes">
		<cfargument name="appTypeName" type="string" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="appInstanceResourceID" type="numeric" required="yes">
		<cfargument name="appWidgetInstanceID" type="numeric" required="yes">
		<cfargument name="appInstanceID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.pageinfo = structNew()>
		
		<!--- run app's default event --->
		<cfset local.strApp = CreateObject("component","model.#arguments.appTypeName#.#arguments.appWidgetTypeName#").runDefaultWidgetEvent(arguments.event,arguments.appInstanceID,arguments.appInstanceResourceID,arguments.appWidgetInstanceID,arguments.siteResourceID)>

		<cfreturn local.strApp>
	</cffunction>

	<cffunction name="decryptMemberKey" access="public" output="false" returntype="struct">
		<cfargument name="k" type="string" required="yes">
		<cfreturn deserializeJSON(decrypt(arguments.k,"M@!6T$", "CFMX_COMPAT", "Hex"))>
	</cffunction>

	<cffunction name="qualifyAllLinks" access="public" output="no" returntype="string">
		<cfargument name="content" type="string" required="true">
		<cfargument name="siteid" type="numeric" required="true">
		<cfargument name="qualURL" type="string" required="false" default="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/">

		<cfscript>
			var local = {};
			local.siteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.siteID);
			local.orgID = application.objSiteInfo.getSiteInfo(local.siteCode).orgID;
			local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.orgID);

			local.hostnameRegex = getQualifyAllLinksRegex(siteID=arguments.siteID);

			//regex to strip any leading or trailing whitespace inside the href quotes
			local.whitespaceURLRegex = "(?:(<\s*(?:a|img)\b[^>]*?\b(?:(?:href|src)))\s*=\s*([""'])\s*([^""']*?)\s*([""']))";

			// support standards compliant relative links (begin with /) and also malformed relative links (anything that doesn't start with a slash, pound sign, bracket, or protocol)
			local.relativeURLRegex = "(?:(<\s*(?:a|img)\b[^>]*?\b(?:(?:href|src))\s*=\s*[""'])(?!(?:##|\[|\w+:(?://)?))/?)((?:[^""']*)(?:[""']))";

			local.newContent = replaceTemporaryAWSLinks(content=arguments.content);
			local.newContent = local.newContent.rereplacenocase(local.whitespaceURLRegex,"\1=\2\3\4","all").replacenocase('/index.cfm','/','all');
			local.newContent = local.newContent.rereplacenocase(local.hostnameRegex,"\1#arguments.qualURL#\2","all").replacenocase('/index.cfm','/','all');
			local.newContent = local.newContent.rereplacenocase(local.relativeURLRegex,"\1#arguments.qualURL#\2","all").replacenocase('/index.cfm','/','all');
			local.newContent = local.newContent.rereplacenocase('([?&]|&amp;)mk=(?!\[\[memberkey\]\])[^&"'' ]*\s*(?=[&"'' ])','\1mk=[[memberkey]]','all');

			// strip protocol from url merge codes
			local.urlMergeCodeList = "subRenewUrl|loginurl|loginlink|manageCreditCardsURL|manageCreditCardsLink|consentListManagementURL|emailOptOutURL";
			local.urlMergeCodeList &= "|taskProspectURL|taskProjectURL|updateURL|updatelink|organizationWebsite|evEventURL";
			for (local.row in local.qryOrgWebsites) { 	
				local.urlMergeCodeList &= "|" & local.row.websiteType;	
			}
			local.stripProtocolFromMergeTagRegex = "https?://(\[\[(?:#local.urlMergeCodeList#)\]\])";
			local.newContent = local.newContent.rereplacenocase(local.stripProtocolFromMergeTagRegex,"\1","all");
			
			return local.newContent;
		</cfscript>
	</cffunction>

	<cffunction name="getQualifyAllLinksRegex" access="private" output="no" returntype="string">
		<cfargument name="siteid" type="numeric" required="true">

		<cfset var local = {}>

		<cfquery name="local.qryHostnamesForRegex" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			SET XACT_ABORT, NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			BEGIN TRY
	
				declare @siteID int, @orgcode varchar(10), @sitecode varchar(10);
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select @orgcode = o.orgcode, @sitecode = s.sitecode
				from dbo.sites as s
				inner join dbo.organizations as o on o.orgID = s.orgID
				where s.siteID = @siteID;
			
				select hostNameList = replace(STRING_AGG(hostname,'|'),'.','\.')
				from (
					select hostname
					from dbo.sitehostnames
					where siteid = @siteID
						union
					select @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mcinternal).com'
						union
					select @orgcode + '.' + @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mcinternal).com'
						union
					select @sitecode + '.(?:membercentral|mccdn).org'
						union
					select @sitecode + '.(?:[A-Za-z0-9]+).(?:membercentral|mccdn).org'
				) as tmp;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<!--- Also support malformed links where the protocol is missing --->
		<cfset local.regex = "(?:(<\s*(?:a|img)\b[^>]*?\b(?:(?:href|src))\s*=\s*[""'])(?:/|(?:https?://)?(?:#local.qryHostnamesForRegex.hostnamelist#)))\b(?:$|/)((?:[^""']*)(?:[""']))">
	
		<cfreturn local.regEx>
	</cffunction>

	<cffunction name="replaceTemporaryAWSLinks" access="public" output="no" returntype="string">
		<cfargument name="content" type="string" required="true">

		<cfscript>
			var local = {};
			local.content = arguments.content;
			local.regex = '(?:<\s*(?:a|img)\b[^>]*?\b(?:href|src)\s*=\s*["''])((?:https?://)?s3\.amazonaws\.com/membercentralcdn/sitedocuments/([^/]+)/([^/]+)/([^/]+)/([^/]+\.[a-zA-Z0-9]+)([^"'']*))(?:["''])';

			local.arrMatches = reMatchNoCase(local.regex, local.content);

			for (local.currentMatch in local.arrMatches) {
				local.strCapturedGroups = reFindNoCase(local.regex, local.currentMatch, 1, true);

				if (structKeyExists(local.strCapturedGroups, "len") && arrayLen(local.strCapturedGroups.len) GT 6) {
					local.thisFullURL = mid(local.currentMatch, local.strCapturedGroups.pos[2], local.strCapturedGroups.len[2]);
					local.thisOrgCode = mid(local.currentMatch, local.strCapturedGroups.pos[3], local.strCapturedGroups.len[3]);
					local.thisSiteCode = mid(local.currentMatch, local.strCapturedGroups.pos[4], local.strCapturedGroups.len[4]);
					local.thisFilename = mid(local.currentMatch, local.strCapturedGroups.pos[6], local.strCapturedGroups.len[6]); // extract filename.extension
					local.thisDocumentVersionID = listFirst(local.thisFilename, ".");

					local.qryDocument = queryExecute("
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT docs.documentID
						FROM dbo.cms_documentVersions as v
						INNER JOIN dbo.cms_documentLanguages as l on l.documentLanguageID = v.documentLanguageID
						INNER JOIN dbo.cms_documents as docs on docs.documentID = l.documentID
						INNER JOIN dbo.sites as s ON s.siteID = docs.siteID
							AND s.siteCode = :siteCode
						INNER JOIN dbo.organizations as o ON o.orgID = s.orgID
							AND o.orgCode = :orgCode
						WHERE v.isActive = 1
						AND v.documentVersionID = :documentVersionID;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						", 
						{ 
							orgCode = { value=local.thisOrgCode, cfsqltype="cf_sql_varchar" },
							siteCode = { value=local.thisSiteCode, cfsqltype="cf_sql_varchar" },
							documentVersionID = { value=val(local.thisDocumentVersionID), cfsqltype="cf_sql_integer" }
						},
						{ datasource=application.dsn.memberCentral.dsn }
					);

					if(local.qryDocument.recordCount){
						local.content = replaceNoCase(local.content, local.thisFullURL, '/docDownload/' & local.qryDocument.documentID, 'all');
					}
				}
			}

			return local.content;
		</cfscript>
	</cffunction>

</cfcomponent>