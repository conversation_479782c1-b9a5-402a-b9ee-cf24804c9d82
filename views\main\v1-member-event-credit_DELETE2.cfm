<div class="delete method-example">
	<div class="method-wrapper">
		<div class="method">DELETE</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/event/{api_id}/{registrant_id}/credit/{authoritycode}_{typecode}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/123456/credit/CAStateBar_General HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">404 EVENT NOT FOUND</td><td>invalid event api_id</td></tr>
		<tr><td class="rc">404 EVENT REGISTRATION NOT FOUND</td><td>member is not registered for this event</td></tr>
		<tr><td class="rc">404 EVENT REGISTRATION CREDIT NOT FOUND</td><td>member has not been awarded credit for this event</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error deleting registration credit</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result":"Credits removed from event registration."
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 EVENT REGISTRATION NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Event registration not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>