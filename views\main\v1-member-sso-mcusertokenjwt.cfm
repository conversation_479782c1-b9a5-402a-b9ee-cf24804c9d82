﻿<a name="v1-member-sso-mcusertokenjwt"></a>
<section id="v1-member-sso-mcusertokenjwt">
	<h3>/member/sso/mcusertokenjwt</h3>
	<p>POST - Returns an JWT for MemberCentral's MCUserToken for a membernumber.</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post method-example">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/sso/mcusertokenjwt</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			The response is a JWT payload encoded by the secret set at <a href="#v1-organization-sso-mcusertokensecret">/organization/sso/mcusertokensecret</a>.<br/>
			It contains:
			<ul>
				<li><i>iat</i> - Issued At. The time the token was generated in the number of seconds since UNIX epoch.</li>
				<li><i>jti</i> - JSON Web Token ID. A unique id for the token.</li>
				<li><i>sitecode</i> - MemberCentral's unique code for your website.</li>
				<li><i>membernumber</i> - The member's MemberNumber.</li>
			</ul>
			HS256 is the JWT algorithm used in the header of the JWT payload. HS256 stands for HMAC SHA 256, a 256-bit encryption algorithm designed by the US National Security Agency.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/sso/mcusertokenjwt HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 TOKEN NOT FOUND</td><td>invalid sso token</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "mcusertokenjwt":"MEMBERCENTRAL_JWT"
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>			

	<div class="delete">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>