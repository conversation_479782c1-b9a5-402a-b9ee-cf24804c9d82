function rptHideAlert() { mca_hideAlert('err_report'); };
function rptShowAlert(msg) {
	mca_showAlert('err_report', msg, true);
	try { enableButtonBar(); } catch(e) { }
}

function valStep1() {
	rptHideAlert();
	if ($('#frmRN').val() == '') {
		rptShowAlert('Enter a name for the new report.');
		return false;
	}
	return true;
}

function editReportName() {
	$('#editRptNameBtn').addClass('d-none');
	$('#editRptNameContainer').removeClass('d-none');
}
function saveReportName(rptid) {
	let saveRptNameResult = function(r) {
		$('#btnRenameReport').prop('disabled',false).text('Save');
		if (r.success && r.success.toLowerCase() == 'true') {
			cancelEditReportName();
			$('.app-breadcrumb ol.breadcrumb li.breadcrumb-item:last,.rptName').html(rptName);
			document.title = 'Control Panel: '+rptName;
		} else alert('We were unable to update report name.');
	};

	let rptName = $('#rptName').val().trim();
	if (!rptName.length) {
		$('#rptName').addClass('is-invalid').focus();
		return false;
	} else {
		$('#rptName').removeClass('is-invalid');
		$('#btnRenameReport').prop('disabled',true).text('Saving...');
		let objParams = { reportID:rptid, reportName:rptName };
		TS_AJX('ADMREPORTS','updateReportName',objParams,saveRptNameResult,saveRptNameResult,10000,saveRptNameResult);
	}
}
function cancelEditReportName() {
	$('#editRptNameContainer').addClass('d-none');
	$('#editRptNameBtn').removeClass('d-none');
}
function copyReport(rptid,tt) {
	top.location.href = mcrpt_link_copyreport + '&rptID=' + rptid + '&toolType=' + tt;
}
function markReportAsEditable(rptid) {
	let markResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			self.location.href = mcrpt_link_showreport;
		} else {
			alert('We were unable to mark this report as editable.');
			$('#markReportEditableBtn').prop('disabled', false);
		}
	};
	$('#markReportEditableBtn').prop('disabled', true);
	let objParams = { reportID:rptid };
	TS_AJX('ADMREPORTS','markReportAsEditable',objParams,markResult,markResult,10000,markResult);
}
function loadReport(rptid) {
	top.location.href = top.location.href + '&rptID=' + rptid;
}
function reloadSavedReports() {
	savedReportsTable.ajax.reload();
}
function reloadScheduledReports(rptid,hideModal) {
	let getSchedRptsResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') populateScheduledReports(r);
		else $('#schedRptsContainer').html('<div class="alert alert-warning mb-2">We were unable to load the scheduled reports.</div>');
	};

	$('#schedRptsContainer').html(mca_getLoadingHTML()).removeClass('d-none');
	if (hideModal) MCModalUtils.hideModal();
	let objParams = { reportID:rptid };
	TS_AJX('ADMREPORTS','getScheduledReports',objParams,getSchedRptsResult,getSchedRptsResult,10000,getSchedRptsResult);
}
function populateScheduledReports(r) {
	let schedRptListSource = $('#mc_schedRptList').html();
	let schedRptListTemplate = Handlebars.compile(schedRptListSource);
	$('#schedRptsContainer').html(schedRptListTemplate(r));
	mcActivateTooltip($('#schedRptsContainer'));
}
function editScheduledReport(itemID) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: (itemID==0?'Schedule Delivery of Report':'Modify Scheduled Delivery of Report'),
		iframe: true,
		contenturl: mcrpt_link_schedrpt+'&itemID='+itemID,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'saveScheduledReport',
			extrabuttonlabel: 'Save',
		}
	});
}
function saveScheduledReport() {
	$('#MCModalBodyIframe')[0].contentWindow.validateAndSaveScheduledReport();
}
function deleteScheduledReport(itemid,rptid) {
	let deleteResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			if (r.reload && r.reload == 1) self.location.href = mcrpt_link_showreport;
			else reloadScheduledReports(rptid,true);
		} else {
			delBtn.prop('disabled',false).html(delMode == 'savedreports' ? '<i class="fa-light fa-calendar-alt mr-2"></i>Delete' : 'Delete Schedule');
			alert('We were unable to delete this schedule.');
		}
	};

	let delBtn = $('#btnDeleteSchedReport_'+itemid).length ? $('#btnDeleteSchedReport_'+itemid) : $('#delSchedRpt_'+itemid);
	let delMode = delBtn.data('usagemode');
	mca_initConfirmButton(
		delBtn,
		function(){
			let objParams = { itemID:itemid };
			TS_AJX('SAVEDREPORT','deleteScheduledReport',objParams,deleteResult,deleteResult,10000,deleteResult);
		},
		delMode == 'savedreports' ? '<i class="fa-light fa-calendar-alt mr-2"></i>Delete' : 'Delete Schedule',
		delMode == 'savedreports' ? '<span class="text-warning text-nowrap"><i class="fa-solid fa-info-circle"></i> Click to Confirm</span>' : '<i class="fa-solid fa-info-circle"></i> Click to Confirm',
		delMode == 'savedreports' ? '<span class="text-info text-nowrap"><i class="fa-solid fa-info-circle"></i> Deleting...</span>' : 'Deleting...'
	);
}
function setupRptFilterDateRange(f,t) {
	mca_setupDatePickerRangeFields(f,t);
}
function clearReportDates(el) {
	el.parent().children('input.reportdate:enabled').val('').change();
}
function clearDateRangeFields(f,t) {
	$('#'+f).val('');
	$('#'+t).val('').change();
	return false;
}
function clearDateField(d) {
	$('#'+d).val('');
	return false;
}

function disableReportSettingsForReadOnly() {
	$('.stepDIV input, .stepDIV select, .stepDIV textarea, .stepDIV button[name=btnSelectMember], .stepDIV .btn-clear-dates').prop('disabled', true);
	$('.rptmulti').find('input').each(function(){
		$(this).prop('disabled', true);
	});
	$('.ui-multiselect-all').closest('li').remove();
	$('.ui-multiselect-none').closest('li').remove();
	$('.ui-multiselect-close').closest('li').remove();
	$('.stepDIV span#spanGroupSelect, .stepDIV span#spanGroupClear, .stepDIV span#spanMemberSelect, .stepDIV span#spanMemberClear').html('');
}
function viewReportRuleVersionsList(rID,rName){
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Rule Versions (' + rName + ')',
		iframe: true,
		contenturl: mcrpt_link_listreportruleversions,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true
		}
	});
}
function reloadReport(){
	self.location.href = mcrpt_link_showreport;
}

/* BUTTON BAR */
function disableButtonBar() { $('#reportDefs button.rptButtonBar').attr('disabled','disabled'); }
function enableButtonBar() { $('#reportDefs button.rptButtonBar').removeAttr('disabled'); }

function rpt_download(u) {
	loadiframe();
	window.setTimeout(rpt_onCompleteLoading, 1000);
	top.location.href='/tsdd/' + u;
}
function runreport(r) { 
	$('#reportDefs #reportAction').val(r);
	return true;
}
function loadiframe() {
	$('#divReportShowScreenLoading').hide();
	enableButtonBar();
}
function rpt_onCompleteLoading() {
	if ((typeof mcrpt_ruleid != 'undefined') && mcrpt_ruleid && typeof window['acceptRuleVersion_'+mcrpt_ruleid] == 'function') window['acceptRuleVersion_'+mcrpt_ruleid]();
	$('#divReportShowScreen').show(); 
	updateReportActionBar();
	$('html,body').animate({scrollTop: $('#divReportShowScreen').offset().top-150},200);
}
function bb_loadScreenReport() {
	$('#divReportShowScreen').load(
		mcrpt_link_runreport + '&' + $('#frmReport').serialize() + ' #screenreport', 
		function(response, status, xhr) { 
			$('#divReportShowScreenLoading').hide();
			if (status == 'error')
				rptShowAlert('There was an error producing the screen report.');
			else if ($('#divReportShowScreen').html().length == 0) {
				var jrerr = '';
				if (response.length > 0) {
					try { var jr = jQuery.parseJSON(response); jrerr = (typeof jr =='object' ? jr.err : ''); } catch(jre) { }
				}
				rptShowAlert('There was an error producing the screen report.' + (jrerr.length > 0 ? '<br/><br/>' + jrerr : ''));
			} else {
				enableButtonBar();
				rpt_onCompleteLoading();
			}
		} 
	);
}
function bb_loadCustomCSVReport() {
	$('#divReportShowScreen').load(
		mcrpt_link_csvsettings + '&' + $('#frmReport').serialize() + ' #csvsettings', 
		function(response, status, xhr) { 
			$('#divReportShowScreenLoading').hide();
			if (status == 'error')
				rptShowAlert('There was an error preparing the CSV report settings.');
			else if ($('#divReportShowScreen').html().length == 0) {
				var jrerr = '';
				if (response.length > 0) {
					try { var jr = jQuery.parseJSON(response); jrerr = (typeof jr =='object' ? jr.err : ''); } catch(jre) { }
				}
				rptShowAlert('There was an error preparing the CSV report settings.' + (jrerr.length > 0 ? '<br/><br/>' + jrerr : ''));
			} else {
				try {
					if (response.indexOf('name="autogencsv"') > 0)
						doBCPtoCSVByPassSave();
					else {
						initialLoadCustomCSV();
						rpt_onCompleteLoading();
					}
				} catch(err) {
					rptShowAlert('There was an error preparing the CSV report settings.');
				}
			}
		} 
	);
}
function bb_loadOutputToScreenReport() {
	$('#divReportShowScreen').load(
		mcrpt_link_runreport + '&' + $('#frmReport').serialize() + ' #screenreport', 
		function(response, status, xhr) { 
			$('#divReportShowScreenLoading').hide();
			if (status == 'error')
				rptShowAlert('There was an error preparing the PDF report.');
			else if ($('#divReportShowScreen').html().length == 0) {
				var jrerr = '';
				if (response.length > 0) {
					try { var jr = jQuery.parseJSON(response); jrerr = (typeof jr =='object' ? jr.err : ''); } catch(jre) { }
				}
				rptShowAlert('There was an error preparing the PDF report.' + (jrerr.length > 0 ? '<br/><br/>' + jrerr : ''));
			} else {
				enableButtonBar();
				rpt_onCompleteLoading();
			}
		} 
	);
}
function prepButtonBar(vf) {
	$('#reportDefs').after('<div style="text-align:center;display:none;" id="divReportShowScreenLoading">'+ mca_getLoadingHTML('Please wait while we prepare your report.') +'</div><div id="divReportShowScreen" style="display:none;"></div>');
	
	$('#frmReport').submit(function() {
		disableButtonBar();
		rptHideAlert();

		if (typeof vf === 'function' && !vf()) return false;
		if (!validateRollingDates(false)) return false;

		$('#divReportShowScreenLoading').hide();
		$('#divReportShowScreen').html('').hide();
		$('#divReportShowScreenLoading').show();
		$('html,body').animate({scrollTop: $('#divReportShowScreenLoading').offset().top-150},200);

		var raval = $('#reportDefs #reportAction').val();
		if (raval == 'screen') {
			window.setTimeout("bb_loadScreenReport()", 500);
			return false;
		} else if (raval == 'customcsv') {
			window.setTimeout("bb_loadCustomCSVReport()", 500);
			return false;
		} else if (raval == 'outputToScreen') {
			$('#reportDefs #reportAction').val('pdf');
			window.setTimeout("bb_loadOutputToScreenReport()", 500);
			return false;
		} else {
			$('#divReportShowScreen').html('<iframe width="1" height="1" src="' + mcrpt_link_runreport + '&' + $('#frmReport').serialize() + '" onload="top.loadiframe();"></iframe>');
			return false;
		}
	});
}

/* CUSTOM CSV FUNCTIONS */
function returnToReportFromCSVSettings() {
	$('#divReportShowScreen').html('').hide();
	enableButtonBar();
}
function validateCSVSettings() {
	var fldlabels = [];
	var allOK = true;
	$('input.mc_rpt_csv_fld').each(function() {
		if (!$(this).prop('disabled')) {
			if ($.inArray(this.value, fldlabels) > -1) {
				allOK = false;
				return false;
			} else {
				fldlabels.push(this.value);
			}
		}
	});
	if (!allOK) {
		mca_showAlert('err_report_csvsettings', 'You have duplicate CSV column names. Each CSV column must have a unique name.');
		return false;
	} else return true;
}
function saveCSVsettings() {
	mca_hideAlert('err_report_csvsettings');
	if (validateCSVSettings()) {
		try {
			var neworder = [], newsortorder = [], objSort;
			$('div#csvsettings tbody#tbodyFieldsIn tr.csv_in_row input.mc_rpt_csv_fld').each(function() { 
				neworder.push($(this).prop('name').split('_')[1]); 
			});
			$('div#csvsettings input#neworder').val(neworder.join());

			$('div#csvsettings table#csvFieldsSortTable tbody tr.csv_in_row').each(function() { 
				if ($(this).find('select.sortfld').val() != '' && $(this).find('select.sortfld').val() != null) {
					objSort = { field:$(this).find('select.sortfld').val(), dir:$(this).find('select.sortdir').val() };
					newsortorder.push(objSort); 
				}
			});
			$('div#csvsettings input#newsortorder').val(JSON.stringify(newsortorder));

			$('#divReportShowScreen').hide();
			$('#divReportShowScreenLoading').show();

			window.setTimeout("doSaveCSVsettings()", 200);
			return false;
		} catch(err) {
			$('#divReportShowScreen').show();
			$('#divReportShowScreenLoading').hide();
			mca_showAlert('err_report_csvsettings', 'There was a problem saving and generating the CSV report.');
		}
	}
}	
function writeReportRunStatement(rc,lrd,lrb,as) {
	let stmt = '';
	if (rc > 0) 
		stmt = 'Report run <b>' + rc + '</b> time' + (rc > 1 ? 's' : '') + ', most recently on <b>' + lrd + '</b> by <b>' + lrb + '</b>.';
	else {
		stmt = 'This report has not been run yet.';
		if (as) 
			stmt += ' You will be able to schedule this report after it is run for the first time.';
	}
	$('#divRptActionBarRunStatement').html(stmt);
}
function updateReportActionBar() {
	let updateReportActionBarResult = function(r) {
		if (r.data.runcount > 0)
			r.data.lastrundate = new Date(Date.parse(r.data.lastrundate)).toLocaleDateString();
		writeReportRunStatement(r.data.runcount,r.data.lastrundate,r.data.lastrunby,mcrpt_allowsch);
		if(mcrpt_allowsch && $('#schedRptsContainer').hasClass('d-none'))
			$('#schedRptsContainer').removeClass('d-none');
	};
	var objParams = { rptId:mcrpt_rptid, toolTypeID:mcrpt_ttid, siteResourceID:mcrpt_srid };
	TS_AJX('ADMREPORTS','getReportInfo',objParams,updateReportActionBarResult,updateReportActionBarResult,20000,updateReportActionBarResult);
}
function doSaveCSVsettings() {
	try {
		$.post(mcrpt_link_csvsavesettings, $('#frmCSVSettings').serialize()).done(
			function(data, status, xhr) { 
				try {
					if (status == 'success' && $.parseJSON(data).success == true) {
						$('#divReportShowScreen').html('<iframe width=1 height=1 src=\"' + mcrpt_link_runreportcustomcsv + '&bcp=' + $('#frmCSVSettings input#bcpfilename').val() + '&rptlgi=' + $('#frmCSVSettings input#rptlgi').val() + '\" onload=\"top.loadiframe();\"></iframe>');
						updateReportActionBar();
						return false;
					} else {
						$('#divReportShowScreenLoading').hide();
						rptShowAlert('There was a problem saving and generating the CSV report.');
					}
				}
				catch(e) {
					$('#divReportShowScreenLoading').hide();
					rptShowAlert('There was a problem saving and generating the CSV report.');
				}
			}).fail(function(xhr, status, error) {
				$('#divReportShowScreenLoading').hide();
				rptShowAlert('There was a problem saving and generating the CSV report.');
			});
	} catch(err) {
		$('#divReportShowScreen').show();
		$('#divReportShowScreenLoading').hide();
		mca_showAlert('err_report_csvsettings', 'There was a problem saving and generating the CSV report.');
	}
}
function doBCPtoCSVByPassSave() {
	$('#divReportShowScreen').html('<iframe width=1 height=1 src=\"' + mcrpt_link_runreportcustomcsv + '&bcp=' + $('#frmCSVSettings input#bcpfilename').val() + '&rptlgi=' + $('#frmCSVSettings input#rptlgi').val() + '\" onload=\"top.loadiframe();\"></iframe>');
}

function removeCSVField(el) {
	var outerdiv = $('div#csvsettings'); 
	var row = el.closest('tr').remove().clone().removeClass('csv_in_row').addClass('csv_ex_row').addClass('nodrag');
	row.find('input.mc_rpt_csv_fld').prop("disabled",true);
	row.find('span.spanHandle, i.fa-circle-minus').addClass('d-none');
	row.find('i.fa-circle-plus').removeClass('d-none').off('click').click(function() { restoreCSVField($(this)); return false; });
	outerdiv.find('tbody#tbodyFieldsEx').append(row);
	if (outerdiv.find('tbody#tbodyFieldsIn tr.csv_in_row').length == 0) {
		restoreAllCSVFields();
	} else {
		outerdiv.find('tbody#tbodyFieldsMiddle, tbody#tbodyFieldsEx').show();
	}
}
function restoreCSVField(el,bypass) {
	bypass = (typeof bypass === 'undefined') ? '0' : bypass;
	var outerdiv = $('div#csvsettings');
	var row = el.closest('tr').remove().clone().removeClass('csv_ex_row').removeClass('nodrag').addClass('csv_in_row');
	row.find('input.mc_rpt_csv_fld').prop("disabled",false);
	row.find('span.spanHandle').removeClass('d-none');
	row.find('i.fa-circle-plus').addClass('d-none');
	row.find('i.fa-circle-minus').removeClass('d-none').off('click').click(function() { removeCSVField($(this)); return false; });
	row.find('input.mc_rpt_csv_fld').off('blur').blur(function() { cleanCSVLabel($(this)); return false; });
	outerdiv.find('tbody#tbodyFieldsIn').append(row);
	if (bypass == 0 && outerdiv.find('tbody#tbodyFieldsEx tr.csv_ex_row').length == 0) {
		outerdiv.find('tbody#tbodyFieldsMiddle, tbody#tbodyFieldsEx').hide();
		resetCSVDragDrop();
	}
}
function cleanCSVLabel(el) {
	var theVal = el.val();
	var newVal = $.trim(theVal.replace(/[^a-zA-Z0-9 \!\@\#\$\%\^\&\*\(\)\_\-\=\+\\\~\:\\.\/\?]/g,'').replace(/  +/g,' '));
	if (theVal != newVal) el.val(newVal);
	if (newVal == '') {
		var origFieldLabel = el.closest('td').prev().attr('data-origfield');
		el.val(origFieldLabel);
	}
}
function restoreAllCSVFields() {
	var outerdiv = $('div#csvsettings');
	outerdiv.find('tbody#tbodyFieldsEx tr.csv_ex_row').each(function() { restoreCSVField($(this),1); });
	outerdiv.find('tbody#tbodyFieldsMiddle, tbody#tbodyFieldsEx').hide();
	resetCSVDragDrop();
}
function resetCSVFieldIcons() {
	var outerdiv = $('div#csvsettings');
	outerdiv.find('table#csvFieldsTable i.fa-circle-minus').off('click').click(function() { removeCSVField($(this)); return false; });
	outerdiv.find('table#csvFieldsTable tbody#tbodyFieldsIn tr.csv_in_row input.mc_rpt_csv_fld').off('blur').blur(function() { cleanCSVLabel($(this)); return false; });
	outerdiv.find('table#csvFieldsTable i.fa-circle-plus').off('click').click(function() { restoreCSVField($(this)); return false; });
}
function resetCSVSortOrder() {
	var jsonStr = $('div#csvsettings input#jsonFields').val();
	var arrCSVSortFields = [];
	$.each($.parseJSON(jsonStr), function(idx, obj) {
		arrCSVSortFields.push({ text:obj.FIELD, value:obj.FIELD });
	});
	arrCSVSortFields.sort(function(a,b) {
		return a.text.localeCompare(b.text);
	});
	tempselCSVSort = $('<select>');
	tempselCSVSort.append('<option value=""></option>');
	$.each(arrCSVSortFields, function(i) {
		tempselCSVSort.append('<option value="' + arrCSVSortFields[i].value + '">' + arrCSVSortFields[i].text + '</option>');
	});
}
function writeSortOption(f,d) {
	f = (typeof f === 'undefined') ? '' : f;
	var tbl = $('div#csvsettings table#csvFieldsSortTable');
	var fieldnum = (tbl.find('tbody > tr.csv_in_row').length || 0) + 1;
	var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
		var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
		return v.toString(16);
	});
	tbl.append('<tr class="csv_in_row"><td class="grabTD"><i class="fa-solid fa-bars fa-lg" title="Hold and drag to move column"></i> &nbsp;<span class="sortdispnum">'+fieldnum+'</span></td><td><select id="csvsortfld_'+uuid+'" class="sortfld form-control form-control-sm" name="csvsortfld_'+uuid+'" onChange="removeIfSortSetToBlank($(this));addSortOptionIfNeeded()"></select></td><td><select id="csvsortdir_'+uuid+'" name="csvsortdir_'+uuid+'" class="sortdir form-control form-control-sm"><option value="asc">Ascending</option><option value="desc">Descending</option></select></td><td><i class="fa-solid fa-circle-minus fa-lg" title="Remove Column from Sort" onClick="removeCSVSortField($(this));"></i></td></tr>');
	tempselCSVSort.clone().children().appendTo('#csvsortfld_'+uuid);
	if (f.length > 0) {
		$('#csvsortfld_'+uuid).val(f);
		$('#csvsortdir_'+uuid).val(d);
	} else {
		$('#csvsortfld_'+uuid).val('');
		$('#csvsortdir_'+uuid).val('asc');
	}
}
function writeInitialSortOptions() {
	var jsonStr = $('div#csvsettings input#jsonSortfields').val();
	$.each($.parseJSON(jsonStr), function(idx, obj) {
		writeSortOption(obj.FIELD, obj.DIR);
	});
	writeSortOption();
}
function removeIfSortSetToBlank(sel) {
	if (sel.val() == '') {
		removeCSVSortField(sel);
	}
}
function reorderSortOptions(tbody) {
	tbody.find('tr').each(function() {
		count = $(this).parent().children().index($(this)) + 1;
		$(this).find('.sortdispnum').html(count);
	});
}
function addSortOptionIfNeeded() {
	var numblank = 0;
	$('div#csvsettings table#csvFieldsSortTable tr.csv_in_row select.sortfld').each(function() { 
		if ($(this).val() == '') { numblank = 1; return false; }
	});
	if (!numblank) writeSortOption();
}
function removeCSVSortField(el) {
	var tbody = el.closest('tbody');
	el.closest('tr').remove();
	reorderSortOptions(tbody);
	addSortOptionIfNeeded();
}
function resetCSVDragDrop() {
	$('#csvFieldsTable').rowSorter({
		handler: 'tr > td.grabTD',
		limitTbody: 'tbody#tbodyFieldsIn'
	});
	$('#csvFieldsSortTable').rowSorter({
		handler: 'tr > td.grabTD',
		onDrop: function(tbody, row, new_index, old_index) { 
			reorderSortOptions($(tbody));
			addSortOptionIfNeeded();
		}
	});
}
function initialLoadCustomCSV() {
	resetCSVFieldIcons();
	resetCSVDragDrop();
	resetCSVSortOrder();
	$('div#csvsettings input.mc_rpt_csv_fld').each(function() { 
		cleanCSVLabel($(this));
	});
	writeInitialSortOptions();
	mca_initNavPills('csvSettingsPills');
}

/* Rolling Dates */
function writeRollDates() {
	var handleResponse = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			$('div#divStepRollDates').show();

			var tempsel = $('<select>');
			tempsel.append('<option value="0">No Advance</option>');
			$.each(r.qryaf, function(i) {
				tempsel.append('<option value="' + r.qryaf[i].afid + '">' + r.qryaf[i].afname + '</option>');
			});

			var sectionTemplate = '<div class="row mt-3"><div class="col-sm-12">{{content}}</div></div>';
			var sectionRowTemplate = '\
				<div class="form-group row">\
					<label for="{{labelfor}}" class="col-md-4 col-sm-12 col-form-label">{{label}}</label>\
					<div class="col-md-8 col-sm-12"><div class="input-group input-group-sm">{{content}}</div></div>\
				</div>';
			var rowControls = '';
			var onchangeFunctions = '';
			var resultRows = '';

			$('input.rolldate').each(function() {
				var controlID = 'roll_' + $(this).attr('name') + '_afid';
				onchangeFunctions = "saveRDAFID();updateAFExample('"+ $(this).attr('name') +"','"+ controlID +"','div"+ controlID +"')";
				rowControls = '<select class="rollafid form-control form-control-sm" name="'+ controlID +'" id="'+ controlID +'" onchange="'+ onchangeFunctions +'"></select> <div id="div'+ controlID +'" class="ml-2" style="min-width:100px;">&lt;No Advance&gt;</div>';
				resultRows += sectionRowTemplate.replace("{{label}}", $(this).attr('mcrdtxt'))
								.replace("{{labelfor}}", controlID)
								.replace("{{content}}", rowControls);
			});

			onchangeFunctions = "saveRDAFID();updateAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');";
			
			rowControls = '<input type="text" name="roll_adv" id="roll_adv" value="" class="form-control form-control-sm dateControl" style="max-width:150px;" onchange="'+ onchangeFunctions +'">\
							<div class="input-group-append"><span class="input-group-text cursor-pointer calendar-button" onclick="$(\'#roll_adv.dateControl\').focus();"><i class="fa-solid fa-calendar"></i></span></div>\
							<button type="button" class="btn btn-pill btn-secondary btn-sm ml-2 py-0" onclick="clearRollADVDate();">clear</button>';
			resultRows += sectionRowTemplate.replace("{{label}}", "When should we next advance these dates?")
							.replace("{{labelfor}}", "roll_adv")
							.replace("{{content}}", rowControls);
			
			rowControls = '<select class="rollafid form-control form-control-sm" name="roll_adv_afid" id="roll_adv_afid" onchange="'+ onchangeFunctions +'"></select> <div id="divroll_adv_afid" class="ml-2" style="min-width:100px;">&lt;No Advance&gt;</div>';
			resultRows += sectionRowTemplate.replace("{{label}}", "How should we advance this date?")
							.replace("{{labelfor}}", "roll_adv_afid")
							.replace("{{content}}", rowControls);

			var rolltbl = sectionTemplate.replace("{{content}}", resultRows);
			$('div#rolldateYes').html(rolltbl);

			var rollingDates = false;
			var roll_adv = '';
			var roll_afid = '';

			$('input.rolldate').each(function() {
				var mcrdfld = $(this).attr('name');
				roll_afid = '';
				for (var x=0; x<objRptExtra.length; x++) {
					if (objRptExtra[x].key.toLowerCase() == mcrdfld.toLowerCase()) { roll_afid = objRptExtra[x].afid; break; }
				}
				if (roll_afid.length > 0) rollingDates = true;
				tempsel.clone().children().appendTo('#roll_' + mcrdfld + '_afid');
				if (roll_afid.length == 0) $('#roll_' + mcrdfld + '_afid')[0].selectedIndex = 0;
				else $('#roll_' + mcrdfld + '_afid option[value="' + roll_afid + '"]').attr('selected', 'selected'); 
				updateAFExample($(this).attr('name'),'roll_' + mcrdfld + '_afid','divroll_' + mcrdfld + '_afid')
			});

			roll_afid = '';
			for (var x=0; x<objRptExtra.length; x++) {
				if (objRptExtra[x].key.toLowerCase() == 'afrundate') { roll_adv = objRptExtra[x].value; roll_afid = objRptExtra[x].afid; break; }
			}
			if (roll_adv.length > 0 || roll_afid.length > 0) rollingDates = true;
			$('#roll_adv').val(roll_adv);
			tempsel.clone().children().appendTo('#roll_adv_afid');
			if (roll_afid.length == 0) $('#roll_adv_afid')[0].selectedIndex = 0;
			else $('#roll_adv_afid option[value="' + roll_afid + '"]').attr('selected', 'selected'); 
			updateAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');

			if (rollingDates) {
				$("#rolldate1").prop("checked", true);
				rollDateRdo(1);
			}

			mca_setupDatePickerField('roll_adv');
		} else { 
			handleResponseFail();
		}
	};
	var handleResponseFail = function(r) {
		alert('There was a problem loading the advancement formulas. Try again.'); 
	};

	TS_AJX_SYNC('VIRTUALGROUPS','getAdvanceFormula',{ fororg:0 },handleResponse,handleResponseFail,10000,handleResponseFail);
}
function rollDateRdo(v) {
	if (v == 1) $('#rolldateYes').show();
	else {
		$('#rolldateYes').hide();
		$('select.rollafid').val('0').trigger('change');
		$('#roll_adv').val('').trigger('change');
	}
}
function updateAFExample(dt,af,txt) {
	var jtxt = $('#'+txt);
	var dtVal = $('#'+dt).val();
	var afVal = $('#'+af).val();

	var chkDateExResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') jtxt.html(r.retdate);
		else jtxt.html('Unable to calculate date.');
	};

	if (afVal == '0' || dtVal == '') {
		jtxt.html('&lt;No Advance&gt;');
	} else {
		jtxt.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Calculating...');

		var objParams = { baseDate:dtVal, afid:afVal };
		TS_AJX('ADMADVFORM','getAdvanceFormulaDateforAFID',objParams,chkDateExResult,chkDateExResult,10000,chkDateExResult);
	}
}
function clearRollADVDate() {
	$('#roll_adv:enabled').val('');
	updateAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');
}

function saveRDAFID() {
	if (validateRollingDates(true)) {
		var svarea = $('#RDAFID_sv');
		disableButtonBar();
		svarea.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Saving...').show().load(
			mcrpt_link_savereportextra, 
			$('#frmReport').serializeArray(),
			function(response, status, xhr) { 
				$('#svarea').hide();
				enableButtonBar();
				if (status == 'error') {
					rptShowAlert('There was an error saving the report.');
				}
			} 
		);
	}
}

function validateRollingDates(supress) {
	var vrdResult = false;

	if ($('input[name=rolldate]:checked').val() == '1') {
		// date must be entered
		if ($('#roll_adv').val().length == 0) {
			if (!supress) rptShowAlert('To automatically advance dates, choose a valid date of advancement.'); 
			vrdResult=false;
		// date's afid must be selected
		} else if ($('#roll_adv_afid').val() == 0) {
			if (!supress) rptShowAlert('To automatically advance dates, choose an advance formula for the date of advancement.'); 
			vrdResult=false;
		} else {
			vrdResult=true;
		}

		$('input.rolldate').each(function() {
			var mcrdfld = $(this).attr('name');
			if($('#roll_'+mcrdfld+'_afid').val() > 0 && $(this).val() == '') {
				if (!supress) rptShowAlert("To automatically advance dates, choose a valid " + $(this).attr('mcrdtxt')+ ".");
				vrdResult=false;
			}
		});
	} else {
		vrdResult=true;
	}

	return vrdResult;
}

/*to check if both dates are entered or not*/
function isCompleteRange(start, end) {
	return (start && end) || (!start && !end);
}

/* to check if range shouldn't exceed 1 year*/
function isWithinOneYear(start, end) {
	if (!start || !end) return false; 
	var startDate = new Date(start);
	var endDate = new Date(end);					
	var oneYearLater = new Date(startDate);
	oneYearLater.setFullYear(startDate.getFullYear() + 1);

	/* End date must be equal to or later than one year from the start*/
	return endDate <= oneYearLater;
}