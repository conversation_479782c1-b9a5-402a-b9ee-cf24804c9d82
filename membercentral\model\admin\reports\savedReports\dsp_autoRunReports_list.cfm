<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		let scheduledReportsTable;

		function editScheduledReport(itemID,rptID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Modify Scheduled Delivery of Report',
				iframe: true,
				contenturl: '#local.editScheduledReportLink#&itemID='+itemID+'&rptid='+rptID,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: 'saveScheduledReport',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function saveScheduledReport() {
			$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveScheduledReport();
		}
		function deleteScheduledReport(itemid) {
			var deleteResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadScheduledReports();
				} else {
					delBtn.prop('disabled',false).html(delMode == 'schedrpttool' ? '<i class="fa-solid fa-trash-can"></i>' : 'Delete Schedule');
					alert('We were unable to delete this schedule.');
				}
			};

			let delBtn = $('##btnDeleteSchedReport_'+itemid).length ? $('##btnDeleteSchedReport_'+itemid) : $('##delSchedRpt_'+itemid);
			let delMode = delBtn.data('usagemode');
			mca_initConfirmButton(
				delBtn,
				function(){
					let objParams = { itemID:itemid };
					TS_AJX('SAVEDREPORT','deleteScheduledReport',objParams,deleteResult,deleteResult,10000,deleteResult);
				},
				delMode == 'schedrpttool' ? '<i class="fa-solid fa-trash-can"></i>' : 'Delete Schedule',
				'<i class="fa-solid fa-circle-info"></i> <small>Click to Confirm</small>',
				'<i class="fa-solid fa-circle-info"></i> Deleting...'
			);
		}
		function reloadScheduledReports() {
			scheduledReportsTable.draw(false);
			MCModalUtils.hideModal();
		}

		function initScheduledReportsTable() {
			scheduledReportsTable = $('##scheduledReportsList').DataTable({
				"processing": true,
				"serverSide": true,
				"ajax": { 
					"url": "#local.scheduledReportsLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ 
						"data": null, 
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="'+data.reporturl+'" target="_blank">'+data.reportname+'<i class="ml-2 fa-solid fa-up-right-from-square" title="Report opens a new window"></i></a><div class="ml-3"><span class="badge badge-info mr-2">'+data.reportaction+'</span>'+data.reporttype+'</div><div class="ml-3">'+data.toemail+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "60%"
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							var untilGTNow = 0;
							var endrundate = data.endrundate;
							if (endrundate.length > 0) {
								var endDate = new Date(endrundate);
								var currentDate = new Date();
								if (endDate > currentDate) {
									untilGTNow = 1;
								}
								endrundate = endrundate.split(' ')[0];
							}
						
							if (type === 'display') {
								if (data.reportstatus.toLowerCase() != "a") {
									renderData += '<div class="mb-2"><span class="badge badge-warning mr-2">Inactive Report</span><b>Run will be skipped.</b></div>';
									renderData += '<div style="text-decoration: line-through;">'+data.nextrundate+'</div>';
								} else {
									renderData += '<div>'+data.nextrundate+'</div>';
								}
								if (endrundate.length > 0 && untilGTNow == 1) 
									renderData += '<div><b>Every '+(data.interval == 1 ? '' : data.interval+' ')+data.intervaltypename+' until '+endrundate+'</b></div>'; 
								else if (endrundate.length > 0) 
									renderData += '<div><b>Every '+(data.interval == 1 ? '' : data.interval+' ')+data.intervaltypename+'; Ended on '+endrundate+'</b></div>'; 
								else 
									renderData += '<div><b>Every '+(data.interval == 1 ? '' : data.interval+' ')+data.intervaltypename+'</b></div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "22%" 
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? '<a href="##" class="btn btn-sm btn-outline-primary px-2 m-1" onclick="editScheduledReport('+data.itemid+','+data.reportid+');return false;" title="Modify Scheduled Delivery of Report"><i class="fa-solid fa-pencil"></i></a><a href="##" id="delSchedRpt_'+data.itemid+'" class="btn btn-sm btn-outline-danger px-2 m-1" onclick="deleteScheduledReport('+data.itemid+');return false;" title="Delete Scheduled Delivery of Report" data-usagemode="schedrpttool" data-confirm="0"><i class="fa-solid fa-trash-can"></i></a>' : data;
						},
						"className": "text-center text-nowrap",
						"orderable": false,
						"width": "18%" }
					
				],
				"order": [[0, 'asc']]
			});
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="divManageAutoRunReports">
	<table id="scheduledReportsList" class="table table-sm table-striped table-bordered" style="width:100%">
		<thead>
			<tr>
				<th>Report</th>
				<th>Next Run</th>
				<th>Actions</th>
			</tr>
		</thead>
	</table>
</div>
</cfoutput>