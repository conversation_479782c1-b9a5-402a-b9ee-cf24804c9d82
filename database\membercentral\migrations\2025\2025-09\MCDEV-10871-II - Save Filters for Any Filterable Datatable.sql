USE membercentral
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @adminViewRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES ('saveToolFiltersData', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='TSCOMMON',
		@requestCFC='application.objCommon',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO
