<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/list/{listname}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>email</i> - e-mail address of the list membership (required)<br/>
			<i>name</i> - member's name on the list (required)<br/>
			<i>lockname</i> - 1 or 0, if the Lock Name flag to be set for this list membership (optional, defaults to 0)<br/>
			<i>lockaddress</i> - 1 or 0, if the Lock Address flag to be set for this list membership (optional, defaults to 0)<br/>
			<i>keepactive</i> - 1 or 0, if the Keep Active flag to be set for this list membership (optional, defaults to 0)<br/>
			<i>membertype</i> - member type of the list membership (required). Valid values are:<br/>
			<p>
				<ul>
					<li><b>normal:</b> An active member who may receive mail from the list.</li>
					<li><b>confirm:</b> The person has requested to join this mailing list, but has not confirmed the subscription request by replying or clicking on a confirmation message. Once the member has confirmed membership, the person's status will change to normal.</li>
					<li><b>private:</b> Awaiting admin approval.</li>
					<li><b>expired:</b> Subscription has expired.</li>
					<li><b>held:</b> An undeliverable member. ListManager failed to deliver mail to this address the number of times specified for the list . A user may become "unheld" by replying to a held notification</li>
					<li><b>unsub:</b> The member has opted out of the list. The member will not receive email, but is retained in the database for statistical reasons or in case the member decides to resubscribe.</li>
					<li><b>referred:</b> Referred by a friend and awaiting confirmation.</li>
					<li><b>needs-confirm:</b> The member will receive a message asking them to confirm membership, and then their status will be set to Confirm. Administrators may use this status when creating members to allow them to confirm their memberships.</li>
					<li><b>needs-hello:</b> The member will receive a message welcoming them to the list, and then their status will be Normal. Administrators may use this status when creating members to notify them they have been added.</li>
					<li><b>needs-goodbye:</b> The member will receive a message informing them they have left the list, and their status will be Unsubscribed. Administrators may use this status when unsubscribing members to notify them they have been removed.</li>
				</ul>
			</p>
			<i>subtype</i> - subscription type of the list membership (required). Valid values are:<br/>
			<p>
				<ul>
					<li><b>mail:</b> As soon as someone contributes a message to the mailing list, the user will receive a copy of that message.</li>
					<li><b>digest:</b> Each night, the user will receive a single email message containing all the messages contributed to the mailing list that day in plain-text; all HTML and attachments will be stripped. At the top of the message will be a numbered list of the subjects in that digest, followed by the complete messages themselves. Digest recipients will not receive messages sent to segments.</li>
					<li><b>index:</b> Each night, the user will receive a single email message containing all the subject lines of all the messages contributed to the mailing list that day. If any of the messages interest the user, the bottom of the index gives the email command that will retrieve the bodies of the messages.</li>
					<li><b>mimedigest:</b> The same as a digest, but in MIME format so that the individual messages' formatting is preserved. Digest recipients will not receive messages sent to segments.</li>
					<li><b>nomail:</b> No email is sent to the user. The user is free to go the web interface whenever they want, and read the full text of the messages there. This setting is useful for when members go on vacation and want to stop receiving mail.</li>
				</ul>
			</p>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/list/samplelist HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{
    "email": "<EMAIL>",
    "name": "Jane Doe, Esq",
    "membertype": "normal",
    "subtype": "mail"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">201 CREATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 LIST NOT FOUND</td><td>invalid list name</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>unable to add list membership</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "count":1,
        "list": {
            "listname": "samplelist",
            "membertype": "normal",
            "subtype": "mail",
            "name": "Jane Doe, Esq",
            "email": "<EMAIL>",
            "lockname": 0,
            "lockaddress": 0,
            "keepactive": 0,
            "api_id": 0000000,
            "x-api-uri": "/v1/member/SAMPLE123456/list/samplelist/0000000"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to add list membership."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>