<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/subscription/{subscriber_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		The subscription listed in the response will have the same data as the /member/subscription GET call.
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/subscription/123456 HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">404 MEMBER SUBSCRIPTION NOT FOUND</td><td>member does not have subscription</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid subscriber_id</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "subscription": {
            "type": "Membership",
            "type_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "subscription": "Annual Membership",
            "subscription_api_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
            "rate": "Less than 5 years",
            "rate_api_id": "ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
            "frequency": "Full",
            "frequency_api_id": "WWWWWWWW-WWWW-WWWW-WWWW-WWWWWWWWWWWW",
            "status": "Expired",
            "activationstatus": "Activation Requirement Met",
            "startdate": "08/22/2011",
            "enddate": "12/31/2011",
            "graceenddate": "03/31/2012",
            "subscriber_id": "123456",
            "parent_subscriber_id": "",
            "x-parent-api-uri": "",
            "x-api-uri": "/v1/member/SAMPLE123456/subscription/123456",
            "x-renew-uri": ""
        } 
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>