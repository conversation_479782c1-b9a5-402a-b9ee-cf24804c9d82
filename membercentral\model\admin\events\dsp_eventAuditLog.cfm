<cfsavecontent variable="local.auditLogsJS">
	<cfoutput>
	<script type="text/javascript">
		let eventAuditLogsTable;
		function initEventAuditLogTable() {
			eventAuditLogsTable = $('##eventAuditLogsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"searching": false,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": {
					"url": "#local.auditLogLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmAuditLogsFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							return type === 'display' ? '<div class="font-weight-bold">'+data.date+' CT</div><div class="font-italic">'+data.actor+'</div>' : data;
						},
						"width": "20%",
						"className": "align-top"
					},
					{ "data": "description", "width": "80%", "className": "align-top" }
				],
				"ordering": false
			});
		}
		function filterEventAuditLogs() {
			if ($('##divFilterAuditLogsForm').hasClass('d-none')) {
				$('##divFilterAuditLogsForm').removeClass('d-none');
			}
		}
		function doFilterEventAuditLogs() {
			eventAuditLogsTable.draw();
		}
		function clearEventAuditLogFilters() { 
			$('##frmAuditLogsFilter')[0].reset();
			doFilterEventAuditLogs(); 
		}
		$(function() {
			let oneWeekAgo = new Date();
			oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
			$('##fDateFrom').val((oneWeekAgo.getMonth() + 1) + '/' + oneWeekAgo.getDate() + '/' + oneWeekAgo.getFullYear());
			$('##fDateTo').val((new Date().getMonth() + 1) + '/' + new Date().getDate() + '/' + new Date().getFullYear());
			
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupCalendarIcons('frmAuditLogsFilter');
			initEventAuditLogTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.auditLogsJS)#">

<cfoutput>
<h5>Event Audit Log</h5>
<div class="toolButtonBar">
	<div><a href="javascript:filterEventAuditLogs();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter audit log."><i class="fa-regular fa-filter"></i> Filter Audit Log</a></div>
</div>
<div id="divFilterAuditLogsForm" class="d-none">
	<form name="frmAuditLogsFilter" id="frmAuditLogsFilter" onsubmit="doFilterEventAuditLogs();return false;">
		<div class="card card-box mb-3">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Filter Event Audit Log
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="form-row">
					<div class="col-6 pr-4">
						<div class="form-group">
							<div class="form-label-group mb-0 d-flex align-items-center">
								<input type="text" name="fDescription" id="fDescription" value="" class="form-control">
								<label for="fDescription">Description</label>
								<i class="fa-solid fa-circle-question text-primary ml-1" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Enter keywords to locate specific audit log entries."></i>
							</div>
						</div>
					</div>
					<div class="col-6">
						<div class="form-row">
							<div class="col">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fDateFrom" id="fDateFrom" value="" class="form-control dateControl" autocomplete="off" >
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fDateFrom"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fDateFrom">Date From</label>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fDateTo" id="fDateTo" value="" class="form-control dateControl" autocomplete="off" >
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fDateTo"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fDateTo">Date To</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="form-row">
					<div class="col-3">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fAreaCode" id="fAreaCode" class="form-control">
									<option value="">All Areas</option>
									<option value="EVENT" selected>Events</option>
									<option value="EVSETUP">Settings</option>
									<option value="EVTICKET">Event Tickets</option>
									<option value="EVRATE">Event Rates</option>
									<option value="EVCREDIT">Event Credits</option>
								</select>
								<label for="fAreaCode">Area</label>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnClear" class="btn btn-sm btn-secondary" onclick="clearEventAuditLogFilters();">Clear Filters</button>
				<button type="submit" class="btn btn-sm btn-primary">Filter Audit Log</button>
			</div>
		</div>
	</form>
</div>
<div class="mb-2">This data describes changes made to events on this site in a date range. Default view shows the last 7 days.</div>
<table id="eventAuditLogsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>DATE / UPDATED BY</th>
			<th>DESCRIPTION</th>
		</tr>
	</thead>
</table>
</cfoutput>
