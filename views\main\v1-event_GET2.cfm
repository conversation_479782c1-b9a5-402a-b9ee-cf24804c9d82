<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/event/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 EVENT NOT FOUND</td><td>invalid event api_id</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "event": {
            "eventcode": "ABCDEFGH",
            "title": "Sample Event",
            "detail": "This is a sample event. Sign up today!",
            "startdate": "2020-02-01 15:00:00",
            "enddate": "2020-02-01 17:30:00",
            "timezone": "US/Central",
            "status": "Active",
            "alldayevent": 0,
            "hidefromcalendar": 0,
            "calendar": "Association Calendar and Events",
            "locationtitle": "Conference Center",
            "location": "123 Anystreet, Anytown, TX 77777",
            "traveltitle": "",
            "travel": "",
            "contacttitle": "",
            "contact": "",
            "canceltitle": "",
            "cancel": "",
            "informationtitle": "",
            "information": "",
            "event_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "category": [
                "Special Events",
                "CLE",
                ...
            ],
            "featuredimages": {
                "fullsize": {
                    "height": "519",
                    "width": "600",
                    "filename": "12345.jpg",
                    "url": "https://www.yourwebsite.org/featuredimages/originals/12345.jpg"
                },
                "program_listing": {
                    "height": "185",
                    "width": "185",
                    "filename": "12345-1.jpg",
                    "url": "https://www.yourwebsite.org/featuredimages/thumbnails/12345-1.jpg"
                },
                "program_detail": {
                    "height": "380",
                    "width": "380",
                    "filename": "12345-2.jpg",
                    "url": "https://www.yourwebsite.org/featuredimages/thumbnails/12345-2.jpg"
                },
                ...
            }
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 EVENT NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Event not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>