﻿<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/subscription/rateschedule</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following key.<br/>
		<div style="margin-left:30px;">
			<i>schedulename</i> - name of the subscription rate schedule<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/subscription/rateschedule/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com

{
    "schedulename": "Membership Rates"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 UPDATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error saving subscription rate schedule</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 UPDATED

{
    "data": {
        "count": 1,
        "result": "Subscription rate schedule updated.",
        "subscriptionrateschedule": {
            "schedulename": "Membership Rates",
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/subscription/rateschedule/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
        }
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update subscription rate schedule.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>