<cfif not request.perms_allAccounting>
	<cflocation url="/" addtoken="false">
</cfif>

<cfparam name="request.mode" default="start">

<cfif request.mode eq "start">
	<cfparam name="request.depomemberdataID" default="0">

	<cfscript>
	// get member data
	qryMemberData = application.objCommon.getMemberData(request.depomemberdataID);
	lookupAuthNetPaymentLink = "PaymentImportAuthorize.cfm?mode=lookupAuthorizeNetPayment&noheader=true";
	importAuthNetPaymentLink = "PaymentImportAuthorize.cfm?mode=doImportAuthorizeNetPayment&noheader=true";
	</cfscript>

	<cfsavecontent variable="jstoInclude">
		<cfoutput>
		<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
		<script type="text/javascript">
			function hideAlert() { $('##everr').html('').hide(); };
			function showAlert(msg) { $('##everr').html(msg).show(); };

			function lookupAuthNetTID() {
				hideAlert();
				$('##btnGetTID').attr('disabled',true);

				var mpid = $('##payProfileID').val();
				var antid = $('##AuthNetTID').val();
				if (antid.length == 0) {
					showAlert('Authorize.Net Transaction ID is required.');
					$('##btnGetTID').attr('disabled',false);
					return false;
				}

				$('div##divImportFormloadingDIV')
					.html('<div class="c"><i class="fa-light fa-circle-notch fa-spin fa-3x"></i><br/><b>Please Wait...</b><br/>We\'re looking for that transaction.</div>')
					.show()
					.load('#lookupAuthNetPaymentLink#&x_mpid=' + mpid + '&x_antid=' + antid,
						function(response, status, xhr) { 
							$('##btnGetTID').attr('disabled',false);
							if (status == 'error' || $.parseJSON(response).success == false ) {
								showAlert('Unable to contact Authorize.Net to lookup transaction.');
								$('##btnGetTID').show();
								$('div##divImportFormloadingDIV').hide();
							}
						} 
					);
			}

			function importAuthNetTID(tdata) {
				$('##btnGetTID, ##btnImportTID').attr('disabled',true);
				$('##btnImportTIDMsg').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Importing...').show().load(
					'#importAuthNetPaymentLink#&depomemberdataID=#qryMemberData.depoMemberDataID#&x_mpid=' + $('##payProfileID').val() + '&tdata='+tdata, 
					function(response, status, xhr) { 
						$('div##btnImportTIDMsg').hide();
					} 
				);
			}

			function importAuthNetTIDFail() {
				showAlert('We ran into a problem importing this payment. MemberCentral has been notified.');
				$('div##divImportFormloadingDIV').html('').hide();
				$('##btnGetTID').attr('disabled',false);
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#jstoInclude#">

	<!--- Crumb and title --->
	<cfoutput>
	<div id="crumbs">
		You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
		<a href="MemberEdit.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">Member Account: #qryMemberData.FirstName# #qryMemberData.LastName#</a> \ 
		Add Missing Authorize.Net Payment
	</div>

	<div id="pageTitle">Add Missing Authorize.Net Payment</div>
	</cfoutput>

	<!--- Summary --->
	<cfoutput>
	<table>
	<tr><td><b>Association:</b></td><td>#qryMemberData.TLAMemberState#</td></tr>
	<tr><td><b>Name:</b></td><td><a href="MemberEdit.cfm?depoMemberDataID=#qryMemberData.depoMemberDataID#">#qryMemberData.LastName#, #qryMemberData.FirstName#</a></td></tr>
	<tr><td><b>SourceID:</b></td><td>#qryMemberData.SourceID#</td></tr>
	<tr><td><b>DepoID:</b></td><td>#qryMemberData.depoMemberDataID#</td></tr>
	</table>
	<br/>

	<p>
		This tool can be used to add a payment from Authorize.Net that does not already exist in TS Admin as long as the payment used an Authorize.Net tokenized CIM record.<br/>
		Lookup the payment by selecting your Authorize.Net payment profile and the payment's transaction ID.
	</p>

	<div id="everr" style="display:none;margin-bottom:6px;color:red;font-weight:bold;"></div>

	<form name="frmImportANet" id="frmImportANet" method="post">
	<table cellpadding="4" cellspacing="0">
	<tr>
		<td><b>Payment Profile:</b></td>
		<td>
			<select id="payProfileID" name="payProfileID">
			<option value="TS">TS - TrialSmith</option>
			<option value="SW">SW - SeminarWeb</option>
			<option value="AS">AS - Asbestos Litigation Group</option>
			<option value="BT">BT - Birth Trauma Litigation Group</option>
			<option value="MG">MG - Medical Negligence Information Exchange Group</option>
			</select>
		</td>
	</tr>
	<tr>
		<td><b>Authorize.Net Transaction ID:</b></td>
		<td>
			<input type="text" name="AuthNetTID" id="AuthNetTID" value="" size="30" autocomplete="off">
			<button name="btnGetTID" id="btnGetTID" type="button" onClick="lookupAuthNetTID();">Lookup Payment</button>
		</td>
	</tr>
	</table>
	</form>

	<div id="divImportFormloadingDIV" style="display:none;margin-top:40px;" class="tsAppBodyText"></div>
	</cfoutput>

<cfelseif request.mode eq "lookupAuthorizeNetPayment">
	<cfparam name="request.x_mpid" default="0">
	<cfparam name="request.x_antid" default="0">

	<!--- if TS or SW, get credentials from depoTLA. Otherwise lookup based on control panel. --->
	<cfif listFindNoCase("TS,SW",request.x_mpid)>
		<cfquery name="qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername as gatewayUsername, authTransKey as gatewayPassword
			from dbo.depoTLA 
			where [state] = <cfqueryparam cfsqltype="cf_sql_varchar" value="#request.x_mpid#">
			and authCIM = 1
		</cfquery>
	<cfelseif listFindNoCase("AS,BT,MG",request.x_mpid)>
		<cfquery name="qryGetCIMInfo" datasource="#application.settings.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = dbo.fn_getSiteIDFromSiteCode(<cfqueryparam cfsqltype="cf_sql_varchar" value="#request.x_mpid#">);

			select TOP 1 gatewayUsername, gatewayPassword
			from dbo.mp_profiles 
			where siteID = @siteID
			and gatewayID = 10
			and status = 'A';
		</cfquery>
	</cfif>

	<!--- see if we already have that transaction --->
	<cfquery name="qryLookupExistingTID" datasource="#application.settings.dsn.trialsmith.dsn#">
		select top 1 transactionID
		from dbo.depoTransactions
		where isPayment = 1
		and ccTransactionId = <cfqueryparam value='#request.x_antid#' cfsqltype="cf_sql_varchar">
	</cfquery>

	<cfif qryLookupExistingTID.recordcount is 1>
		<cfset stErrorMessage = "That payment has already been captured in TS Admin in transactionID #qryLookupExistingTID.transactionID#.">
		<cfset strTransactionInfo.responseCode = 3>
	<cfelse>
		<cfset strTransactionInfo = CreateObject("component","models.trialsmith.tsChargeCardCIM").getTransaction(CIMUsername=qryGetCIMInfo.gatewayUsername, CIMPassword=qryGetCIMInfo.gatewayPassword, x_transid=request.x_antid)>

		<cfset stErrorMessage = "">
		<cfif strTransactionInfo.responseCode is 1>
			<cfset arrTransNode = XMLSearch(strTransactionInfo.rawResponse,"/getTransactionDetailsResponse/transaction")>
			<cfif NOT listFindNoCase("capturedPendingSettlement,settledSuccessfully",arrTransNode[1].transactionStatus.XmlText)>
				<cfset stErrorMessage = "We found the transaction, but the payment is not in the capturedPendingSettlement or settledSuccessfully status, so it cannot be imported.">
			<cfelseif arrTransNode[1].responseCode.XmlText neq "1">
				<cfset stErrorMessage = "We found the transaction, but the payment was not successful, so it cannot be imported.">
			<cfelseif NOT structKeyExists(arrTransNode[1], "profile")>
				<cfset stErrorMessage = "We found the transaction, but the payment was not made using an Authorize.Net CIM profile, so it cannot be imported.">
			</cfif>
			<cfset transactionDate = replaceNoCase(arrTransNode[1].submitTimeLocal.XmlText,"T"," ")>

			<cfset stTransData = Replace(URLEncodedFormat(ToBase64(Encrypt(strTransactionInfo.rawResponse,"20.18_CenTR@l"))),"%","xPcmKx","ALL")>
		<cfelse>
			<cfset stErrorMessage = strTransactionInfo.RESPONSEREASONTEXT>
			<cfif findNoCase("permissions to call the Transaction Details API",strTransactionInfo.RESPONSEREASONTEXT)>
				<cfset stErrorMessage = stErrorMessage & "<br/><br/>You will need to enable the Transaction Details API in the Authorize.Net account in order to use this import tool.">
			</cfif>
		</cfif>
	</cfif>

	<cfoutput>
	<div class="tsAppBodyText">
		<h3>Authorize.Net Transaction Information</h3>
		<cfif strTransactionInfo.responseCode is 1>
			<table cellpadding="4" cellspacing="0">
			<tr>
				<td class="tsAppBodyText"><b>Transaction ID:</b></td>
				<td class="tsAppBodyText">#arrTransNode[1].transId.XmlText#</td>
			</tr>
			<tr>
				<td class="tsAppBodyText"><b>Payment Submitted:</b></td>
				<td class="tsAppBodyText">#DateFormat(transactionDate,"m/d/yyyy")# #TimeFormat(transactionDate,"h:mm tt")#</td>
			</tr>
			<tr>
				<td class="tsAppBodyText"><b>Amount:</b></td>
				<td class="tsAppBodyText">#dollarFormat(arrTransNode[1].authAmount.XmlText)#</td>
			</tr>
			<tr>
				<td class="tsAppBodyText"><b>Payment:</b></td>
				<td class="tsAppBodyText">#arrTransNode[1].payment.creditcard.cardType.XmlText# #arrTransNode[1].payment.creditcard.cardNumber.XmlText#</td>
			</tr>
			<tr>
				<td class="tsAppBodyText"><b>Transaction Status:</b></td>
				<td class="tsAppBodyText">#arrTransNode[1].transactionStatus.XmlText#</td>
			</tr>
			<cfif len(arrTransNode[1].order.description.XmlText)>
				<tr>
					<td class="tsAppBodyText"><b>Order Description:</b></td>
					<td class="tsAppBodyText">#arrTransNode[1].order.description.XmlText#</td>
				</tr>
			</cfif>
			</table>
			<div style="margin-top:30px;border:1px solid;padding:10px;">
				<cfif len(stErrorMessage)>
					<div style="color:red;font-weight:bold;">#stErrorMessage#</div>
				<cfelse>
					<button name="btnImportTID" id="btnImportTID" type="button" onClick="importAuthNetTID('#stTransData#');">Import Payment</button>
					<span id="btnImportTIDMsg"></span>
					<div style="margin-top:10px;">
						This will add a payment of #dollarFormat(arrTransNode[1].authAmount.XmlText)# to this member's record.
					</div>
				</cfif>
			</div>
		<cfelse>
			<div style="color:red;font-weight:bold;">#stErrorMessage#</div>
		</cfif>
	</div>
	</cfoutput>

<cfelseif request.mode eq "doImportAuthorizeNetPayment">
	<cfparam name="request.depomemberdataID" default="0">
	<cfparam name="request.x_mpid" default="">
	<cfparam name="request.tdata" default="">

	<cftry>
		<cfset strTransInfo = decrypt(toString(toBinary(URLDecode(replace(request.tdata,"xPcmKx","%","ALL")))),"20.18_CenTR@l")>
		<cfset arrTransNode = XMLSearch(strTransInfo,"/getTransactionDetailsResponse/transaction")>

		<cfset importSuccess = CreateObject("component","models.trialsmith.tsChargeCardCIM").recordMissingTransaction(depomemberdataid=request.depomemberdataID, transactionNode=arrTransNode[1], merchantOrgCode=request.x_mpid)>
	<cfcatch type="Any">
		<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		<cfset importSuccess = false>
	</cfcatch>
	</cftry>

	<cfoutput>
	<script language="javascript">
		<cfif importSuccess>
			self.location.href='TransactionView.cfm?depoMemberDataID=#request.depomemberdataID#';
		<cfelse>
			importAuthNetTIDFail();
		</cfif>
	</script>
	</cfoutput>
</cfif>