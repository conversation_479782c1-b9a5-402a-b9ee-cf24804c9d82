<cfsavecontent variable="local.js2">
	<cfoutput>
	<script language="javascript">
		let subsAcctListTable;

		function getSubAcctFilters() {
			let objParams = {};
			objParams.sID = $('##fSubStatus2').val();
			objParams.stID = $('##fSubType2').val();
			if (objParams.stID.length == 0) objParams.stID = '0';
			objParams.subID = $('##fSubscription2').val();
			if (objParams.subID.length == 0) objParams.subID = '0';
			objParams.freqID = $('##fFreq2').val();
			objParams.dtTSf = $('##fTermStartFrom2').val();
			objParams.dtTSt = $('##fTermStartTo2').val();
			objParams.dtTEf = $('##fTermEndFrom2').val();
			objParams.dtTEt = $('##fTermEndTo2').val();
			objParams.spID = $('##fSubPaymentStatus2').val();
			objParams.hasCard = $('##fHasCard2').val();
			objParams.RevGL = $('##fRevenueGL2').val();
			objParams.associatedMemberID = $('##associatedMemberID2').val();
			objParams.associatedGroupID = $('##associatedGroupID2').val();
			objParams.associatedMemberName = $('##associatedMemberName2').val();
			objParams.associatedMemberNum = $('##associatedMemberNum2').val();
			objParams.associatedGroupName = $('##associatedGroupName2').val();
			objParams.rateID = $('##fRate2').val();
			if (objParams.rateID.length == 0)	objParams.rateID = '0';
			
			return objParams;
		}
		function clearFilterSubGrid2() {
			/* since reset() won't clear fields with default values */
			$('##frmFilter2 input[type="hidden"], ##frmFilter2 input[type="text"], ##fHasCard2').val('');
			$('##fSubPaymentStatus2, ##fFreq2, ##selfSubType2, ##selfSubscription2, ##fRevenueGL2').val(0);
			$('##fSubStatus2').val('0');
			$('##selfRate2').empty().trigger('change');
			$('##aClearAssocType2').click();
			filterSubGrid2();
		}
		function generateCustomSubAcctRadioFilterVerbose(filterField) {
			let label = "";
			let value = "";
			const fieldId = filterField.attr('id');

			if (filterField.is(':checked')) {
				switch (fieldId) {
					case 'assocTypeMember2':
						label = 'Associated With Member';
						value = $('##associatedMemberName2').val() + ' (' + $('##associatedMemberNum2').val() + ')';
					break;

					case 'assocTypeGroup2':
						label = 'Associated With Group';
						value = $('##associatedGroupName2').val();
					break;

					default:
						label = $(`label[for='${fieldId}']`).text().trim();
						value = filterField.val();
				}
			}

			return { label, value };
		}
		function filterSubGrid2() {
			var saveFilterResult = function(r) { 
				mca_generateVerboseFilterMessage('frmFilter2');
				reloadSubsAcctTable();
			};

			let strFilters = getSubAcctFilters();
			var objParams = { fType:'accounting', spID: strFilters.spID, stID: strFilters.stID, subID: strFilters.subID, rateID: strFilters.rateID,
				freqID: strFilters.freqID, dtTSf: strFilters.dtTSf, dtTSt: strFilters.dtTSt, dtTEf: strFilters.dtTEf, dtTEt: strFilters.dtTEt,
				sID: strFilters.sID, fCard: strFilters.hasCard, fRevGL: strFilters.RevGL, associatedMemberID: strFilters.associatedMemberID,
				associatedGroupID: strFilters.associatedGroupID, associatedMemberName: strFilters.associatedMemberName,
				associatedMemberNum: strFilters.associatedMemberNum, associatedGroupName: strFilters.associatedGroupName };
			TS_AJX('ADMSUBS','saveSubReportFilter',objParams,saveFilterResult,saveFilterResult,20000,saveFilterResult);
		}
		function startExportSubs2() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Export Subscribers',
				iframe: true,
				contenturl: '#this.link.startExportSubscriptionsAccounting#',
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'exportSubs2ActionButtonHandler',
					extrabuttonlabel: 'Export',
					extrabuttoniconclass: 'fa-light fa-file-csv'
				}
			});
		}
		function exportSubs2ActionButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.doExport();
		}
		function selectGroupInvFilter2() {
			var selhref = '#this.link.grpSelectGotoLink#&mode=direct&fldName=associatedGroupID2&retFunction=top.updateGroupField2&dispTitle=' + escape('Filter Subscribers by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectMemberInvFilter2() {		
			var selhref = '#this.link.memSelectGotoLink#&mode=direct&fldName=associatedMemberID2&retFunction=top.updateField2&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function updateField2(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal2');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				$('##associatedMemberName2').val(mName);
				$('##associatedMemberNum2').val(mNum);
				fldName.html(mName + ' (' + mNum + ')');
				$('##associatedGroupID2').val(0);
				$('##divAssociatedVal2').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal2').hide();
			}
		}
		function updateGroupField2(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal2');
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##associatedGroupName2').val(newgPath);
				fldName.html(newgPath);
				$('##associatedMemberID2').val(0);
				$('##divAssociatedVal2').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal2').hide();
			}
		}
		function initSubsAcctTable() {
			let domString = "<'row'<'d-flex col-sm-5 col-md-5'<'mt-2'l><'d-flex flex-wrap p-1 m-2'>><'col-sm-7 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			subsAcctListTable = $('##subsAcctListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 25,
				"lengthMenu": [ 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": '#local.subsAcctListLink#',
					"type": "post",
					"data": function(d) {
						let strFilters = getSubAcctFilters();
						d['sID'] = strFilters.sID;
						d['spID'] = strFilters.spID;
						d['stID'] = strFilters.stID;
						d['subID'] = strFilters.subID;
						d['rateID'] = strFilters.rateID;
						d['freqID'] = strFilters.freqID;
						d['fCard'] = strFilters.hasCard;
						d['dtTSf'] = strFilters.dtTSf;
						d['dtTSt'] = strFilters.dtTSt;
						d['dtTEf'] = strFilters.dtTEf;
						d['dtTEt'] = strFilters.dtTEt;
						d['associatedMemberID'] = strFilters.associatedMemberID;
						d['associatedGroupID'] = strFilters.associatedGroupID;
					},
					"deferLoading":400
				},
				"autoWidth": false,
				"columns": [
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div><a href="javascript:editMember('+data.memberID+')" _target="blank">'+data.memberName+'</a></div>';
								if(data.membercompany.length) renderData += '<div class="text-dim small">'+data.membercompany+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "25%"
					},
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div class="float-right">';
								if (data.paymentStatusCode == 'N' && ['A','P'].includes(data.statusCode)) renderData += '<span class="badge badge-warning">Activation Not Met</span>';
								renderData += '<span class="badge badge-neutral-second text-second">' + data.statusName + '</span></div>';
								renderData += '<div><a href="javascript:showSubTree('+data.memberID+','+data.subscriberID+')">'+data.subscriptionName+'</a></div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top"
					},
					{ "data": "substartdate", "width": "9%", "className": "align-top text-right" },
					{ "data": "subenddate", "width": "9%", "className": "align-top text-right" },
					{ "data": "subSaleDate", "width": "9%", "className": "align-top text-right" },
					{ "data": "subAmount", "width": "9%", "className": "align-top text-right" },
					{ "data": "subAmountDue", "width": "9%", "className": "align-top text-right" }
				],
				"order": [[4, 'desc']],
				"searching": false,
				"pagingType": "simple"
			});

			mca_generateVerboseFilterMessage('frmFilter2');
		}
		function reloadSubsAcctTable(){
			subsAcctListTable.draw();
		}

		$(document).ready(function(){
			mca_setupDatePickerRangeFields('fTermStartFrom2','fTermStartTo2');
			mca_setupDatePickerRangeFields('fTermEndFrom2','fTermEndTo2');
			mca_setupCalendarIcons('frmFilter2');
			mca_setupSelect2();
			
			$('body').on('change', '##selfSubType2', function(e) {
				mca_callChainedSelect('selfSubType2', 'selfSubscription2', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
				$('##fSubType2').val($('##selfSubType2').val());
				$('##fSubscription2, ##fRate2').val('0');
				$('##selfRate2').empty().trigger('change');;
			});

			$('body').on('change', '##selfSubscription2', function(e) {
				mca_callChainedSelect('selfSubscription2', 'selfRate2', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				$('##fSubscription2').val($('##selfSubscription2').val());
				$('##fRate2').val('0');
			});
		
			$('body').on('change', '##selfRate2', function(e) {
				var r = $('##selfRate2').val() || '';
				if (r.length > 0) { $('##fRate2').val(r.toString()); } else { $('##fRate2').val(''); }
			});

			if ($('##associatedMemberID2').val() == 0 && $('##associatedGroupID2').val() == 0) $('##divAssociatedVal2').hide();

			$(".assocType2").live("click",function(){	
				var assocType = $('input:radio[name=assocType2]:checked').val();
				if (assocType != undefined) {
					if (assocType == "group") selectGroupInvFilter2();
					else selectMemberInvFilter2();
				}
			});

			$("##aClearAssocType2").live("click",function() {
				$(".assocType2").each(function(){
					$(this).attr("checked",false);
				});	
				$('##associatedVal2').html("");
				$('##associatedMemberID2').val(0);
				$('##associatedGroupID2').val(0);
				$('##associatedMemberName2').val('');
				$('##associatedMemberNum2').val('');
				$('##associatedGroupName2').val('');
				$('##divAssociatedVal2').hide();
			});

			<cfif local.SubReportFilter.accountingFilter.fSubType gt 0>
				$('##selfSubType2').val(#local.SubReportFilter.accountingFilter.fSubType#);
				mca_callChainedSelect('fSubType2', 'selfSubscription2', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubReportFilter.accountingFilter.fSubscription#, false, false);
				<cfif local.SubReportFilter.accountingFilter.fSubscription gt 0>
					mca_callChainedSelect('fSubscription2', 'selfRate2', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubReportFilter.accountingFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js2)#">

<cfoutput>
<div class="toolButtonBar">
	<button class="btn btn-link p-0 mr-2" onclick="startExportSubs2();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to export subscriptions.">
		<i class="fa-regular fa-file-export"></i> Export Subscribers
	</button>
</div>

<div id="divFilterSubsAcctForm" class="mb-3" style="display:none;">
	<form name="frmFilter2" id="frmFilter2" onsubmit="filterSubGrid2();return false;" data-filterwrapper="divFilterSubsAcctForm" data-verbosemsgwrapper="divSubAcctFilterVerbose" data-customverbose-radio="generateCustomSubAcctRadioFilterVerbose">
		<input type="hidden" name="fSubType2" id="fSubType2" value="#local.SubReportFilter.accountingFilter.fSubType#">
		<input type="hidden" name="fSubscription2" id="fSubscription2" value="#local.SubReportFilter.accountingFilter.fSubscription#">
		<input type="hidden" name="fRate2" id="fRate2" value="#local.SubReportFilter.accountingFilter.fRate#">
		<input type="hidden" name="fRevenueGL2" id="fRevenueGL2" value="0">

		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Filter Subscribers
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="row">
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="selfSubType2" id="selfSubType2" class="form-control">
									<option value="0">All Subscription Types</option>
									<cfloop query="local.qrySubTypes">
										<option value="#local.qrySubTypes.typeID#">#local.qrySubTypes.typeName#</option>
									</cfloop>
								</select>
								<label for="selfSubType2">Subscription Type</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="selfSubscription2" id="selfSubscription2" class="form-control">
									<option value="0">All Subscriptions</option>
								</select>
								<label for="selfSubscription2">Subscription</label>
							</div>
						</div>
						<div class="form-group">
							<div class="d-flex align-items-center mb-1">
								<span class="text-grey small mx-1">Quickly Select: </span>
								<a href="javascript:quickSelectSubRates('selfRate2',0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
								<a href="javascript:quickSelectSubRates('selfRate2',1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
							</div>
							<div class="form-label-group mb-2">
								<select name="selfRate2" id="selfRate2" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
								<label for="selfRate2">Rate</label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartFrom2" id="fTermStartFrom2" value="#local.SubReportFilter.accountingFilter.fTermStartFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom2"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom2');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartFrom2">Start Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartTo2" id="fTermStartTo2" value="#local.SubReportFilter.accountingFilter.fTermStartTo#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo2"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo2');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartTo2">Start Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndFrom2" id="fTermEndFrom2" value="#local.SubReportFilter.accountingFilter.fTermEndFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom2"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom2');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndFrom2">End Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndTo2" id="fTermEndTo2" value="#local.SubReportFilter.accountingFilter.fTermEndTo#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo2"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo2');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndTo2">End Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubStatus2" id="fSubStatus2" class="form-control">
									<option value="0">All Statuses</option>
									<cfloop query="local.qryStatuses">
										<option value="#local.qryStatuses.statusCode#" <cfif local.SubReportFilter.accountingFilter.fSubStatus eq local.qryStatuses.statusCode>selected</cfif>>#local.qryStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubStatus2">Status</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubPaymentStatus2" id="fSubPaymentStatus2" class="form-control">
									<option value="0">All Activation Options</option>
									<cfloop query="local.qryPaymentStatuses">
										<option value="#local.qryPaymentStatuses.statusCode#" <cfif local.SubReportFilter.accountingFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode>selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubPaymentStatus2">Activation Option</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fFreq2" id="fFreq2" class="form-control">
									<option value="0">All Frequencies</option>
									<cfloop query="local.qryFrequencies">
										<option value="#local.qryFrequencies.frequencyID#" <cfif local.SubReportFilter.accountingFilter.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
									</cfloop>
								</select>
								<label for="fFreq2">Frequency</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fHasCard2" id="fHasCard2" class="form-control">
									<option value="">With or Without Pay Method Associated</option>
									<option value="Y" <cfif local.SubReportFilter.accountingFilter.fHasCardOnFile eq "Y">selected</cfif>>With Pay Method Associated</option>
									<option value="N" <cfif local.SubReportFilter.accountingFilter.fHasCardOnFile eq "N">selected</cfif>>With no Pay Method Associated</option>
								</select>
								<label for="fHasCard2">Pay Method</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group row">
					<div class="col-md-12">
						<div class="row">
							<div class="col-auto">
								Associated With:
							</div>
							<div class="col">
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType2" id="assocTypeMember2" class="assocType2 form-check-input" value="member"<cfif local.SubReportFilter.accountingFilter.associatedMemberID gt 0> checked</cfif>>
									<label class="form-check-label" for="assocTypeMember2">A Specific Member</label>
								</div>
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType2" id="assocTypeGroup2" class="assocType2 form-check-input" value="group"<cfif local.SubReportFilter.accountingFilter.associatedGroupID gt 0> checked</cfif>>
									<label class="form-check-label" for="assocTypeGroup2">A Specific Group</label>
								</div>
								<div id="divAssociatedVal2">
									<span id="associatedVal2" class="font-weight-bold">
										<cfif local.SubReportFilter.accountingFilter.associatedMemberID gt 0>
											#local.SubReportFilter.accountingFilter.associatedMemberName# (#local.SubReportFilter.accountingFilter.associatedMemberNum#)
										<cfelseif local.SubReportFilter.accountingFilter.associatedGroupID gt 0>
											#local.SubReportFilter.accountingFilter.associatedGroupName#
										</cfif>
									</span>
									<a href="##" id="aClearAssocType2" class="ml-2">clear</a>
								</div>
								<input type="hidden" name="associatedMemberID2" id="associatedMemberID2" value="#local.SubReportFilter.accountingFilter.associatedMemberID#">
								<input type="hidden" name="associatedMemberName2" id="associatedMemberName2" value="#local.SubReportFilter.accountingFilter.associatedMemberName#">
								<input type="hidden" name="associatedMemberNum2" id="associatedMemberNum2" value="#local.SubReportFilter.accountingFilter.associatedMemberNum#">
								<input type="hidden" name="associatedGroupID2" id="associatedGroupID2" value="#local.SubReportFilter.accountingFilter.associatedGroupID#">
								<input type="hidden" name="associatedGroupName2" id="associatedGroupName2" value="#local.SubReportFilter.accountingFilter.associatedGroupName#">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnResetFilterSubs2" class="btn btn-sm btn-secondary" onclick="clearFilterSubGrid2();">Clear Filters</button>
				<button type="submit" name="btnFilterSubs2" class="btn btn-sm btn-primary">
					<i class="fa-light fa-filter"></i> Filter Subscribers
				</button>
				<button type="button" class="btnReApplyFilter d-none" onclick="reloadSubsAcctTable();"></button><!--- hidden button used by verbose fn to refresh datatable --->
			</div>
		</div>
	</form>
</div>
<div id="divSubAcctFilterVerbose" style="display:none;"></div>
<table id="subsAcctListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Member</th>
			<th>Subscription</th>
			<th align="right">Start</th>
			<th align="right">End</th>
			<th align="right">Sale</th>
			<th align="right">Billed</th>
			<th align="right">Due</th>
		</tr>
	</thead>
</table>
</cfoutput>