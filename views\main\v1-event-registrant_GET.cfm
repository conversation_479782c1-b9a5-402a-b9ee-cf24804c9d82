<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/event/{api_id}/registrant</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li><i>registereddatefrom</i> is a date (m/d/yyyy) to limit registrations to on or after this date</li>
		<li><i>registereddateto</i> is a date (m/d/yyyy) to limit registrations to on or before this date</li>
		<li><i>attended</i> is a boolean value to limit attended or not attended registrants</li>
		<li><i>rates</i> is a pipe (|) delimited list of event rate names</li>
		<li><i>roles</i> is a pipe (|) delimited list of registrant role names</li>
		<li><i>authorityname</i> is the awarded credit authority name</li>
		<li><i>credittype</i> is the awarded credit type under the credit authority</li>
		<li><i>amountbilledfrom</i> is the billed amount from</li>
		<li><i>amountbilledto</i> is the billed amount to</li>
		<li><i>amountduefrom</i> is the due amount from</li>
		<li><i>amountdueto</i> is the due amount to</li>
		<li><i>group_api_id</i> is the api_id of a group to limit registrants to members of that group</li>
		<li>
			<i>search</i> is a structure of subkeys used to limit the members returned.<br/>
			Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		<li>
			<i>result</i> is an array of subkeys that specifies what information to return for each member. Subkeys defined here will be returned in addition to the standard data described below.<br/>
			Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
			If your JSON object contains invalid result subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each member listed in the response will have the same data as in the GET /members endpoint along with the following data:
		<ul>
			<li><i>x-member-api-uri</i> - the URI of the member</li>
			<li><i>x-api-uri</i> - the URI of the member's registration</li>
			<li><i>registereddate</i> - date of registration</li>
			<li><i>attended</i> - attended status of registrant</li>
			<li><i>amountdue</i> - amount due for the registrant</li>
			<li><i>totalfee</i> - amount billed for the registrant</li>
		</ul>
	</div>

	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/registrant HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/registrant HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 146

{
    "count": 5,
    "registereddatefrom": "1/23/2020",
    "registereddateto": "1/25/2020",
    "attended": true,
    "rates": "EarlyBird|Regular Member",
    "roles": "Speaker",
    "authorityname": "Credit Authority Name",
    "credittype": "Credit Authority Type",
    "amountduefrom": "0.00",
    "amountdueto": "10.00",
    "amountbilledfrom": "10.00",
    "amountbilledto": "120.00",
    "group_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
    "search": {
        "primary address_city": "Austin",
        "language": "Spanish"
    },
    "result": [
        "email"
    ]
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 EVENT NOT FOUND</td><td>invalid event api_id</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":5,
        "registrants":[
            {
                "membernumber":"SAMPLE123456",
                "prefix":"Mrs.",
                "firstname":"Jane",
                "middlename":"",
                "lastname":"Doe",
                "suffix":"",
                "professionalsuffix":"",
                "company":"Law Office of Jane Doe",
                "datecreated":"August, 05 2020 09:57:31 -0700",
                "datelastupdated":"August, 09 2020 19:09:36 -0700",
                "mcaccountstatus":"Active",
                "mcaccounttype":"User",
                "mcrecordtype":"Individual",
                "email": "<EMAIL>",
                "x-member-api-uri":"/v1/member/SAMPLE123456",
                "x-member-photo-uri":"https://www.yourwebsite.org/memberphotos/sample123456.jpg",
                "x-api-uri":"/v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/ZZZZZZZ",
                "dateregistered":"August, 10 2020 16:31:54 -0700",
                "attended": 1,
                "amountdue": "0.00",
                "totalfee": "100.00",
            } ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>

		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 EVENT NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Event not found."
    ]
}
</pre>
		</div>		
</div>
<div style="clear:both;padding-top:10px;"></div>