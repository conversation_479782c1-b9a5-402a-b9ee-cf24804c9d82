<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/email</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>type</i> - name of the email type (required)<br/>
			<i>description</i> - description of the email type (optional)<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/email HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 69

{
    "type": "Company Email",
    "description": "Company email address"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">201 CREATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT CREATED</td><td>error creating email type</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "count":1,
        "result": "Email type created.",
        "email": {
            "type": "Company Email",
            "description": "Company email address",
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
        }
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to create email type.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>