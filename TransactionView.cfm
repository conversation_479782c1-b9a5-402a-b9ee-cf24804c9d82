<cfparam name="request.depomemberdataID" default="0">

<cfset qryMemberData = application.objCommon.getMemberData(depoMemberDataID=request.depomemberdataID)>

<cfset local.qryMCMemberInfo = application.objCommon.getMemberDataMCInfo(depoMemberDataID=request.depomemberdataid)>
<cfif local.qryMCMemberInfo.memberID gt 0>
	<cfset local.cpLink = "http://#local.qryMCMemberInfo.hostname#?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryMCMemberInfo.memberID#&tab=emailactivity">
</cfif>

<cfquery name="local.qryBillingPeriods" datasource="#application.settings.dsn.trialsmith.dsn#">
	select periodID, EOMPeriod
	from dbo.billingPeriods
	order by EOMPeriod desc
</cfquery>

<cfquery name="qTransList" datasource="#application.settings.dsn.trialsmith.dsn#">
	set nocount on;

	IF OBJECT_ID('tempdb..##tmpTrans') IS NOT NULL 
		DROP TABLE ##tmpTrans;

	select t.ispayment, t.accountCode, t.Description, t.caseref, t.documentid, t.AmountBilled, t.SalesTaxAmount, t.DatePurchased, t.paymentmethod,
		t.Reversable, t.transactionid, t.enteredByDepomemberdataid, m.Firstname, m.LastName, t.manualTransaction, t.madewhilePending, t.orgcode,
		t.isPayrix, t.merchantOrgCode, ISNULL(t.refundableAmount,-1) as refundableAmount, t.ccTransactionID,
		ROW_NUMBER() OVER (order By t.DatePurchased asc, t.transactionID asc) as row
	into ##tmpTrans
	from dbo.depoTransactions t 
	left outer join dbo.depomemberdata m on t.enteredbyDepomemberdataid = m.depomemberdataid
	where t.depomemberdataid = <cfqueryparam value="#val(qryMemberData.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
	order by row;

	select tmp.*, (SELECT SUM(b.AmountBilled + b.SalesTaxAmount) FROM ##tmpTrans b WHERE b.row <= tmp.row) as Balance
	from ##tmpTrans as tmp
	order by tmp.row desc;

	IF OBJECT_ID('tempdb..##tmpTrans') IS NOT NULL 
		DROP TABLE ##tmpTrans;
</cfquery>
<cfquery name="qTransSum" dbtype="query">
	select sum(AmountBilled) as Total, sum(SalesTaxAmount) as TotalTax
	from qTransList
</cfquery>


<!--- Javascript --->
<cfsavecontent variable="JS">
	<script type="text/javascript">
		var GB_ROOT_DIR = "/javascripts/greybox/";
	</script>
	<script type="text/javascript" src="/javascripts/greybox/AJS.js"></script>
	<script type="text/javascript" src="/javascripts/greybox/gb_scripts.js"></script>
	<link href="/javascripts/greybox/gb_styles.css" rel="stylesheet" type="text/css" />

	<script language="JavaScript">
	function invoiceTransMark() {
		var cbox = document.forms['frmReverse'].invTransID;
		for (var i=0;i<cbox.length;i++) cbox[i].checked = true;
	}
	function invoiceTransUnmark() {
		var cbox = document.forms['frmReverse'].invTransID;
		for (var i=0;i<cbox.length;i++) cbox[i].checked = false;
	}
	function reverseTransMark() {
		var cbox = document.forms['frmReverse'].transactionID;
		for (var i=0;i<cbox.length;i++) cbox[i].checked = true;
	}
	function reverseTransUnmark() {
		var cbox = document.forms['frmReverse'].transactionID;
		for (var i=0;i<cbox.length;i++) cbox[i].checked = false;
	}

	function editTrans(tid) {
		return GB_showCenter('Edit Transaction','/TransactionEdit.cfm?tid=' + tid + '&nobanner=1',420,700);
	}
	function refundPayment(tid) {
		return GB_showCenter('Refund Payment','/PaymentRefund.cfm?tid=' + tid + '&nobanner=1',500,700);
	}
	function reloadTrans() {
		<cfoutput>self.location.href = 'TransactionView.cfm?depoMemberDataID=#request.depomemberdataid#';</cfoutput>
	}
	
	function invoiceTrans() {
        //reset on submit
        _CF_error_exists = false;
        _CF_error_messages = new Array();
        _CF_error_fields = new Object();
        _CF_FirstErrorField = null;

        _CF_this = document.forms['frmReverse'];
		
		//form element invTransID required check
        if( !_CF_hasValue(_CF_this['invTransID'], "CHECKBOX", false ) ) {
            _CF_onError(_CF_this, "invTransID", _CF_this['invTransID'].value, "Select at least one transaction to invoice.");
            _CF_error_exists = true;
        }

        //display error messages and return success
        if( _CF_error_exists )
        {
            if( _CF_error_messages.length > 0 )
            {
                // show alert() message
                _CF_onErrorAlert(_CF_error_messages);
                // set focus to first form error, if the field supports js focus().
                if( _CF_this[_CF_FirstErrorField].type == "text" )
                { _CF_this[_CF_FirstErrorField].focus(); }

            }
            return false;
        }else {
		   _CF_this.action = 'BillingGenerateInvoices.cfm?custombyTrans=1';
		   _CF_this.submit();
        }
	}
	function reverseTrans() {
        //reset on submit
        _CF_error_exists = false;
        _CF_error_messages = new Array();
        _CF_error_fields = new Object();
        _CF_FirstErrorField = null;

        _CF_this = document.forms['frmReverse'];

        //form element transactionID required check
        if( !_CF_hasValue(_CF_this['transactionID'], "CHECKBOX", false ) ) {
            _CF_onError(_CF_this, "transactionID", _CF_this['transactionID'].value, "Select at least one transaction to reverse.");
            _CF_error_exists = true;
        }

        //display error messages and return success
        if( _CF_error_exists )
        {
            if( _CF_error_messages.length > 0 )
            {
                // show alert() message
                _CF_onErrorAlert(_CF_error_messages);
                // set focus to first form error, if the field supports js focus().
                if( _CF_this[_CF_FirstErrorField].type == "text" )
                { _CF_this[_CF_FirstErrorField].focus(); }

            }
            return false;
        }else {
           _CF_this.action = 'TransactionReverse.cfm?btnReverse=1';
		   _CF_this.submit();
        }
	}
	function removePTrans() {
        //reset on submit
        _CF_error_exists = false;
        _CF_error_messages = new Array();
        _CF_error_fields = new Object();
        _CF_FirstErrorField = null;

        _CF_this = document.forms['frmReverse'];

        //form element transactionID required check
        if( !_CF_hasValue(_CF_this['transactionID'], "CHECKBOX", false ) ) {
            _CF_onError(_CF_this, "transactionID", _CF_this['transactionID'].value, "Select at least one transaction to reverse.");
            _CF_error_exists = true;
        }

        //display error messages and return success
        if( _CF_error_exists )
        {
            if( _CF_error_messages.length > 0 )
            {
                // show alert() message
                _CF_onErrorAlert(_CF_error_messages);
                // set focus to first form error, if the field supports js focus().
                if( _CF_this[_CF_FirstErrorField].type == "text" )
                { _CF_this[_CF_FirstErrorField].focus(); }

            }
            return false;
        }else {
           _CF_this.action = 'TransactionReverse.cfm?btnRemoveP=1';
		   _CF_this.submit();
        }
	}

	function generateBillingStatement(a) {
		var bp = document.getElementById('billingPeriod').value;
		var s = document.getElementById('spanemailstatement');
		s.style.display = 'none';
		s.innerHTML = '';
		<cfoutput>document.getElementById('statementFrame').src = 'BillingGenerateInvoices.cfm?bsdid=1&nobanner=1&depomemberdataID=#val(qryMemberData.depomemberdataID)#&bp='+bp+'&act='+a;</cfoutput>
	}
	</script>
</cfsavecontent>
<cfhtmlhead text="#js#">

<!--- Crumb and title --->
<cfoutput>
<div id="crumbs">
	You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
	<a href="MemberEdit.cfm?depomemberdataID=#val(qryMemberData.depomemberdataID)#">Member Account: #qryMemberData.FirstName# #qryMemberData.LastName#</a> \ 
	Transactions by Member
</div>

<div id="pageTitle">Transactions by Member</div>
</cfoutput>


<!--- Summary --->
<cfoutput>
<table>
<tr><td><b>Association:</b></td><td>#qryMemberData.TLAMemberState#</td>
	<td width="80">&nbsp;</td>
	<td rowspan="4">
		<form>
		<input type="button" <cfif not request.perms_canAddTransaction>disabled</cfif> value="Add Transaction" onclick="self.location.href='TransactionAdd.cfm?depomemberdataID=#qryMemberData.depomemberdataID#';">
		<input type="button" <cfif not request.perms_allAccounting>disabled</cfif> value="Add Manual Payment" onclick="self.location.href='PaymentAdd.cfm?depomemberdataID=#qryMemberData.depomemberdataID#';">
		<input type="button" <cfif not request.perms_allAccounting>disabled</cfif> value="Add Missing Authorize.Net Payment" onclick="self.location.href='PaymentImportAuthorize.cfm?depomemberdataID=#qryMemberData.depomemberdataID#';">
		<br/><br/>
	
		Regenerate Billing Statement for: 
		<select name="billingPeriod" id="billingPeriod">
			<cfloop query="local.qryBillingPeriods">
				<option value="#dateFormat(local.qryBillingPeriods.EOMPeriod,"m/d/yyyy")#">#dateFormat(local.qryBillingPeriods.EOMPeriod,"mmmm yyyy")#</option>
			</cfloop>
		</select>
		<input type="button" value="PDF" onclick="generateBillingStatement('dl');">
		&nbsp; &nbsp; &nbsp; &nbsp; 
		<input type="button" value="Email" <cfif isDefined("local.cpLink") AND (len(qryMemberData.Email) or len(qryMemberData.billingContactEmail))>onclick="generateBillingStatement('e');"<cfelse>disabled</cfif>>
		<span id="spanemailstatement" style="display:none;color:##228B22;font-weight:bold;padding-left:10px;"></span>
		<cfif isDefined("local.cpLink")>
			&nbsp; <a href="#local.cpLink#" target="_blank">View Previous Email Activity</a>
		</cfif>
		</form>
		<iframe name="statementFrame" id="statementFrame" frameborder="0" style="width:1;height:1" src="about:blank;"></iframe>
	</td>
	<td rowspan="4">
		<cfif not request.perms_allAccounting>
			<div style="align-content:center !important; align-items:center !important; display:flex !important; color:##824224; background-color:##fde4d5; border-color:##fcd9c4; position:relative; padding:.75rem 1.25rem; margin-bottom:1rem; margin-left:100px; border:1px solid transparent; border-radius:.65rem;">
				<span style="font-size: 1.1875rem;height: 40px !important; line-height: 40px !important; width: 40px !important; text-align: center !important; margin-right: .5rem !important;display: block !important;">
					<i class="fa-solid fa-headset" style="padding-top:10px;"></i>
				</span>
				<span>
					<strong style="display: block !important;">Restricted Mode</strong> Contact Lyndee to add, edit, or reverse transactions.
				</span>
			</div>
		</cfif>
	</td>
</tr>
<tr><td><b>Name:</b></td><td><a href="MemberEdit.cfm?depoMemberDataID=#qryMemberData.depoMemberDataID#">#qryMemberData.LastName#, #qryMemberData.FirstName#</a></td></tr>
<tr><td><b>SourceID:</b></td><td>#qryMemberData.SourceID#</a></td></tr>
<tr><td><b>DepoID:</b></td><td>#qryMemberData.depoMemberDataID#</a></td></tr>
</table>
<br/>
</cfoutput>

<cfif qTransList.recordcount is 0>
	<p>No transactions found.</p>

<cfelse>
	<cfoutput>
	<cfform action="TransactionReverse.cfm" method="POST" name="frmReverse">
	<input type="hidden" name="depomemberdataID" value="#qryMemberData.depomemberdataID#">
	<table class="tblSlim" width="100%">
	<tr><th>
		<span style="width:4px">&nbsp;</span>
		<img src="media/arrow_small.gif" width="21" height="15"><input type="button" name="btnInvoice" value="Invoice marked transactions" style="font-size:8pt;width:180px;" onClick="invoiceTrans()">
		<input type="button" name="btnInvoiceMark" value="Mark All" style="font-size:8pt;width:70px;" onClick="invoiceTransMark()">
		<input type="button" name="btnInvoiceUnmark" value="Unmark All" style="font-size:8pt;width:70px;" onClick="invoiceTransUnmark()">
		</th>
	</tr>
	<tr><th>
		<span style="width:60px"><img src="media/spacer.gif" width="60" height="1"></span>
		<img src="media/arrow_small.gif" width="21" height="15">
		<input type="button" <cfif not request.perms_allAccounting>disabled</cfif> name="btnReverse" value="Reverse marked transactions" style="font-size:8pt;width:180px;" onClick="reverseTrans()">
		<cfif listValueCount(valueList(qTransList.madewhilePending),"1") gt 0>
			<input type="button" name="btnRemoveP" value="Remove (P) from marked transactions" style="font-size:8pt;width:210px;" onClick="removePTrans()">
		</cfif>
		<input type="button" name="btnReverseMark" value="Mark All" style="font-size:8pt;width:70px;" onClick="reverseTransMark()">
		<input type="button" name="btnReverseUnmark" value="Unmark All" style="font-size:8pt;width:70px;" onClick="reverseTransUnmark()">
		<cfif listValueCount(valueList(qTransList.madewhilePending),"1") gt 0> &nbsp; <span class="red b">(P)</span> = Made while member was pending</cfif>
		</th>
	</tr>
	</table>
	<div id="scrollarea">
		<table class="tblSlim" width="100%">
		<tr><th>Invoice</th><th>Reverse</th><th>Org</th><th>TID</th><th>Trans&nbsp;Date</th><th>AcctCode</th><th>Description</th><th class="r">Amount</th><th class="r">Tax</th><th class="r">Balance</th><th>Method</th></tr>
		<cfloop query="qTransList">
			<tr <cfif qTranslist.ispayment is 1>bgcolor="##EEFFBA"</cfif>>
				<td width="40"><input type="checkbox" name="invTransID" value="#transactionID#"></td>
				<td>
					<cfif Reversable EQ "Y" AND qTranslist.ispayment is not 1 AND NOT listFind('7000,7001,7002,7003,7004',AccountCode)>
						<input type="checkbox" name="transactionID" value="#transactionID#">
					<cfelseif qTranslist.ispayment is 1 and qTranslist.amountBilled < 0 and request.perms_refundPayment and qTranslist.refundableAmount gt 0 and len(qTranslist.ccTransactionID) and len(qTranslist.merchantOrgCode)>
						<button type="button" name="btnRefund" style="font-size:8pt;width:70px;" onClick="refundPayment(#qTransList.transactionID#)">Refund</button>
					<cfelseif qTranslist.ispayment is 1 and qTranslist.amountBilled < 0 and qTranslist.refundableAmount eq 0>
						Refunded
					</cfif>
					<cfif madewhilePending is 1><span class="red b">(P)</span></cfif>
				</td>
				<td width="40">#orgcode#</td>
				<td>
					<cfif listFind('7000,7001,7002,7003,7004',AccountCode)>
						#transactionID#
					<cfelse>
						<cfif not request.perms_allAccounting>
							#transactionID#
						<cfelse>
							<a href="##" onclick="editTrans(#transactionID#);">#transactionID#</a>
						</cfif>						
					</cfif>
				</td>
				<td nowrap>#DateFormat(DatePurchased,"m/d/yy")# #TimeFormat(DatePurchased,"h:mm tt")#</td>
				<td>#accountCode#</td>
				<td>
					<cfif qTransList.isPayment is 1 and qTransList.isPayrix is 1>
						#qTransList.merchantOrgCode# 
					</cfif>
					#Description#<cfif len(caseref)>, Case Ref: #caseref#</cfif> <cfif DocumentID gt 0>(<a href="DocumentEdit.cfm?documentid=#documentid#">#documentid#</a>)</cfif>
					<cfif len(enteredByDepomemberdataid)><br/>Entered by: <span title="Depomemberdataid: #enteredByDepomemberdataid#">#Firstname# #lastname#</span><cfif manualTransaction is 1> (Manual)</cfif></cfif>
				</td>
				<td class="r bl">#DollarFormat(AmountBilled)#</td>
				<td class="r bl">#DollarFormat(SalesTaxAmount)#</td>
				<td class="r bl b">#DollarFormat(Balance)#</td>
				<td class="c bl">#paymentmethod#&nbsp;</td>
			</tr>
		</cfloop>
		<cfloop query="qTransSum">
			<tr><th colspan="7"><B>Total</B></th><th class="r">#DollarFormat(Total)#</th><th class="r">#DollarFormat(TotalTax)#</th><th colspan="2"></th></tr>
		</cfloop>
		</table>
	</div>
	</cfform>
	</cfoutput>
</cfif>
