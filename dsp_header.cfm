<CFHEADER NAME="Expires" VALUE="Mon, 06 Jan 1990 00:00:01 GMT">
<CFHEADER NAME="Pragma" VALUE="no-cache">
<CFHEADER NAME="cache-control" VALUE="no-cache, no-store, must-revalidate">

<cfoutput>
<html>
<head>
	<title>Admin</title>
	<script src="https://kit.fontawesome.com/feb2b3f29d.js" crossorigin="anonymous"></script>
	<STYLE>
	<cfif listFindNoCase("localDevelopment,development,test",application.settings.environment)>
		body { background: url("/media/development.gif"); }
	<cfelseif listFindNoCase("beta",application.settings.environment)>
		body { background: url("/media/beta.gif"); }
	</cfif>

	/* TS green 99cc00; TS blue: 0E568D; SW green: 6c9d32; SW blue: 0054a5; */
	body, div, td, layer { font-size:9pt; font-family:arial; }
	a:link, a:visited, a:hover, a:active { color:##0E568D; text-decoration:underline; }
	##pageTitle { font-family:verdana; font-size:1.5em; font-weight:bold; margin:12px 0 4px 0; color:##0E568D; }
	div.menuSectionTitle { font-size:1.2em; font-weight:bold; margin:8px 0; background-color:##0E568D; padding:2px; color:##fff;}
	div.menuSectionTitleSW { background-color:##609327; color:##EEE;}
	.tmode { font-size: 11px; color: ##f00; font-weight: bold;}
}
	table.tblSlim, table.tblSlimmer { border-collapse:collapse; border:1px solid ##bbb; }
	table.tblSlim tr { vertical-align:top; }
	table.tblSlim th, table.tblSlimmer th { font-size:10pt; font-weight:bold; text-align:left; background-color:##CCC; padding:4px; }
	table.tblSlim th.c, table.tblSlimmer th.c { text-align:center; }
	table.tblSlim th.r, table.tblSlimmer th.r { text-align:right; }
	table.tblSlim td { padding:4px; border-bottom: 1px solid ##bbb; }
	table.tblSlimmer td { padding:2px; border-bottom: 1px solid ##bbb; }
	table.tblInside { border-collapse:collapse; border:0px; }
	table.tblInside td { padding:2px; border-bottom:0px; }

	table.navtable button { padding-left:5px; padding-right:5px; }
	table.navtable div.navrow { margin-bottom:3px; }
	
	.button { height:22px; }
	.red { color:##F00; }
	.pending { color:##3CF; }
	.b { font-weight:bold; }
	.r { text-align:right; }
	.c { text-align:center; }
	##logoutlink { float: right; font-size:10pt; }
	##crumbs { background-color:##ccc; padding:2px; margin-top:4px; font-weight:bold; border-top:1px solid ##666; border-bottom:1px solid ##666;}
	.bb { border-bottom: 1px solid ##bbb; }
	.bl { border-left: 1px solid ##bbb; }
	.dim { color: ##bbb; }
	.dim50 { color: ##999; }
	.pendingTransAlert { margin-top:8px; padding:3px; border:1px solid ##F00; color:##F00; font-weight:bold; }
	
	.matchBack { background:url(/media/possibleMatch.gif); background-repeat:no-repeat; background-position:top left; }
	.matchScore { position:relative; margin-left:20px; top:5px; color:##06F; font-size:8pt; font-weight:bold; }
	.matchName { margin-left:30px; }
	.matchbt { border-top: 4px solid ##CCC; }
	
	.cellRejected { background:##FFF; color:##969696; }
	.cellSelected { background:##6c9; color:##000; }
	.cellHidden { background:##eee; color:##eee; }

	ul.admintabs { list-style-type:none; margin:15px 0 0 0; padding:0 0 0.3em 0; }
	ul.admintabs li { display:inline; }
	ul.admintabs li a { color:##42454a; background-color:##dedbde; border:1px solid ##c9c3ba; border-bottom:none; padding:0.85em 0.45em 0.38em 0.45em; text-decoration:none; }
	ul.admintabs li a:hover { background-color:##f1f0ee; }
	ul.admintabs li a.active { color:##000; background-color:##f1f0ee; font-weight:bold; padding:0.85em 0.45em 0.38em 0.45em; }
	div.admintabcontent { border:1px solid ##c9c3ba; padding:0.5em; }
	
	.hidden { display:none; }
	.shown { display:block; }
	</STYLE>
	
	<SCRIPT LANGUAGE="JavaScript">
	function launchX(url) { remote = window.open(url, "remoteX", "scrollbars=yes,resizable=yes,width=600,height=500"); }
	function launchCus(url,w,h) { remoteCust = window.open(url, "remoteCust", "scrollbars=yes,resizable=yes,width=" + w + ",height=" + h); }
	function adminMenuJump(menuitem) { self.location.href = '/adm_depoexec.cfm?menuitem=' + menuitem; }
	function adminSwitchTab(tabid,tabclass,tabmenuclass,tabcontent) {
		var i, x = document.getElementsByClassName(tabclass);
		for (i = 0; i < x.length; i++) { x[i].style.display = 'none'; }
		document.getElementById(tabcontent).style.display = 'block';
		x = document.getElementsByClassName(tabmenuclass);
		for (i = 0; i < x.length; i++) { x[i].className = tabmenuclass; }
		document.getElementById(tabid).className = tabmenuclass + ' active';
	}
	</script>

	<!--- script used for popup calendar --->
	<script type="text/javascript" src="/media/calendarDateInput.js"></script>
	
	<!--- script for ajax calls --->
	<script type='text/javascript'>ajaxurl = '/scheduled/ajax.cfm';</script>
	<script type='text/javascript' src='/javascripts/ajax-min.js?20140516'></script>
	<script type='text/javascript' src='/javascripts/cfform.js'></script>

	<META HTTP-EQUIV="expires" CONTENT="0"> 
	<META HTTP-EQUIV="Pragma" CONTENT="no-cache"> 
	<META HTTP-EQUIV="cache-control" CONTENT="no-cache, no-store, must-revalidate"> 

	<cfif StructKeyExists(CGI,"HTTP_COOKIE")>
		<cfoutput><!-- cookie: #cgi.HTTP_COOKIE# --></cfoutput>
	</cfif>

</head>

<body topmargin="6">

<!--- tooltips (must be included in body) --->
<script type="text/javascript" src="/javascripts/tooltips/wz_tooltip.js"></script>
<script type="text/javascript" src="/javascripts/tooltips/tip_balloon.js"></script>

<cfif NOT isDefined("request.nobanner")>
	<div id="alertDIV" style="display:none;">
		<div id="alertMSG"></div>
		<div id="alertForm">
			<textarea id="frmAlertMSG" name="frmAlertMSG" style="width:70%;height:40px;"></textarea>
			<input type="button" name="btnSubmitAlert" value="Update Alert" style="font-size:.9em;" onClick="saveAlert();">
			<input type="button" name="btnCancelAlert" value="Close" style="font-size:.9em;" onClick="closeAlert();">
		</div>
	</div>
	<div>
		<span id="logoutlink">
			<cfif session.loggedin is 1>
				<a href="/adm_depomenu.cfm?lo=1">Logoff</a> &bull; 
			</cfif>
			<cfif isDefined("server.system.environment.SOURCE_BRANCH") AND len(server.system.environment.SOURCE_BRANCH)>
				#server.system.environment.SOURCE_BRANCH#:#left(server.system.environment.SOURCE_COMMIT,10)#:#server.system.environment.BUILD_DATE#
			</cfif>
		</span>
		<a href="/adm_depomenu.cfm"><img src="/media/adminBanner.png" alt="" width="381" height="30" border="0"></a>
	</div>
</cfif>
</cfoutput>
