<div class="delete method-example">
	<div class="method-wrapper">
		<div class="method">DELETE</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Status Requirement</div>
	<div class="jsonblock-info">
		Only pending referrals can be DELETED. Issuing this call against a referral that is not pending will result in 406 NOT ACCEPTABLE.
	</div>
	
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 REFERRAL NOT FOUND</td><td>referral not found</td></tr>
			<tr><td class="rc">406 NOT ACCEPTABLE</td><td>referral cannot be deleted</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>unable to delete referral</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result":"Referral deleted."
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 REFERRAL NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Referral not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>