<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// SET RIGHTS INTO EVENT -------------------------------------------------------------------- ::
			this.siteResourceID = arguments.event.getTrimValue('srID',0);
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];
			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getSavedReports" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sr.reportName")>
		<cfset arrayAppend(local.arrCols,"logData.lastRunDate")>
		<cfset arrayAppend(local.arrCols,"sr.dateCreated")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qrySavedReports" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpReports') IS NOT NULL 
				DROP TABLE ##tmpReports;
			CREATE TABLE ##tmpReports (reportID int PRIMARY KEY, siteResourceID int, reportName varchar(200), dateCreated datetime, 
				toolType varchar(100), memberid int, firstname varchar(75), lastname varchar(75), status char(1), lastRunDate datetime, row int,
				lastRunFirstName varchar(75), lastRunLastName varchar(75));

			DECLARE @siteID int, @totalCount int, @posStart int, @posStartAndCount int, @loggedInMemberID int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			SET @loggedInMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			INSERT INTO ##tmpReports (reportID, siteResourceID, reportName, dateCreated, toolType, memberid, firstname, lastname, status, lastRunDate, lastRunFirstName, lastrunLastName, row)
			select sr.reportID, ast.siteResourceID, ltrim(sr.reportName), sr.dateCreated, tt.toolType, mActive.memberID, mActive.firstname, mActive.lastname, sr.status, 
				logData.lastRunDate, logData.lastRunFirstName, logData.lastrunLastName, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			from dbo.rpt_SavedReports as sr
			inner join dbo.admin_toolTypes tt on tt.toolTypeID = sr.toolTypeID 
				and sr.siteID = @siteID
				and tt.toolTypeID = <cfqueryparam value="#arguments.event.getValue('tt', 0)#" cfsqltype="CF_SQL_INTEGER">
				and tt.includeInAllReportsGrid = 1
				and sr.controllingSiteResourceID = <cfqueryparam value="#this.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.admin_siteTools ast on ast.toolTypeID = sr.toolTypeID
				and ast.siteID = sr.siteID
			inner join dbo.ams_members as m on m.memberid = sr.memberid 
			inner join dbo.ams_members as mActive on mActive.memberid = m.activememberID
			outer apply (
				select runlog.dateRun as lastRunDate, mRunActive.firstname as lastRunFirstName, mRunActive.lastname as lastrunLastName
				from (
					select max(logID) as lastRunID
					from platformstatsMC.dbo.rpt_runLog 
					where reportID = sr.reportID
				) as tmp
				inner join platformstatsMC.dbo.rpt_runLog as runlog on runlog.logID = tmp.lastRunID
				INNER JOIN dbo.ams_members AS mRun ON mRun.memberid = runlog.memberID
				INNER JOIN dbo.ams_members AS mRunActive ON mRunActive.memberid = mRun.activememberID
			) as logData
			where 1 = 1
			<cfif len(local.searchValue)>
				AND (sr.reportName LIKE @searchValue OR mActive.firstname LIKE @searchValue OR mActive.lastname LIKE @searchValue)
			</cfif>;

			SET @totalCount = @@ROWCOUNT;
			
			SELECT reportID, toolType, reportName, dateCreated, memberID, firstname, lastname, status, lastRunDate, lastRunFirstName, lastrunLastName, 
				dbo.fn_cache_perms_getResourceRightsXML(siteResourceID,@loggedInMemberID,@siteID) as reportPerms, @totalCount as totalCount
			FROM ##tmpReports AS tmp
			WHERE tmp.row > @posStart 
			AND tmp.row <= @posStartAndCount
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##tmpReports') IS NOT NULL 
				DROP TABLE ##tmpReports;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qrySavedReports">
			<cfset local.statusChangeTo = ''>
			<cfswitch expression="#local.qrySavedReports.Status#">
				<cfcase value="A">
					<cfset local.Status = "Active">
					<cfset local.statusChangeTo = 'I'>
					<cfset local.SwitchText = "Inactivate Report">
				</cfcase>
				<cfcase value="I">
					<cfset local.Status = "Inactive">
					<cfset local.statusChangeTo = 'A'>
					<cfset local.SwitchText = "Activate Report">
				</cfcase>
				<cfdefaultcase>
					<cfset local.Status = "N/A">
				</cfdefaultcase>
			</cfswitch> 

			<cfset local.editAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='EditAnyReport']/@allowed)")>
			<cfset local.editOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='EditOwnReport']/@allowed)")>
			<cfset local.runAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='RunAnyReport']/@allowed)")>
			<cfset local.runOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='RunOwnReport']/@allowed)")>
			<cfset local.deleteAnyReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='DeleteAnyReport']/@allowed)")>	
			<cfset local.deleteOwnReport = XMLSearch(local.qrySavedReports.reportPerms,"string(/rights/right[@functionName='DeleteOwnReport']/@allowed)")>	
			<cfset local.arrData.append({
				"reportid": local.qrySavedReports.reportID,
				"tooltype": local.qrySavedReports.toolType,
				"reportname": local.qrySavedReports.reportName,
				"status": local.Status,
				"statuschangeto": local.statusChangeTo,
				"switchtext": local.SwitchText,
				"creatorname": "#local.qrySavedReports.firstname# #local.qrySavedReports.lastName#",
				"lastrunname": "#local.qrySavedReports.lastrunFirstName# #local.qrySavedReports.lastrunLastName#",
				"lastrundatetime": "#len(local.qrySavedReports.lastRunDate) gt 0 ? dateTimeFormat(local.qrySavedReports.lastRunDate,'m/d/yy h:nn tt') & ' CT' : ''#",
				"createddatetime": "#dateTimeFormat(local.qrySavedReports.dateCreated,'m/d/yy h:nn tt')# CT",
				"totalcount": local.qrySavedReports.totalcount,
				"loadReport": (val(local.runAnyReport) or (val(local.runOwnReport) and local.qrySavedReports.memberid eq session.cfcuser.memberdata.memberid)) ? 1 : 0,
				"copyReport": (val(local.editAnyReport) or (val(local.editOwnReport) and local.qrySavedReports.memberid eq session.cfcuser.memberdata.memberid)) ? 1 : 0,
				"deleteReport": (val(local.deleteAnyReport) or (val(local.deleteOwnReport) and local.qrySavedReports.memberid eq session.cfcuser.memberdata.memberid)) ? 1 : 0,
				"DT_RowId": "savedRptRow_#local.qrySavedReports.reportID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qrySavedReports.totalcount),
			"recordsFiltered": val(local.qrySavedReports.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralPanelsInReport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		local.rpIDList = arguments.event.getValue('p','');

		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		local.searchValue = form['search[value]'] ?: '';

		local.qryReportInfo = CreateObject("component","report").getReportInfo(rptid=arguments.event.getValue('rptId'), tooltypeid=arguments.event.getValue('rptTT'), siteResourceID=this.siteResourceID);
		local.otherXML = XMLParse(local.qryReportInfo.otherXML);
		</cfscript>

		<cfquery name="local.qryReferralPanels" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpReportPanels') IS NOT NULL 
				DROP TABLE ##tmpReportPanels;
			CREATE TABLE ##tmpReportPanels (panelID int PRIMARY KEY, thePathExpanded varchar(max), row int);

			DECLARE @siteID int, @referralID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			SELECT top 1 @referralID = r.referralID
			FROM dbo.ref_referrals as r
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			WHERE ai.siteID = @siteID;

			-- This subquery is because some reports have duplicate panel ids in the rplist for some reason
			INSERT INTO ##tmpReportPanels (panelID, thePathExpanded, row)
			SELECT panelID, thePathExpanded, ROW_NUMBER() OVER (ORDER BY thePathExpanded #arguments.event.getValue('orderDir')#) as row
			from (
				SELECT distinct p.panelID, p.thePathExpanded
				FROM dbo.fn_getRecursiveReferralPanels(@referralID,null) as p
				INNER JOIN dbo.fn_intListToTable(<cfqueryparam value="0#XMLSearch(local.otherXML,"string(/report/extra/rplist/text())")#" cfsqltype="CF_SQL_VARCHAR">,',') dg on dg.listitem = p.panelID
				<cfif len(local.searchValue)>
					WHERE p.thePathExpanded LIKE @searchValue
				</cfif>
			) as tmp;

			SET @totalCount = @@ROWCOUNT;
			
			SELECT panelID, thePathExpanded, @totalCount as totalCount
			FROM ##tmpReportPanels AS tmp
			WHERE tmp.row > @posStart 
			AND tmp.row <= @posStartAndCount
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##tmpReportPanels') IS NOT NULL 
				DROP TABLE ##tmpReportPanels;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qryReferralPanels">
			<cfset local.arrData.append({
				"panelID": local.qryReferralPanels.panelID,
				"thePathExpanded": local.qryReferralPanels.thePathExpanded,
				"DT_RowId": "reportPanelRow_#local.qryReferralPanels.panelID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryReferralPanels.totalcount),
			"recordsFiltered": val(local.qryReferralPanels.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getReferralPanels" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		local.searchValue = form['search[value]'] ?: '';

		local.qryReportInfo = CreateObject("component","report").getReportInfo(rptid=arguments.event.getValue('rptId'), tooltypeid=arguments.event.getValue('rptTT'), siteResourceID=this.siteResourceID);
		local.otherXML = XMLParse(local.qryReportInfo.otherXML);
		</cfscript>

		<cfquery name="local.qryReferralPanels" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpReportPanels') IS NOT NULL 
				DROP TABLE ##tmpReportPanels;
			CREATE TABLE ##tmpReportPanels (panelID int PRIMARY KEY, thePathExpanded varchar(max), isSelected bit, row int);

			DECLARE @siteID int, @referralID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			SELECT top 1 @referralID = r.referralID
			FROM dbo.ref_referrals as r
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			WHERE ai.siteID = @siteID;

			INSERT INTO ##tmpReportPanels (panelID, thePathExpanded, isSelected, row)
			SELECT panelID, thePathExpanded, isSelected, ROW_NUMBER() OVER (ORDER BY thePathExpanded #arguments.event.getValue('orderDir')#) as row
			FROM (
				SELECT DISTINCT p.panelID, p.thePathExpanded, case when isnull(dg.listitem,0) > 0 then 1 else 0 end as isSelected
				FROM dbo.fn_getRecursiveReferralPanels(@referralID,null) as p
				LEFT OUTER JOIN dbo.fn_intListToTable(<cfqueryparam value="0#XMLSearch(local.otherXML,"string(/report/extra/rplist/text())")#" cfsqltype="CF_SQL_VARCHAR">,',') dg on dg.listitem = p.panelID
				<cfif len(local.searchValue)>
					WHERE p.thePathExpanded LIKE @searchValue
				</cfif>
			) as tmp;

			SET @totalCount = @@ROWCOUNT;
			
			SELECT panelID, thePathExpanded, isSelected, @totalCount as totalCount
			FROM ##tmpReportPanels AS tmp
			WHERE tmp.row > @posStart 
			AND tmp.row <= @posStartAndCount
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##tmpReportPanels') IS NOT NULL 
				DROP TABLE ##tmpReportPanels;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryReferralPanels">
			<cfset local.arrData.append({
				"panelID": local.qryReferralPanels.panelID,
				"thePathExpanded": local.qryReferralPanels.thePathExpanded,
				"isSelected": local.qryReferralPanels.isSelected,
				"DT_RowId": "referralPanelRow_#local.qryReferralPanels.panelID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryReferralPanels.totalcount),
			"recordsFiltered": val(local.qryReferralPanels.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>