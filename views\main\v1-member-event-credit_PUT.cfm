<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/event/{api_id}/{registrant_id}/credit</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with an array of awarded credits. All 4 subkeys are required for each credit. Any existing credits will be removed prior to assigning credit.
		<ul>
			<li><i>authoritycode</i> - the credit authority code</li>
			<li><i>credittypecode</i> - the credit type code</li>
			<li><i>creditawarded</i> - the numeric amount of credit awarded</li>
			<li><i>idnumber</i> - may be blank; any authority specific ID number for this registrant</li>
		</ul>
	</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/123456/credit HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 96

[
    {
        "authoritycode": "xxxxx",
        "credittypecode": "xxxxx",
        "creditawarded": 1.5,
        "idnumber": "xxxxx"
    }
]
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 UPDATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 EVENT NOT FOUND</td><td>invalid event api_id</td></tr>
			<tr><td class="rc">404 EVENT REGISTRATION NOT FOUND</td><td>member is not registered for this event</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error updating registration</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 UPDATED

{
    "data": {
        "count": 3,
        "credit": [
            {
                "authorityname": "State Bar Of California",
                "credittype": "General",
                "creditawarded": 1,
                "credittypecode": "General",
                "idnumber": "CA123456",
                "authoritycode": "CAStateBar"
            },
            ...
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update registration credit."
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>