<cfparam name="request.documentID" default="0">
<cfparam name="request.opt3" default="0">

<cfset local.objDocuments = CreateObject("component","models.tsadmin.act_documents")>
<cfset local.getDocument = local.objDocuments.getDocument(val(request.documentID))>

<cfif request.keyExists("approveDoc") AND request.approveDoc EQ 1>
	<cfset local.objDocuments.saveDocument(request)>
	<cfset local.objDocuments.approveDocument(request)>

	<cfset local.qryNextDocument = local.objDocuments.getNextDocumentPending(documentID=request.documentID)>
	<cfif local.qryNextDocument.recordCount>
		<cfset local.navigateURL = "DocumentEdit.cfm?depoMemberDataID=#local.qryNextDocument.depoMemberDataID#&documentID=#local.qryNextDocument.DocumentID#">
	<cfelse>
		<cfset local.navigateURL = "DocumentsQueue.cfm">
	</cfif>
	
	<div style="width:360px;color:#0c6b7e; background-color:#cff3f8; border-color:#bceff5;position:relative; padding:.75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-radius: .65rem;">
		<cfif request.keyExists("docApprovalOpt") AND request.docApprovalOpt EQ 'new'>
			The document has successfully been uploaded and approved.
		<cfelseif request.keyExists("docApprovalOpt") AND request.docApprovalOpt EQ 'merge'>
			The document has successfully been merged and approved.
		<cfelse>
			The document has been approved successfully.
		</cfif>
	</div>
	<div style="margin-top:20px;"><i class="fa-light fa-circle-notch fa-spin"></i> <b>Please wait while we load the next document.</div>
	<script type="text/javascript">
		setTimeout(function(){ 
			top.closeBox();
			top.location.href = <cfoutput>'#local.navigateURL#'</cfoutput>;
		},3000);
	</script>

<cfelseif listFindNoCase("Pending Approval,Uploaded",local.getDocument.documentStatusName)>
	<cfsavecontent variable="local.pageJS">
		<cfoutput>
		<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
		<script type="text/javascript">
			function validateForm(theForm) {
				var errMsg = '';

				if (!$('input[name="docApprovalOpt"]').is(':checked')) 
					errMsg += 'Select a Document Approval Option.<br/>';
				else if ($('input[name="docApprovalOpt"]:checked').val() == 'new' && !$('##newDepoDocFile').val().length) 
					errMsg += 'Select a New Document File.<br/>';

				if (errMsg.length) {
					$('##err_frmdocapprove').html(errMsg).show();
					return false;
				} else {
					$('##frmDocButtons').hide();
					$('##frmDocSaveLoading').show();
					$('##err_frmdocapprove').html('').hide();
				}
				return true;
			}
			function toggleDocApprovalOptions(f){
				if(f) $('.newDocApproveOptions').show();
				else $('.newDocApproveOptions').hide();
			}
			<cfif request.opt3 is 1>
				$(function() {
					var newsortorder = [], objSort;
					top.$('table##attachSortTable tbody tr.csv_in_row').each(function() { 
						var cbox = $(this).find('input.sortfld:checked');
						if (cbox.length == 1) {
							objSort = { type:cbox.data('attype'), atb64:cbox.data('atb64') };
							newsortorder.push(objSort); 
						}
					});
					if (newsortorder.length == 0) {
						$('input##docApprovalOpt_merge').attr('disabled','disabled');
						$('span##attachmergelabel').css('color','##ccc');
					} else {
						$('span##numselattach').text(newsortorder.length);
						$('input##newsortorder').val(JSON.stringify(newsortorder));
						$('input##docApprovalOpt_merge').prop('checked', true);
					}
				});
			</cfif>
		</script>
		<style type="text/css">
			##err_frmdocapprove { width:500px;padding:0.5rem 1rem;margin:10px 0px;border:1px solid ##e78989;border-radius:0.25rem;color:##870808;background-color:##ebcaca; }
			button##btnApproveDoc {
				color:##fff;
				background-color:##28a745;
				border-color:##28a745;
				display: inline-block;
				font-weight: 400;
				text-align: center;
				white-space: nowrap;
				vertical-align: middle;
				border: 1px solid transparent;
				padding: .375rem .75rem;
				font-size: 1rem;
				line-height: 1.5;
				border-radius: .25rem;
				transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
			}
			button##btnCancel {
				margin-left:10px;
				font-weight: 400;
				color: ##007bff;
				background-color: transparent;
				display: inline-block;
				text-align: center;
				white-space: nowrap;
				vertical-align: middle;
				user-select: none;
				border: 1px solid transparent;
				padding: .375rem .75rem;
				font-size: 1rem;
				line-height: 1.5;
				border-radius: .25rem;
				transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
			}
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.pageJS#">

	<cfset local.formActionURL = "DocumentApprove.cfm?documentID=#local.getDocument.documentID#&depoMemberDataID=#local.getDocument.depoMemberDataID#&approveDoc=1&nobanner=1">
	<cfoutput>
	<div id="pageTitle">Approve Document #local.getDocument.documentID#</div>

	<div id="err_frmdocapprove" style="width:250px;display:none;"></div>
	<form id="frmApproveDocument" name="frmApproveDocument" action="#local.formActionURL#" method="POST" enctype="multipart/form-data" onsubmit="return validateForm(this)">
		<input type="hidden" name="newsortorder" id="newsortorder" value="">
		<cfloop collection="#request#" item="thisField">
			<cfif isSimpleValue(request[thisField]) AND NOT listFindNoCase("documentID,depoMemberDataID,nobanner",thisField)>
				<input type="hidden" name="#thisField#" value="#request[thisField]#">
			</cfif>
		</cfloop>

		<div style="margin-bottom:10px;margin-top:10px;">
			<input type="radio" name="docApprovalOpt" id="docApprovalOpt_original" value="original" checked onclick="toggleDocApprovalOptions(0)">
			<label for="docApprovalOpt_original">Approve using the <b>original file</b></label>
		</div>
		<div style="margin-bottom:10px;">
			<input type="radio" name="docApprovalOpt" id="docApprovalOpt_new" value="new" onclick="toggleDocApprovalOptions(1)">
			<label for="docApprovalOpt_new">Approve by <b>uploading a new file</b></label>
		</div>
		<table cellpadding="2" style="display:none;margin-top:10px;margin-left:20px;" class="newDocApproveOptions">
			<tr>
				<td>Document:</td>
				<td></td>
				<td><input type="file" id="newDepoDocFile" name="newDepoDocFile"></td>
			</tr>
		</table>
		<cfif request.opt3 is 1>
			<div style="margin-bottom:10px;">
				<input type="radio" name="docApprovalOpt" id="docApprovalOpt_merge" value="merge" onclick="toggleDocApprovalOptions(0)">
				<label for="docApprovalOpt_merge"><span id="attachmergelabel">Approve by <b>merging the <span id="numselattach"></span> selected attachments</b> in the order specified</span></label>
			</div>
		</cfif>

		<div id="frmDocButtons" style="margin-top:15px;padding-left:22px;">
			<button type="submit" name="btnApproveDoc" id="btnApproveDoc"><i class="fa-solid fa-circle-check"></i> Approve</button>
			<button type="button" name="btnCancel" id="btnCancel" onclick="top.closeBox();">Cancel</button>
		</div>
		<div id="frmDocSaveLoading" style="display:none;margin-top:10px;"><i class="fa-light fa-circle-notch fa-spin"></i> <b>Please Wait...</b></div>
	</form>
	</cfoutput>
<cfelse>
	No Rights.
</cfif>