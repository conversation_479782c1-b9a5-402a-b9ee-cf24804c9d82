<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/group/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. You only need to provide the keys for data you wish to update.<br/>
		<div style="margin-left:30px;">
			<i>group</i> - name of the group<br/>
			<i>groupcode</i> - unique group code for the group<br/>
			<i>description</i> - description of the group<br/>
			<i>manualassignments</i> - if manual assignments are allowed<br/>
			<i>badgebackgroundcolor</i> - badge background color<br/>
			<i>badgetextcolor</i> - badge text color<br/>
			<i>applybadgecolortochildren</i> - apply this group's colors to all child groups (default FALSE)<br/>
			<i>parentapi_id</i> - this group's new parent group api_id<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 33

{
    "group": "My Test Group ABC"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 GROUP NOT FOUND</td><td>invalid group api_id</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error updating group</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "result": "Group updated.",
        "group": {
            "group": "My Test Group ABC"
            "groupcode": "TestA",
            "grouppath": "My Test Groups \\ My Test Group ABC",
            "description": "",
            "systemgroup": 0,
            "alertifpopulated": 0,
            "manualassignments": 0,
            "protected": 0,
            "membercount": 0,
            "badgebackgroundcolor": "#3b3e66",
            "badgetextcolor": "#ffffff",
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "parentapi_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
            "x-parent-api-uri": "/v1/group/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY"
        }
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update group.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>