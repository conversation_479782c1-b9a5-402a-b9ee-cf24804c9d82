﻿<a name="v1-authenticate"></a>
<section id="v1-authenticate">	
	<h3>/authenticate</h3>
	<p>POST - Returns an API token from your API credentials. API tokens expire 30 minutes after they are generated.</p>

	<h4>Required Request Headers</h4>
	<p>content-type: application/json</p>

	<h4>Methods</h4>
	<div class="get">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="post method-example">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">/v1/authenticate</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Required Request Details</div>
		<div class="jsonblock-info">
			The request must contain a JSON object in the body with two keys, <i>MCAPIKeyPublic</i> and <i>MCAPIKeySecret</i>.<br/>
			<div style="margin-left:30px;">
				<i>MCAPIKeyPublic</i> is your API Public Key. You can retrieve this in Control Panel under API Tokens.<br/>
				<i>MCAPIKeySecret</i> is your API Secret Key. This was provided to you when the API Token was created.
			</div>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
POST /v1/authenticate HTTP/1.1
Content-Type: application/json
Host: api.membercentral.com
Content-Length: 112

{
    "MCAPIKeyPublic": "Your API Public Key",
    "MCAPIKeySecret": "Your API Secret Key"
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 UNAUTHORIZED</td><td>invalid keys</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>missing or invalid JSON body</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "token":"YOUR_API_TOKEN",
        "tokenexpires":"MMMM, DD YYYY HH:MM:SS -0700",
        "tokenname":"Your token name"
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
401 UNAUTHORIZED
	
{
    "data": {},
    "error": true,
    "messages": [
        "Login failed."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>			

	<div class="delete">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>