<div class="post method-example">
    <div class="method-wrapper">
        <div class="method">POST</div>
        <div class="method-text">
            <div style="float:left;">/v1/member/{membernumber}/merge</div>
            <div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
        </div>
    </div>
</div>
<div class="method-exampleCode" style="display:none;">
    <div class="jsonblock-head">Required Request Details</div>
    <div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>membernumber</i> - an array of one or more membernumbers you want to merge into this member record (required)<br/>
			<i>memberdata</i> - a data structure that will be saved to the new merged member record (optional). This is the same data structure in POST /member/{membernumber}<br/>
            If your JSON object contains invalid memberdata subkeys, an <i>ignoredfields</i> array is returned in the response.
		</div>
	</div>

    <div class="jsonblock-head">Sample Request</div>
    <div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/merge HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{
    "membernumber": [ "SAMPLE123456DUPE1", "SAMPLE123456DUPE2" ],
    "memberdata": {
       "firstname": "Jane",
       "lastname": "Doe",
       ...
    }
}
</pre>
    </div>
    <div class="jsonblock-head">Possible Response Codes</div>
    <div class="jsonblock-table">
        <table>
        <tr><td class="rc">201 CREATED</td><td>success</td></tr>
        <tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
        <tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
        <tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
        <tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
        <tr><td class="rc">500 NOT MERGED</td><td>unable to merge members</td></tr>
        </table>
    </div>
    <div class="jsonblock-head">Sample Response (success)</div>
    <div class="jsonblock">
<pre class="prettyprint">
200 OK
{
"data": {
    "result": "Merged Members",
    "membernumber": "SAMPLE123456",
    "report": {
        "groups": {
            "summary": "The new member account will have 2 manual group assignments.",
            "assignedgroups": [
                "All Members",
                "All Practice Area Members"
            ]
        },
        "events": {
            "summary": "The new member account will have 1 duplicate event registrations.",
            "events": [
                "12/10/22 - Holiday Seminar"
            ]
        }
    },
    "ignoredfields": [
        "contacttype"
    ]
},
"error":false,
"messages":[]
}
</pre>
    </div>
    <div class="jsonblock-head">Sample Response (failure)</div>
    <div class="jsonblock">
<pre class="prettyprint">
500 NOT MERGED

{
"data": {},
"error": true,
"messages": [
    "Unable to merge members.",
    ...
]
}
</pre>
    </div>
</div>
<div style="clear:both;padding-top:10px;"></div>