<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. You only need to provide the keys for data you wish to update.<br/>
		<div style="margin-left:30px;">
			<i>source</i> - source of the referral<br/>
			<i>othersource</i> - other source name (valid only if the value of source is 'Other')<br/>
			<i>language</i> - referral language<br/>
			<i>legalissue</i> - issue description<br/>
			<i>agreesurvey</i> - if client agrees to receive surveys regarding the case? (true or false)<br/>
			<i>agreenewsletter</i> - if client agrees to receive e-mails regarding Newsletters and/or Blog updates? (true or false)<br/>
			<i>client</i> is a structure of subkeys used to update client information. Eligible subkeys are:<br/>
			<div style="margin-left:30px;">
				<i>clientfirstname</i> - client's first name<br/>
				<i>clientmiddlename</i> - client's middle name<br/>
				<i>clientlastname</i> - client's last name<br/>
				<i>clientcompany</i> - client's business name<br/>
				<i>clientaddress1</i> - client's address line 1<br/>
				<i>clientaddress2</i> - client's address line 2<br/>
				<i>clientcity</i> - client's city<br/>
				<i>clientstateprov</i> - client's state<br/>
				<i>clientpostalcode</i> - client's zip code<br/>
				<i>clientemail</i> - client's email<br/>
				<i>clientphonehome</i> - client's home phone<br/>
				<i>clientphonecell</i> - client's cell phone<br/>
				<i>clientphonealternate</i> - client's alternate phone<br/>
			</div>
			<i>representative</i> is a structure of subkeys used to update representative information. Eligible subkeys are:<br/>
			<div style="margin-left:30px;">
				<i>repfirstname</i> - representative's first name<br/>
				<i>replastname</i> - representative's last name<br/>
				<i>relationtoclient</i> - representative's relation to client<br/>
				<i>repaddress1</i> - representative's address line 1<br/>
				<i>repaddress2</i> - representative's address line 2<br/>
				<i>repcity</i> - representative's city<br/>
				<i>repstateprov</i> - representative's state<br/>
				<i>reppostalcode</i> - representative's zip code<br/>
				<i>repemail</i> - representative's email<br/>
				<i>repphonehome</i> - representative's home phone<br/>
				<i>repphonecell</i> - representative's cell phone<br/>
				<i>repphonealternate</i> - representative's alternate phone<br/>
			</div>
			<i>call</i> is a structure of subkeys used to update call information. Eligible subkeys are:<br/>
			<div style="margin-left:30px;">
				<i>calltype</i> - call type name<br/>
			</div>
			<i>agencyname</i> - name of the agency (valid only if calltype is 'Referral to Agency')<br/>
			<i>otheragency</i> - new agency name (valid only if calltype is 'Referral to Agency')<br/>
			<i>feetype</i> - fee type name (valid only if case exists and Allow Fee Type Management setting is enabled)<br/>
			<i>referralstatus</i> - status of referral (valid only if case does not exists)<br/>
			<i>case</i> is a structure of subkeys used to update case information (valid only if case exists). Eligible subkeys are:<br/>
			<div style="margin-left:30px;">
				<i>status</i> - status of case<br/>
				<i>feesreportedbyclient</i> - fees reported by client<br/>
			</div>
			<i>filters</i> is a structure of subkeys used to update referral filters (valid only if isreferred is 0 and calltype is not 'Referral to Agency'). Eligible subkeys are:<br/>
			<div style="margin-left:30px;">
				<i>primarypanel</i> - primary panel name<br/>
				<i>primarysubpanel</i> - primary sub-panel names separated by pipe symbol<br/>
				<i>secondarypanel</i> - secondary panel name<br/>
				<i>secondarysubpanel</i> - secondary sub-panel names separated by pipe symbol<br/>
				<i>tertiarypanel</i> - tertiary panel name<br/>
				<i>tertiarysubpanel</i> - tertiary sub-panel names separated by pipe symbol<br/>
				<i>customfields</i> - an array of member fields (present in the 'Field Set for Filter Tab' selection) passed in as label and value pairs<br/>
				<i>classifications</i> - an array of defined classifications entries passed in as label and value pairs<br/>
			</div>
			<i>attorneycustomfields</i> - an array of custom fields (Attorney Fields) passed in as field_api_id and value pairs (valid only if isreferred is 1), with no grouping specified.<br/>
			<i>clientcustomfields</i> - an array of custom fields (Extra Information) passed in as field_api_id and value pairs, with no grouping specified.<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request - Not Referred</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 1511

{
    "source":"Website",
    "language": "English",
    "legalissue": "Family Law: Divorce with Children and Custody Matters",
    "agreenewsletter": 1,
    "client": {
        "clientmiddlename": "Elizabeth",
        "clientphonealternate": "***********"
    },
    "representative": {
        "repfirstname": "Michael",
        "replastname": "Smith",
        "relationtoclient": "Brother",
        "repemail": "<EMAIL>",
        "repcity": "Anytown",
        "repstateprov": "Texas",
        "reppostalcode": "78735",
        "repphonecell": "************"
    },
    "call": {
        "calltype": "Lawyer Referral"
    },
    "filters": {
        "primarypanel": "Family Law",
        "primarysubpanel": "Custody and Modification|Child Support",
        "secondarypanel": "Guardianship",
        "customfields": [
            {
                "value": "Texas",
                "label": "Primary Address State"
            },
            {
                "value": "English|German|French",
                "label": "Languages"
            },
            {
                "value": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
                "label": "Earliest Bar Date"
            }
        ],
        "classifications": [
            {
                "value": "Guardianship|Administrative Law|Civil Litigation",
                "label": "Areas of Practice"
            },
            {
                "value": "5-10 Years in Practice",
                "label": "Membership Level"
            }
        ]
    },
    "attorneycustomfields": [
        {
            "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "value": "10/01/2024"
        },
        {
            "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "value": "Law School|Courthouse"
        }
    ],
    "clientcustomfields": [
        {
            "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "value": "No"
        },
        {
            "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "value": "Telephone|Email|Website"
        }
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Request - Case Exists</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 203

{
    "source":"Other",
    "othersource":"Former Clients",
    "legalissue": "Criminal Defense - Assault Charges",
    "agreenewsletter":1,
    "feetype":"Hourly Fee",
    "case": {
        "status": "Closed - Retained",
        "feesreportedbyclient": "1500.00"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 REFERRAL NOT FOUND</td><td>invalid referral api_id</td></tr>
		<tr><td class="rc">406 NOT ACCEPTABLE</td><td>invalid input data</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error updating referral</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "ignoredfields": [],
        "referral": {
            "calldate": "1/1/2024 11:04 AM",
            "lastupdateddate": "3/1/2024 4:16 PM",
            "referraldate": "1/4/2024 11:08 AM",
            "caseopendate": "2/1/2024 2:00 PM",
            "casecloseddate": "3/1/2024 4:16 PM",
            "source":"Website",
            "language": "English",
            "legalissue": "Family Law: Divorce with Children and Custody Matters",
            "agreesurvey": 1,
            "agreenewsletter": 1,
            "isreferred": 0,
            "caseexists": 0,
            "client": {
                "clientfirstname": "Sally",
                "clientmiddlename": "Elizabeth",
                "clientlastname": "Smith",
                "clientcompany": "",
                "clientemail": "<EMAIL>",
                "clientaddress1": "123 AnyStreet",
                "clientaddress2": "",
                "clientcity": "Anytown",
                "clientstateprov": "Texas",
                "clientpostalcode": "78735",
                "clientphonehome": "",
                "clientphonecell": "************",
                "clientphonealternate": "***********"
            },
            "representative": {
                "repfirstname": "Michael",
                "replastname": "Smith",
                "repaddress1": "",
                "repaddress2": "",
                "repcity": "Anytown",
                "repstateprov": "Texas",
                "reppostalcode": "78735",
                "repemail": "<EMAIL>",
                "repphonehome": "",
                "repphonecell": "************",
                "repphonealternate": "",
                "relationtoclient": "Brother"
            },
            "counselor": {
                "counselorfirstname": "Tammy",
                "counselorlastname": "Thompson",
                "counselormembernumber": "ABC123456"
            },
            "call": {
                "calltype": "Lawyer Referral",
                "callid": "XXXXXXXX-XXXX-XXXX-XXXXXXXXXXXXXXXX"
            },
            "clientfeesinfo": {
                "clientfees": [
                    {
                        "date": "9/11/2024",
                        "fee": " $8.00",
                        "amounttobepaid": " $0.00",
                        "paidtodate": " $8.00"
                    }
                ],
                "clientfeestotals": {
                    "fee": " $8.00",
                    "amounttobepaid": " $0.00",
                    "paidtodate": " $8.00"
                }
            },
            "counselornotes": [
                {
                    "enteredby": "John Doe",
                    "createddate": "09/26/2024 12:00 AM",
                    "followupstatus": "Completed",
                    "followupdate": "10/26/2024",
                    "description": "This is a test counselor note."
                },
                ...
            ],
            "agencyname": "Legal Shield",
            "referralstatus": "Pending - Referral NOT sent",
            "filters": {
                "primarypanel": "Criminal Law",
                "primarysubpanel": "Bank Robbery|Tax Evasion",
                "secondarypanel": "",
                "secondarysubpanel": "N/A",
                "tertiarypanel": "",
                "tertiarysubpanel": "N/A",
                "customfields": [
                    {
                        "value": "John",
                        "label": "First Name"
                    },
                    {
                        "value": "01/01/1988",
                        "label": "Date Of Birth"
                    },
                    {
                        "value": "English|German|French",
                        "label": "Languages"
                    },
                    ...
                ],
                "classifications": [
                    {
                        "value": "Board Members",
                        "label": "Classification"
                    },
                    {
                        "value": "Less than 5 Years in Practice|Over 10 Years in Practice",
                        "label": "Membership Level"
                    },
                    ...
                ]
            },
            "questionanswerpath": "Select a Panel / Administrative / Yes or No / Yes /",
            "clientcustomfields": {
                "fieldgroupings": [
                    {
                        "ungrouped": 1,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "Attorney referred me",
                                "label": "How did you hear about us?"
                            },
                            ...
                        ],
                        "groupname": ""
                    },
                    {
                        "ungrouped": 0,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "No",
                                "label": "Do you need special accommodations?"
                            },
                            ...
                        ],
                        "groupname": "Optional Information"
                    },
                    ...
                ]
            }
        }
        "result": "Referral updated."
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response - Invalid Input (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
406 NOT ACCEPTABLE

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update referral.",
        "Invalid source.",
        "Invalid representative state.",
        "Invalid call type."
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response - Internal Error (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update referral."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>