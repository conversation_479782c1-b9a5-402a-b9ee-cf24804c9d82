<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
		</cfscript>

		<cfset local.methodToRun = this[arguments.event.getValue('mca_ta')]>
		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>
	
	<cffunction name="search" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.rc = arguments.event.getCollection();
		local.editURL = buildCurrentLink(arguments.event,"edit");
		local.editInfoURL = buildCurrentLink(arguments.event,"editInfo") & "&mode=direct";
		local.listURL = buildCurrentLink(arguments.event,"list");
		local.objMemberSettingsAdmin = createObject("component","model.admin.membersettings.membersettingsadmin");

		if (NOT checkRights(arguments.event,'SearchOrg') AND NOT checkRights(arguments.event,'SearchAll')) {
			local.messageURL = buildCurrentLink(arguments.event,"message");
			application.objCommon.redirect('#local.messageURL#&message=1');
		}

		// Setup Default Form Params
		arguments.event.paramValue('orgID','');

		// add search params to applicationReservedURLParams
		local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='search');
		local.fieldsetInfo = StructNew();
		local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName;
		local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat;
		local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp;
		
		local.xmlFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberAdminSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		variables.applicationReservedURLParams = reReplaceNoCase(listAppend(variables.applicationReservedURLParams,replaceNoCase(arrayToList(local.arrFieldCodes),'<?xml version="1.0" encoding="UTF-8"?>','','ALL')),"\s+","","ALL");
		
		local.jsValidation = "";
		local.showReqFlag = false;

		local.qryGetClassifications = local.objMemberSettingsAdmin.getClassifications(siteResourceID=this.siteResourceID);

		local.qrySettings = CreateObject("component","model.admin.accountLocator.accountLocatorAdmin").getAccountLocatorSettings(local.rc.mc_siteinfo.siteID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_search.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getLastMembersViewed" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = {}>

		<cftry>
			<cfquery name="local.strReturn['members']" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;

				SELECT top 5 m2.memberid as memberid, m2.LastName + ', ' + m2.FirstName + isnull(' ' + nullif(m2.middlename,''),'') as membername, m2.membernumber, m2.company, m2.hasmemberphotothumb
				from dbo.ams_viewedMemberTimes as vmt
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = vmt.memberID 
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID and m2.status <> 'D'
				where vmt.viewedOrgID = @orgID
				and vmt.adminID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
				group by m2.memberid, m2.LastName + ', ' + m2.FirstName + isnull(' ' + nullif(m2.middlename,''),''), m2.membernumber, m2.company, m2.hasMemberPhotoThumb
				order by max(vmt.lastViewedDateTime) desc;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strReturn["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.editInfoURL = buildCurrentLink(arguments.event,"editInfo") & "&mode=direct";
		local.numPerPageInSearchResults = xmlSearch(this.appInstanceSettings.settingsXML,'string(/settings/setting[@name="numPerPageInSearchResults"]/@value)');
		local.showMemberPhotosInSearchResults = xmlSearch(this.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)');
		if (local.showMemberPhotosInSearchResults neq "false") local.showMemberPhotosInSearchResults = true;

		if (NOT checkRights(arguments.event,'SearchOrg') AND NOT checkRights(arguments.event,'SearchAll')) {
			local.messageURL = buildCurrentLink(arguments.event,"message");
			application.objCommon.redirect('#local.messageURL#&message=1');
		}
				
		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		appendBreadCrumbs(arguments.event,{ link='', text='Member Search Results' });
		</cfscript>

		<!--- if posting from a search form, put form vars into url vars and redirect. --->
		<!--- helps create the real link as well as allows the back button to not cause a browser alert --->
		<cfif CGI.REQUEST_METHOD eq "post">
			<cfset local.re_directlink = buildCurrentLink(arguments.event,"list")>
			<cfset local.re_membersearchform = duplicate(form)>
			<cfset structDelete(local.re_membersearchform,"btnSubmit")>
			<cfset structDelete(local.re_membersearchform,"fieldnames")>
			<cfloop collection="#local.re_membersearchform#" item="local.currentitem">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode_radius',local.currentitem)>
					<cfif len(local.re_membersearchform[replaceNoCase(local.currentitem,'_radius','')])>
						<cfset local.re_directlink = local.re_directlink & "&" & lcase(local.currentitem) & "=" & urlEncodedFormat(local.re_membersearchform[local.currentitem])>
					</cfif>
				<cfelseif len(local.re_membersearchform[local.currentitem])>
					<cfset local.re_directlink = local.re_directlink & "&" & lcase(local.currentitem) & "=" & urlEncodedFormat(local.re_membersearchform[local.currentitem])>
				</cfif>
			</cfloop>
			<cflocation url="#local.re_directlink#" addtoken="no">
		</cfif>

		<!--- support for paging --->
		<cfset arguments.event.setValue('memPageNum',int(val(arguments.event.getValue('memPageNum',1))))>
		<cfset local.paging = { 
			link="/?#ReReplaceNoCase(cgi.QUERY_STRING,'&memPageNum=[0-9]+','','ALL')#",
			rowsize=local.numPerPageInSearchResults,
			currPage=arguments.event.getValue('memPageNum'),
			nextPage=arguments.event.getValue('memPageNum') + 1,
			prevPage=arguments.event.getValue('memPageNum') - 1
			}>

		<!--- get search and result fields --->
		<cfset local.objMFS = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.qrySearchFieldsetID = getLocatorFieldsetID(siteResourceID=this.siteResourceID,area='search')>
		<cfset local.qryResultsFieldsetID = getLocatorFieldsetID(siteResourceID=this.siteResourceID,area='results')>
		<cfset local.MemberAdminSRID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID')>
		
		<!--- get siteResourceID for classifications --->
		<cfquery name="local.qryGetClassificationsSearch" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select c.ClassificationID, c.groupSetID
			from dbo.ams_classifications c
			inner join dbo.ams_memberGroupSets as mgs on mgs.groupSetID = c.groupSetID
			where c.siteResourceID = <cfqueryparam value="#local.MemberAdminSRID#" cfsqltype="cf_sql_integer">
			and c.allowSearch = <cfqueryparam value="1" cfsqltype="cf_sql_bit">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- determine search conditions --->
		<cfset local.strSearchConditions = local.objMFS.getSQLSearchConditionsFromFieldSet(fieldsetID=local.qrySearchFieldsetID.fieldsetID, fieldsetUsage="memberAdminSearch", event=arguments.event)>
		<cfset structInsert(local.strSearchConditions.sqlParams,'orgid',{ value=arguments.event.getValue('mc_siteinfo.orgid'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'resultsFSID',{ value=local.qryResultsFieldsetID.fieldsetID, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'startrow',{ value="#(local.paging.prevPage*local.paging.rowsize)+1#", cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'endrow',{ value="#(local.paging.prevPage*local.paging.rowsize)+local.paging.rowsize#", cfsqltype="CF_SQL_INTEGER" })>

		<cfsavecontent variable="local.stClassificationJoin">
			<cfloop query="local.qryGetClassificationsSearch">
				<cfif len(trim(arguments.event.getValue("mg_gid_#local.qryGetClassificationsSearch.groupSetID#",'')))>
					<cfoutput>
					inner join dbo.cache_members_groups as mg#local.qryGetClassificationsSearch.classificationID# on mg#local.qryGetClassificationsSearch.classificationID#.orgID = @orgID AND mg#local.qryGetClassificationsSearch.classificationID#.memberID = m.memberID
					inner join dbo.ams_groups AS g#local.qryGetClassificationsSearch.classificationID# on g#local.qryGetClassificationsSearch.classificationID#.groupID = mg#local.qryGetClassificationsSearch.classificationID#.groupID
						and g#local.qryGetClassificationsSearch.classificationID#.status <> 'D'
					inner join dbo.ams_memberGroupSetGroups mgsg#local.qryGetClassificationsSearch.classificationID# on mgsg#local.qryGetClassificationsSearch.classificationID#.groupID = mg#local.qryGetClassificationsSearch.classificationID#.groupID
						and mgsg#local.qryGetClassificationsSearch.classificationID#.groupSetGroupID in (#arguments.event.getValue("mg_gid_#local.qryGetClassificationsSearch.groupSetID#")#)
					inner join dbo.ams_memberGroupSets mgs#local.qryGetClassificationsSearch.classificationID# on mgs#local.qryGetClassificationsSearch.classificationID#.groupSetID = mgsg#local.qryGetClassificationsSearch.classificationID#.groupSetID
						and mgs#local.qryGetClassificationsSearch.classificationID#.groupSetID in (#local.qryGetClassificationsSearch.groupSetID#)
					inner join dbo.ams_Classifications as c#local.qryGetClassificationsSearch.classificationID# on c#local.qryGetClassificationsSearch.classificationID#.groupSetID = mgs#local.qryGetClassificationsSearch.classificationID#.groupSetID
						and c#local.qryGetClassificationsSearch.classificationID#.classificationID = #local.qryGetClassificationsSearch.classificationID#
					</cfoutput>
				</cfif>
			</cfloop>
		</cfsavecontent>

		<cfset local.qryMembers = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalMatches int, @orgID int, @resultsFSID int, @startrow int, @endrow int, @outputFieldsXML xml;
			set @orgID = :orgid;
			set @resultsFSID = :resultsFSID;
			set @startrow = :startrow;
			set @endrow = :endrow;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL 
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAMembers') IS NOT NULL 
				DROP TABLE ##tmpMAMembers;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL 
				DROP TABLE ##tmpMAResults;
			CREATE TABLE ##tmpMAMemberIDs (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpMAMembers (memberID int PRIMARY KEY, lastName varchar(75), firstName varchar(75), membernumber varchar(50), 
				MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), mc_row int);
			CREATE TABLE ##tmpMAResults (MFSAutoID int IDENTITY(1,1) not null);

			#local.strSearchConditions.stFilterPrep#

			insert into ##tmpMAMemberIDs (memberID)
			select distinct m.memberID
			from dbo.ams_members as m 
			#local.stClassificationJoin#
			where m.orgID = @orgID
			and m.status in ('A','I')
			and m.isProtected = 0
			#local.strSearchConditions.stFilter#;

			#local.strSearchConditions.stFilterPost#

			select @totalMatches = count(*) from ##tmpMAMemberIDs;

			insert into ##tmpMAMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company, mc_row)
			select m.memberID, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company,
				ROW_NUMBER() OVER (order by m.lastname, m.firstname, m.membernumber) as mc_row
			from dbo.ams_members as m
			inner join ##tmpMAMemberIDs as tmpM on tmpM.memberID = m.memberID
			where m.orgID = @orgID;

			delete from ##tmpMAMembers
			where mc_row not between @startrow and @endrow;

			-- get members fieldset data and set back to snapshot because proc ends in read committed
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, 
				@existingFields='m_lastname,m_firstname,m_membernumber,m_company,m_status', @ovNameFormat=NULL, 
				@ovMaskEmails=NULL, @membersTableName='##tmpMAMembers', @membersResultTableName='##tmpMAResults', 
				@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			-- return @outputFieldsXML in only the first row to reduce the query payload
			SELECT *, CASE WHEN tmp.mc_row = @startrow THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
			FROM (
				SELECT m.lastname, m.firstname, m.membernumber, m.MCAccountStatus, m.hasMemberPhotoThumb, m.company, tmpM.*, @totalMatches as mc_totalMatches, m.mc_row
				FROM ##tmpMAMembers AS m 
				INNER JOIN ##tmpMAResults AS tmpM ON tmpM.memberID = m.memberID
				) AS tmp
			ORDER BY tmp.mc_row;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL 
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAMembers') IS NOT NULL 
				DROP TABLE ##tmpMAMembers;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL 
				DROP TABLE ##tmpMAResults;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			local.strSearchConditions.sqlParams, { datasource=application.dsn.memberCentral.dsn, result="local.qryMembersResult" }
		)>

		<cfif local.qryMembers.mc_totalMatches is 1>
			<cflocation url="#buildCurrentLink(arguments.event,"edit")#&memberID=#local.qryMembers.memberid#" addtoken="no">
		<cfelseif local.qryMembers.recordCount gt 0>
			<cfset local.xmlResultFields = local.qryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'), includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>

			<cfif local.paging.currpage eq 1>
				<cfset local.currCountStart = 1>
				<cfset local.currCountStop 	= local.paging.rowsize>
			<cfelse>
				<cfset local.currCountStart = local.paging.rowSize * (local.paging.currpage - 1) + 1>
				<cfset local.currCountStop 	= local.paging.rowsize * (local.paging.currpage - 1) + local.qryMembers.recordCount>
			</cfif>

			<cfset local.arrAllClassifications = getClassificationsForMemberIDList(siteResourceID=local.MemberAdminSRID, memberIDList=valueList(local.qryMembers.memberid))>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_search_results.cfm">
			</cfsavecontent>
		<cfelse>
			<cflocation url="#buildCurrentLink(arguments.event,"search")#&err=1" addtoken="no">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getClassificationsForMemberIDList" access="public" output="false" returntype="array">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="memberIDList" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.classifications = arrayNew(1)>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getClassificationsBySiteResourceID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			<cfprocresult name="local.getClassifications" resultset="1">
		</cfstoredproc>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getGroupsByMemberAndClassifications">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.memberIDList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#ValueList(local.getClassifications.classificationID)#">
			<cfprocresult name="local.qryMemberClassifications" resultset="1">
		</cfstoredproc>

		<cfloop query="local.getClassifications">
			<cfset local.thisGroupName = replace(reReplace(local.getClassifications.name,"[^a-zA-Z0-9]"," ","ALL"),' ','_','ALL')>
			
			<cfquery name="local.qryClass" dbtype="query">
				select memberID, groupID, imageExt, groupName, groupDesc, classificationID
				from [local].qryMemberClassifications
				where classificationID = #local.getClassifications.classificationID#
				and showInSearchResults = 1
			</cfquery>

			<cfset local.classificationsStruct = structNew()>
			<cfset structInsert(local.classificationsStruct,"qryClass",local.qryClass)>
			<cfset structInsert(local.classificationsStruct,"name",replace(reReplace(local.getClassifications.name,"[^a-zA-Z0-9]"," ","ALL"),' ','_','ALL'))>
			<cfset arrayAppend(local.classifications,duplicate(local.classificationsStruct))>
		</cfloop>

		<cfreturn local.classifications>
	</cffunction>

	<cffunction name="getMemberClassifications" access="public" output="false" returntype="array">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="area" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.classifications = arrayNew(1)>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getClassificationsBySiteResourceID">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			<cfprocresult name="local.getClassifications" resultset="1">
		</cfstoredproc>

		<cfif len(arguments.area)>
			<cfset var area = arguments.area>
			<cfset local.getClassifications = local.getClassifications.filter(
				function(thisRow){ 
					return arguments.thisRow.area eq area;
				})>
		</cfif>

		<cfloop query="local.getClassifications">
			<cfset local.thisGroupName = replace(reReplace(local.getClassifications.name,"[^a-zA-Z0-9]"," ","ALL"),' ','_','ALL')>
			
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getGroupsByMemberAndClassification">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.getClassifications.classificationID#">
				<cfprocresult name="local.qryClass" resultset="1">
			</cfstoredproc>

			<cfset local.classificationsStruct = structNew()>
			<cfset structInsert(local.classificationsStruct,"qryClass",local.qryClass)>
			<cfset structInsert(local.classificationsStruct,"name",local.thisGroupName)>
			<cfset arrayAppend(local.classifications,duplicate(local.classificationsStruct))>
		</cfloop>

		<cfreturn local.classifications>
	</cffunction>
	
	<cffunction name="getOrgAdditionalDataColumns" access="private" output="false" returntype="xml">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="cache" type="numeric" required="no" default="0">

		<cfset var local = structNew()>

		<cfif arguments.cache gt 0>
			<cfquery name="local.xmlDataColumns" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,0,arguments.cache)#">
				EXEC dbo.ams_getOrgMemberDataColumns @orgID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#">
			</cfquery>
		<cfelse>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getOrgMemberDataColumns">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocresult name="local.xmlDataColumns" resultset="1">
			</cfstoredproc>
		</cfif>

		<cfreturn XMLParse(local.xmlDataColumns.additionalDataXML)>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct" hint="Edit Member">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.rc = arguments.event.getCollection();
		local.objMember = CreateObject("component","members");

		// get member 
		local.origMemberID = int(val(arguments.event.getValue('memberID',0)));
		local.strMember = structNew();
		local.strMember.qryMember = local.objMember.getMember_demo(memberid=local.origMemberID);
		local.rc.memberID = val(local.strMember.qryMember.memberID);

		// if queried memberID is different than the one passed in, it was merged and we need to redirect to the active memberID
		if (local.rc.memberID gt 0 && local.rc.memberID neq local.origMemberID) 
			application.objCommon.redirect('#buildCurrentLink(arguments.event,"edit")#&memberID=#local.rc.memberID#');

		// if no member found 
		if (local.rc.memberID is 0) 
			application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=2');
		// protected member
		else if (local.strMember.qryMember.isProtected is 1)
			application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=6');
		</cfscript>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.hasPrefix, o.usePrefixList, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- is member in queue to be processed? --->
		<cfset local.memberInQueue = false>
		<cfif local.objMember.isMemberInQueue(orgID=local.rc.mc_siteinfo.orgID,memberID=local.rc.memberID) is 1>
			<cfset local.memberInQueue = true>
		</cfif>

		<!--- is member in update queue to be processed? --->
		<cfset local.memberInUpdateQueue = false>
		<cfif local.objMember.isMemberInUpdateQueue(memberID=local.rc.memberID) is 1>
			<cfset local.memberInUpdateQueue = true>
		</cfif>

		<!--- Build printed name --->
		<cfsavecontent variable="local.memberNamePrinted">
			<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.strMember.qryMember.prefix)><cfoutput>#local.strMember.qryMember.prefix# </cfoutput></cfif>
			<cfoutput>#local.strMember.qryMember.firstname# </cfoutput>
			<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.strMember.qryMember.middlename)><cfoutput>#local.strMember.qryMember.middlename# </cfoutput></cfif>
			<cfoutput>#local.strMember.qryMember.lastname# </cfoutput>
			<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.strMember.qryMember.suffix)><cfoutput>#local.strMember.qryMember.suffix# </cfoutput></cfif>
			<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.strMember.qryMember.professionalsuffix)><cfoutput>#local.strMember.qryMember.professionalsuffix#</cfoutput></cfif>
		</cfsavecontent>
		<cfset local.memberNamePrinted = ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>

		<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=local.rc.mc_siteinfo.orgID)>
		<cfset local.qryLists = getLists(local.rc.mc_siteinfo.siteID)>

		<cfset local.MemSetSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MemberSettingsAdmin',siteID=local.rc.mc_siteinfo.siteID)>
		<cfset local.tmpMemSetRights = buildRightAssignments(siteResourceID=local.MemSetSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=local.rc.mc_siteinfo.siteID)>

		<cfset local.viewSuppressionListEmailsURL = buildCurrentLink(arguments.event,"viewSuppressionListEmails") & "&orgID=#local.rc.mc_siteinfo.orgID#&siteID=#local.rc.mc_siteinfo.siteID#&memberID=#local.rc.memberID#&mode=stream">
		<cfset local.viewListTroubleshooterIssuesURL = buildCurrentLink(arguments.event,"viewListTroubleshooterIssues") & "&mode=direct">
		<cfset local.editSuppressedEmailURL = buildCurrentLink(arguments.event,"editSuppressedEmail") & "&memberID=#local.rc.memberID#&mode=stream">
		<cfset local.hasSuppressedEmails = CreateObject('component', 'model.admin.emailBlast.suppressionListEmails').hasSuppressedEmailsForMember(siteID=local.rc.mc_siteinfo.siteID,memberID=local.rc.memberID)>

		<!--- Build breadCrumb Trail --->
		<cfset appendBreadCrumbs(arguments.event,{ link='', text='#local.strMember.qryMember.memberNumber# - #encodeForHTML(local.memberNamePrinted)#' })>

		<!--- tabs --->
		<cfset var currTab = arguments.event.getValue('tab','profile')>
		<cfif currTab eq ''><cfset currTab = 'profile'></cfif>
		<cfset local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','')>
		<cfset local.arrTabs = arrayNew(1)>
		<cfset local.mainEmail.email = ''>
		<cfif structKeyExists(local.tmpMemSetRights,"ViewMain") and local.tmpMemSetRights.ViewMain eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Main', id='profile', fn='dsp_memberForm_demographics' }>

			<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.rc.mc_siteinfo.orgID)>
			<cfset local.isMemberASuperUser = local.rc.mc_siteinfo.orgID EQ 1 AND isMemberASuperUser(memberID=local.rc.memberID) ? true : false>
			<cfset local.isMemberInLoginGroup = local.objMember.isMemberInLoginGroup(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID)>
			<cfif local.isMemberASuperUser>
				<cfset local.strMember.stUserName = application.objUser.login_getUsernameSuperUser(memberid=local.rc.memberID)>
			<cfelse>
				<cfset local.strMember.stUserName = application.objUser.login_getUsername(memberid=local.rc.memberID, siteID=local.rc.mc_siteinfo.siteID)>
			</cfif>
			<cfset local.strMember.qryDefaultLogins = local.objMember.getMember_defaultlogins(memberID=local.rc.memberID)>
			<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.rc.mc_siteinfo.orgcode, membernumber=local.strMember.qryMember.membernumber)>
			<cfset local.strMember.lastLoginDate = local.objMember.getMember_lastLogin(memberid=local.rc.memberID, siteID=local.rc.mc_siteinfo.siteID)>
			<cfset local.mainEmail = application.objMember.getMainEmail(memberID=local.rc.memberID)>
			<cfset local.qryFeatureImgs = local.objMember.getMember_groups(memberid=local.rc.memberID, orgID=local.rc.mc_siteinfo.orgID, featuredOnly=1)>
			<cfset local.showMemberPhotosInSearchResults = xmlSearch(this.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)') neq "false" ? 1 : 0>
			<cfset local.getLinkedRecordsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getLinkedRecords&memberID=#local.rc.memberID#&mode=stream">

			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfset local.subscriptionObj = CreateObject('component', 'model.admin.subscriptions.subscriptions')>
				<cfset local.qryCopyInfoFieldSet = getCustomFieldsetIDs(siteresourceID=this.siteResourceID, area="copyinfo", permissionMemberID=0)>
				<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.rc.mc_siteinfo.orgID)>
			
				<cfset local.subscriptionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getMemberSubscriptions&mode=stream&mid=#local.rc.memberID#&fstatus=A,P">
				
				<cfset local.strMember.memberMatchCount = local.objMember.getMatchingMembersCount(memberID=local.rc.memberID)>
				<cfset local.qryMemRoleTypes = local.objMember.getLinkedMemberRoleTypes(orgID=local.rc.mc_siteinfo.orgID, memberid=local.rc.memberID)>
				<cfset local.qryMemRecordTypes = local.objMember.getLinkedMemberRecordTypes(orgID=local.rc.mc_siteinfo.orgID, memberid=local.rc.memberID)>

				<cfset local.showLoginAsMember = 0>
				<cfif local.isMemberInLoginGroup 
					and local.strMember.qryMember.status eq "A" 
					and (
						(len(local.strMember.stUserName) and arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1) 
						or 
						arguments.event.getValue('mc_siteinfo.useRemoteLogin') is 1
					)
					and not local.isMemberASuperUser>
					<cfif session.cfcuser.memberdata.memberid eq local.strMember.qryMember.memberID>
						<cfset local.showLoginAsMember = -1>
					<cfelseif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) and isMemberInSiteAdminGroup(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID)>
						<cfset local.showLoginAsMember = -2>
					<cfelse>
						<cfset local.showLoginAsMember = 1>
					</cfif>
				</cfif>

				<cfset local.strCopyFieldsets = {}>
				<cfloop query="local.qryCopyInfoFieldSet">
					<cfset local.tmpStr = { name=encodeForHTML(local.qryCopyInfoFieldSet.fieldSetName), data=local.objMember.getCopyInfoData(orgID=local.rc.mc_siteinfo.orgID, mid=local.rc.memberID, fid=local.qryCopyInfoFieldSet.fieldsetID).data }>
					<cfset StructInsert(local.strCopyFieldsets,local.qryCopyInfoFieldSet.fieldsetID,local.tmpStr,true)>
				</cfloop>

				<!--- qualified login policy for this member --->
				<cfscript>
					local.hasLoginPolicy = CreateObject('component', 'model.admin.website.website').hasLoginPolicy(siteID=local.rc.mc_siteinfo.siteID);
					if (local.hasLoginPolicy) {
						local.objLogin = CreateObject("component","model.login.login");
						local.qryLoginPolicyMethods = local.objLogin.getQualifiedLoginPolicyMethod(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID);
						if (local.qryLoginPolicyMethods.recordCount) {
							local.arrConfiguredMethods = local.objLogin.getConfiguredLoginPolicyMethod(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID);
							local.qryRequiredPolicyMethods = QueryFilter(local.qryLoginPolicyMethods, (row) => arguments.row.isRequired EQ 1);
							local.requiredPolicyMethodIDs = valueList(local.qryRequiredPolicyMethods.policyMethodID);
							if (local.arrConfiguredMethods.containsNoCase('MFASMS'))
								local.MFAPhoneNumber = local.objLogin.getMFAPhoneNumber(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID);
						}
					}
				</cfscript>
			</cfif>
		</cfif>
		
		<cfif isXMLDoc(local.xmlAdditionalData) and arrayLen(local.xmlAdditionalData.XmlRoot.XmlChildren)
			and structKeyExists(local.tmpMemSetRights,"ViewCustom") and local.tmpMemSetRights.ViewCustom eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Custom', id='ad', fn='dsp_memberForm_additional' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfset local.saveADURL = buildCurrentLink(arguments.event,"saveAD") & "&mode=stream">
				<cfset local.hasDocCols = arrayLen(xmlSearch(local.xmlAdditionalData,"//column[@dataTypeCode='DOCUMENTOBJ']")) gt 0>
				<cfset local.saveDocURL = buildCurrentLink(arguments.event,"saveMemberCustomSingleDocument") & "&mode=stream">

				<cfscript>
				local.customFieldSetArray = ArrayNew(1);
				local.qryFieldSets = getCustomFieldsetIDs(siteResourceID=this.siteResourceID, area="custom", permissionMemberID=local.rc.memberID);
				if (local.qryFieldSets.recordCount) {
					local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
					local.count = 0;
					while (local.count lt local.qryFieldSets.RecordCount) {
						local.count = local.count + 1;
						local.tmpStruct = { fieldSetID=local.qryFieldSets.fieldSetID[local.count], xmlFields=local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldSets.fieldSetID[local.count]), usage="memberAdminCustom"), isApplicable = local.qryFieldSets.isApplicable[local.count] };
						arrayAppend(local.customFieldSetArray,local.tmpStruct);
					}
				}
				</cfscript>
			</cfif>
		</cfif>
		
		<cfif local.rc.mc_siteinfo.sf_contributions and structKeyExists(local.tmpMemSetRights,"ViewContributions") and local.tmpMemSetRights.ViewContributions eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Contributions', id='contributions', fn='dsp_memberForm_contributions' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfset local.objContributions = CreateObject('component', 'model.admin.contributions.contributions')>
				<cfset local.qryFrequencies = local.objContributions.getFrequencyOptions(siteID=local.rc.mc_siteinfo.siteID)>
				<cfset local.qryCPStatuses = local.objContributions.getCPStatuses()>
				<cfset local.qryProgramsForFilter = local.objContributions.getProgramsForFilterByMember(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID)>
				<cfset local.qryProgramsForAdd = local.objContributions.getProgramsForMemberToContrib(siteID=local.rc.mc_siteinfo.siteID, memberID=local.rc.memberID)>

				<cfset local.contributionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=contributionJSON&meth=getContributionList&dtMode=memberContribTab&memberID=#local.rc.memberID#&mode=stream">
				<cfset local.addContributionsLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='addContribution')>
				<cfset local.linkCCToContributorLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='linkCCToContributor') & "&mode=direct">
				<cfset local.viewContributionLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='viewContribution')>
				<cfset local.massEmailContributionsLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='massEmailContributions') & "&mode=direct">
				<cfset local.exportContributionsPromptLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='exportContributionsPrompt') & "&mode=direct&memberID=#local.rc.memberID#">
				<cfset local.cancelContributionLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='cancelContribution') & "&ret=member&mode=direct">
				<cfset local.deleteContributions = buildLinkToTool(toolType='ContributionAdmin',mca_ta='deleteContributions') & "&mode=direct">
				<cfset local.showContribInstallmentDetailsLink = buildLinkToTool(toolType='ContributionAdmin',mca_ta='showContribInstallmentDetails') & "&mode=direct">

				<cfset local.addPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct">
				<cfset local.allocatePaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct">
			</cfif>
		</cfif>

		<cfif local.rc.mc_siteinfo.sf_memberDocuments is 1 and structKeyExists(local.tmpMemSetRights,"ViewMemberDocuments") and local.tmpMemSetRights.ViewMemberDocuments eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Docs', id='documents', fn='dsp_memberForm_documents' }>			
			<cfset local.memberDocslink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getMemberDocuments&srID=#this.siteResourceID#&mode=stream&memberid=#local.strMember.qryMember.memberid#">
		</cfif>

		<cfif local.rc.mc_siteinfo.sf_subscriptions and structKeyExists(local.tmpMemSetRights,"ViewDues") and local.tmpMemSetRights.ViewDues eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Dues', id='subscriptions', fn='dsp_memberForm_subscriptions' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfset local.subAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('SubscriptionAdmin')>
				<cfset local.subExpireLink = local.subAdminTool & "&mca_ta=editMemberExpireSubscription&mid=#local.rc.memberID#&mode=direct">
				<cfset local.subRemoveLink = local.subAdminTool & "&mca_ta=editMemberRemoveSubscription&mid=#local.rc.memberID#&mode=direct">
				<cfset local.cleanupInvoicesLink = local.subAdminTool & "&mca_ta=editMemberCleanupInvoicesSubscription&mid=#local.rc.memberID#&mode=direct">

				<cfset local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin')>
				<cfset local.addPaymentURL = local.transAdminTool & "&mca_ta=addPayment&mode=direct">
				<cfset local.allocatePaymentURL = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct">
				<cfset local.refundPaymentURL = local.transAdminTool & "&mca_ta=refundPayment&tabMode=subscriptions&mode=direct">

				<cfset local.subLink = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='subscribe') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.editSubLink = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='manageSubscription') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.subConfirmRenewLink = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='confirmGenerateRenewals') & "&mode=direct">
				<cfset local.subProcessGenerateRenewals = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='processGenerateRenewals') & "&mode=direct">
				<cfset local.processGenerateOffers = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='processGenerateOffers') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.startGenerateOffers = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='startGenerateOffers') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.subAssocCC = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='linkCCToSubscriber') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.linkAcceptSubscription = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='linkAcceptSubscription') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.showPaperStatementsForm = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='showPaperStatementsForm') & "&mid=#local.rc.memberID#&mode=direct">
				<cfset local.subRenewalLink = buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='showSubRenewalLink') & "&mode=direct">
				<cfset local.subscriptionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscriptionsList&mode=stream&mid=#local.rc.memberID#">
				
				<!--- Check the subscriber added to queue. --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberInqueue">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select qid.subscriberID, s.memberID
					from platformQueue.dbo.queue_subscriptionAccept as qid
					inner join dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID  and s.memberID = #local.rc.memberID#
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qid.statusID
						and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done');

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.TransactionAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
				<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

				<cfset local.subscriptionObj = CreateObject('component', 'model.admin.subscriptions.subscriptions')>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionPaymentStatuses">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select statusCode, statusName
					from dbo.sub_paymentStatuses
					order by statusName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionStatuses">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select statusCode, statusName
					from dbo.sub_statuses
					order by statusName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFrequencies">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select frequencyID, frequencyName
					from dbo.sub_frequencies
					where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
					and status = 'A'
					order by frequencyName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfquery name="local.qrySubTypes" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select typeid, typeName
					from dbo.sub_types
					where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="cf_sql_integer">
					and status <> 'D' 
					order by typeName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
		
				<cfset local.historyObj = CreateObject('component', 'model.system.platform.history')>
				<cfset local.subAuditTrailFlag = arguments.event.getValue('subat','')>
				<cfif local.subAuditTrailFlag eq 'all'>
					<cfset local.memberSubAuditTrail = getMemberSubscriptionUpdateHistory(orgID=local.rc.mc_siteinfo.orgID, memberID=local.rc.memberID)>
				<cfelse>
					<cfset local.memberSubAuditTrail = getMemberSubscriptionUpdateHistory(orgID=local.rc.mc_siteinfo.orgID, memberID=local.rc.memberID, limit=10)>
				</cfif>
			</cfif>
		</cfif>

		<cfif structKeyExists(local.tmpMemSetRights,"viewEmailActivity") and local.tmpMemSetRights.viewEmailActivity eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Email Activity', id='emailactivity', fn='dsp_memberForm_emailActivity' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
					local.fromDt = dateFormat(dateAdd("yyyy",-1,now()), "m/d/yyyy");
					local.toDt = dateFormat(now(), "m/d/yyyy");

					local.recipientsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&mode=stream&meth=getEmailRecipients&gridMode=memberActivitiesGrid&selectedMemberID=#local.rc.memberID#";
					local.qryApplications = CreateObject('component', 'model.admin.emailBlast.emailBlast').getEmailMessageTypes(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
					local.resendEmailLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=resendEmail&mode=direct";
					local.downloadMessageAttachment = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=downloadMessageAttachment&mode=stream";

					local.objEmailPreferences = createObject("component","model.admin.emailPreferences.emailPreferences");
					local.qryOptOutLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out");
					local.qryOptInLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-In");

					if (local.qryOptOutLists.recordCount)
						local.inOptOutListCount = local.objEmailPreferences.getConsentListMemberCount(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.rc.memberID, listMode="Opt-Out");
					if (local.qryOptInLists.recordCount)
						local.inOptInListCount = local.objEmailPreferences.getConsentListMemberCount(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.rc.memberID, listMode="Opt-In");

					local.manageMemConsentListsLink = buildLinkToTool(toolType='EmailPreferencesAdmin',mca_ta='manageMemberConsentLists') & "&mID=#local.rc.memberID#&mode=direct";
				</cfscript>
			</cfif>
		</cfif>

		<cfset local.arrNodes = XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Events']/navitem/navitem[@navName='Events']")>
		<cfif structKeyExists(session.mcastruct.strNavKeys, local.arrNodes[1].xmlAttributes.navKey)
			and structKeyExists(local.tmpMemSetRights,"ViewEvents") and local.tmpMemSetRights.ViewEvents eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Events', id='events', fn='dsp_memberForm_events' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.objAdminEvent	= CreateObject("component","model.admin.events.event");
				local.selectEventLink = buildLinkToTool(toolType='EventAdmin',mca_ta='selectEventForAddRegistrant') & "&mode=direct";
				local.editEventLink = buildLinkToTool(toolType='EventAdmin',mca_ta='editEvent');
				local.removeRegistrantLink = buildLinkToTool(toolType='EventAdmin',mca_ta='removeReg') & "&mode=direct";
				local.cleanupInvoicesRegLink = buildLinkToTool(toolType='EventAdmin',mca_ta='cleanupInvoicesReg') & "&mode=direct";
				local.sendConfirmationLink = buildLinkToTool(toolType='EventAdmin',mca_ta='sendConfirmation') & "&mode=direct";
				local.sendMaterialsLink = buildLinkToTool(toolType='EventAdmin',mca_ta='sendRegistrantMaterials') & "&mode=direct";
				local.addRegLink = buildLinkToTool(toolType='EventAdmin',mca_ta='addReg') & "&bc=m&mode=direct";
				local.manageAttendanceCreditLink = buildLinkToTool(toolType='EventAdmin',mca_ta='manageAttendanceCredit') & "&mode=direct";
				local.viewCertificateLink = buildLinkToTool(toolType='EventAdmin',mca_ta='viewCertificate') & "&mode=direct";
				local.printRegLink = buildLinkToTool(toolType='EventAdmin',mca_ta='printReg') & "&mode=direct";
				local.eventRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getMemberEvents&mode=stream&memberID=#local.rc.memberID#";
				local.printBadgeRegistrantLink = buildLinkToTool(toolType='EventAdmin',mca_ta='printBadgeRegistrant') & "&mode=direct";

				local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
				local.addPaymentURL = local.transAdminTool & "&mca_ta=addPayment&mode=direct";
				local.allocatePaymentURL = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";
				local.refundPaymentURL = local.transAdminTool & "&mca_ta=refundPayment&tabMode=registrants&mode=direct";
				local.qryCalendars = local.objAdminEvent.getCalendarsForFilters(arguments.event.getValue('mc_siteInfo.siteID'));
				</cfscript>

				<!--- get the SRID and permissions of TransactionsAdmin. --->
				<cfset local.TransactionAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
				<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
			</cfif>
		</cfif>

		<cfif arguments.event.getValue('mc_siteinfo.sf_memberHistory') eq 1 and structKeyExists(local.tmpMemSetRights,"ViewHistory") and local.tmpMemSetRights.ViewHistory eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='History', id='memhistory', fn='/model/admin/memberhistory/dsp_memberHistory_list' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.objMemberHistory = CreateObject('component','model.admin.memberHistory.memberHistory');
				local.MemberHistoryAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MemberHistoryAdmin',siteID=local.rc.mc_siteinfo.siteID);
				local.qryCategories = local.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=local.MemberHistoryAdminSRID);
				
				local.historyTitle = "History";
				local.historyTitleSingular = "History";
				local.typeID = 1;
				local.editMemberTab = local.arrTabs[local.ap].id;
				local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
				local.mhEditLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='editMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhAddLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='addMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhExportLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='exportMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItem') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemsLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItems') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhAnalyzeMemberHistory = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='dspAnalyzeMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#';
				local.mhMassEmailLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='massEmailMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.mhReCategorizeLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='reCategorizeMemberHistory') & '&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream&LimitToMemberID=#local.rc.memberID#";
				local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
				local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
				arguments.event.setValue('ret',local.rc.memberID);
				</cfscript>
			</cfif>
		</cfif>

		<cfif structKeyExists(local.tmpMemSetRights,"ViewInvoices") and local.tmpMemSetRights.ViewInvoices eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Invoices', id='transactions', fn='dsp_memberForm_transactions' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfset local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin')>
				<cfset local.addPaymentURL = local.transAdminTool & "&mca_ta=addPayment&mode=direct">
				<cfset local.allocatePaymentURL = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct">
				<cfset local.manageCardsURL = local.transAdminTool & "&mca_ta=manageCards&mode=direct">
				<cfset local.refundPaymentURL = local.transAdminTool & "&mca_ta=refundPayment&mode=direct">
				<cfset local.addSaleURL = local.transAdminTool & "&mca_ta=addSale&mode=direct">
				<cfset local.editTransactionURL = local.transAdminTool & "&mca_ta=editTransaction&mode=direct">
				<cfset local.adjustTransactionURL = local.transAdminTool & "&mca_ta=adjustTransaction&mode=direct">
				<cfset local.viewTransactionInfoURL = local.transAdminTool & "&mca_ta=viewTransactionInfo&mode=direct">
				<cfset local.importAuthorizeNetPaymentURL = local.transAdminTool & "&mca_ta=importAuthorizeNetPayment&mode=direct">
				
				<cfset local.invoiceAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('InvoiceAdmin')>
				<cfset local.viewInvoiceInfoURL = local.invoiceAdminTool & "&mca_ta=viewInvoiceInfo&mode=direct">
				<cfset local.editInvoiceURL = local.invoiceAdminTool & "&mca_ta=editInvoice&mode=direct">
				<cfset local.invoiceListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getInvoiceListByMemberID&srID=#this.siteResourceID#&mode=stream&memberid=#local.strMember.qryMember.memberid#">
				<cfset local.transactionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getTransactionListByMemberID&srID=#this.siteResourceID#&mode=stream&memberid=#local.strMember.qryMember.memberid#">
				<cfset local.failedPaymentListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getFailedPaymentListByMemberID&srID=#this.siteResourceID#&mode=stream&memberid=#local.strMember.qryMember.memberid#">
				
				<!--- get the SRID and permissions of InvoiceAdmin. --->
				<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
				<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

				<!--- get the SRID and permissions of TransactionsAdmin. --->
				<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
				<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

				<cfset local.strMemberCredit = local.objMember.getMemberCreditAmount(orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=local.rc.memberID)>
				<cfif NOT local.strMemberCredit.success>
					<cfset local.strMemberCredit.unallocatedamount = 0>
				</cfif>
				<cfset local.strMemberBalance = local.objMember.getMemberOutstandingAmount(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=local.rc.memberID)>
				<cfif NOT local.strMemberBalance.success>
					<cfset local.strMemberBalance.outstandingamount = 0>
				</cfif>
				<cfset local.strMemberInvBalance = local.objMember.getMemberOutstandingInvAmount(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=local.rc.memberID)>
				<cfif NOT local.strMemberInvBalance.success>
					<cfset local.strMemberInvBalance.outstandingamount = 0>
				</cfif>
				<!--- get invoice dues --->
				<cfset local.qryInvoicesDue = CreateObject('component','model.admin.transactions.transactionAdmin').getInvoicesDueByFilters(orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.rc.memberID, strFilters={})>
				<cfif local.qryInvoicesDue.recordcount>
					<cfset local.isDueInvoice = 1>
				<cfelse>
					<cfset local.isDueInvoice = 0>
				</cfif>

				<!--- Enc string for Add Payment and Allocate Payments links --->
				<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.rc.memberID)>
				<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
					<cfset local.allocPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.rc.memberID)>
				</cfif>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMPForCOF">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select count(distinct mp.profileID) as cofMPCount, count(distinct mpp.payProfileID) as cofBadCount
					from dbo.mp_profiles as mp
					inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
					left outer join dbo.ams_memberPaymentProfiles as mpp on mpp.profileID = mp.profileID 
						and mpp.memberID = #local.rc.memberID#
						and mpp.status = 'A'
						and mpp.failedLastDate is not null
					where mp.siteID = #local.rc.mc_siteinfo.siteID#
					and mg.gatewayType in ('AuthorizeCCCIM','SageCCCIM','BankDraft','AffiniPayCC','MCPayEcheck')
					and mp.status = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMPForImportAuthNet">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select count(mp.profileID) as mpCount
					from dbo.mp_profiles as mp
					inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
					where mp.siteID = #local.rc.mc_siteinfo.siteID#
					and mg.gatewayType = 'AuthorizeCCCIM'
					and mp.status = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				
				<cfquery name="local.qryOrgData" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select o.invoiceNumPrefix
					from dbo.organizations as o
					where o.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.objInvoice = createObject("component","model.admin.transactions.invoice")>
				<cfset local.qryStatus = local.objInvoice.getInvoiceStatuses()>
				<cfset local.qryInvoiceProfiles = local.objInvoice.getInvoiceProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
				<cfset local.qryPayProfiles = local.objInvoice.getPaymentProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
			</cfif>
		</cfif>

		<cfif local.qryLists.recordcount and structKeyExists(local.tmpMemSetRights,"ViewLists") and local.tmpMemSetRights.ViewLists eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Lists', id='lists', fn='dsp_memberForm_lists' }>
			<cfset local.mainEmail = application.objMember.getMainEmail(memberID=local.rc.memberID)>
		</cfif>

		<cfif structKeyExists(local.tmpMemSetRights,"ViewNotes") and local.tmpMemSetRights.ViewNotes eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Notes', id='notes', fn='/model/admin/memberhistory/dsp_memberHistory_list' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.objMemberHistory = CreateObject('component','model.admin.memberHistory.memberHistory');
				local.HistoryAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=local.rc.mc_siteinfo.siteID);
				local.qryCategories = local.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=local.HistoryAdminSRID);
				
				local.historyTitle = "Notes";
				local.historyTitleSingular = "Note";
				local.typeID = 3;
				local.editMemberTab = local.arrTabs[local.ap].id;
				local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
				local.mhEditLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='editMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhAddLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='addMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhExportLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='exportMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItem') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemsLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItems') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhAnalyzeMemberHistory = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='dspAnalyzeMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#';
				local.mhMassEmailLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='massEmailMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.mhReCategorizeLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='reCategorizeMemberHistory') & '&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream&LimitToMemberID=#local.rc.memberID#";
				local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
				local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
				arguments.event.setValue('ret',local.rc.memberID);
				</cfscript>
			</cfif>
		</cfif>

		<cfif arguments.event.getValue('mc_siteInfo.hasReferrals') and structKeyExists(local.tmpMemSetRights,"ViewReferrals") and local.tmpMemSetRights.ViewReferrals eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Referrals', id='referral', fn='dsp_memberForm_referrals' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.link.editClient = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='ReferralsAdmin',mca_ta='editClient');
				local.link.viewCaseStatement = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='ReferralsAdmin',mca_ta='viewCaseStatement') & "&mode=direct";
				local.link.viewProgressReport = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='ReferralsAdmin',mca_ta='viewProgressReport') & "&mode=direct&memberID=#local.rc.memberID#";
				arguments.event.paramValue('refDateRange',0);
				arguments.event.paramValue('caseDateRange',0);
				local.addPanelLink = buildCurrentLink(arguments.event,"addPanel")  & "&mode=direct";
				local.editPanelLink = buildCurrentLink(arguments.event,"editPanel")  & "&mode=direct";
				local.addSubPanelLink = buildCurrentLink(arguments.event,"addSubPanel")  & "&mode=direct";
				
				local.urlString = "";
				local.refDatatableRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&mode=stream&mid=#local.rc.memberID#&meth="
				local.panelLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getPanels&mode=stream&memberID=#local.rc.memberID#";
				local.referralList = "#local.refDatatableRootLink#getMemberReferrals";
				local.caseList = "#local.refDatatableRootLink#getAdminMemberCases";
				local.historyList = "#local.refDatatableRootLink#getMemberReferralHistory";
				
				local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
				local.addPaymentURL = local.transAdminTool & "&mca_ta=addPayment&mode=direct";
				local.allocatePaymentURL = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";
				local.objAdminReferrals = createObject("component","model.admin.referrals.referrals");
				local.referralID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;
				local.isPanelGroupDepend = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).isPanelGroupDepend;
				
				local.historyObj = CreateObject('component', 'model.system.platform.history');
				local.auditTrailFlag = arguments.event.getValue('at','');
				if (local.auditTrailFlag eq 'all')
					local.memberAuditTrail = local.historyObj.getPanelMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.rc.memberID);
				else
					local.memberAuditTrail = local.historyObj.getPanelMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.rc.memberID, limit="5");
				</cfscript>

				<!--- get the SRID and permissions of TransactionsAdmin. --->
				<cfset local.TransactionAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
				<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
			</cfif>
		</cfif>

		<cfif arguments.event.getValue('mc_siteinfo.sf_relationships') eq 1 and structKeyExists(local.tmpMemSetRights,"ViewRelationships") and local.tmpMemSetRights.ViewRelationships eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Relationships', id='relationships', fn='/model/admin/memberhistory/dsp_memberHistory_list' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.objMemberHistory = CreateObject('component','model.admin.memberHistory.memberHistory');
				local.RelationshipAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=local.rc.mc_siteinfo.siteID);
				local.qryCategories = local.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=local.RelationshipAdminSRID);
				
				local.historyTitle = "Relationships";
				local.historyTitleSingular = "Relationship";
				local.typeID = 2;
				local.editMemberTab = local.arrTabs[local.ap].id;
				local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
				local.mhEditLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='editMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhAddLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='addMemberHistory') & '&ret=#local.rc.memberID#&mode=direct';
				local.mhExportLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='exportMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItem') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhDeleteItemsLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='deleteMemberHistoryItems') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=stream';
				local.mhAnalyzeMemberHistory = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='dspAnalyzeMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#';
				local.mhMassEmailLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='massEmailMemberHistory') & '&typeID=#local.typeID#&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.mhReCategorizeLink = buildLinkToTool(toolType='MemberHistoryAdmin',mca_ta='reCategorizeMemberHistory') & '&LimitToMemberID=#local.rc.memberID#&mode=direct';
				local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream&LimitToMemberID=#local.rc.memberID#";
				local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
				local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
				arguments.event.setValue('ret',local.rc.memberID);
				</cfscript>
			</cfif>
		</cfif>
		
		<cfset local.arrNodes = XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Events']/navitem[@navName='SeminarWeb']")>
		<cfif structKeyExists(session.mcastruct.strNavKeys, local.arrNodes[1].xmlAttributes.navKey)
			and structKeyExists(local.tmpMemSetRights,"ViewSeminarWeb") and local.tmpMemSetRights.ViewSeminarWeb eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='SeminarWeb', id='seminarweb', fn='dsp_memberForm_seminarweb' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
				local.viewSWCertificateLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewCertificate') & "&mode=direct";
				local.manageSWCreditLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='manageCredit') & "&mode=direct";
				local.viewSWCommunicationLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='getCommunication') & "&mode=direct";
				local.resendSWInstructionsLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='resendInstructions') & "&mode=direct";
				local.changeRegistrantPriceLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta="changeRegistrantPrice") & "&mode=direct";
				local.removeEnrollmentLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='removeEnrollment') & "&mode=direct";

				local.transactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
				local.myRightsTransactionsAdmin = buildRightAssignments(local.transactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
				local.addSWPaymentLink	= "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
				if (local.myRightsTransactionsAdmin.transAllocatePayment is 1)
					local.allocateSWPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
				
				if (len(arguments.event.getValue('mc_siteInfo.swlBrand'))) {
					local.SWLheading = arguments.event.getValue('mc_siteInfo.swlBrand');
					local.SWLRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWLRegistrants&gridmode=reggrid&mode=stream&mid=#local.rc.memberID#";
					local.editSWLProgram = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWLProgram',navMethod='listSWL');
					local.sendSWLMaterialsLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='sendMaterials') & "&mode=direct";
					local.sendSWLReplayLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='sendSWLReplayLink') & "&mode=direct";
					local.viewSWLProgressLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewSWLProgress') & "&mode=direct";
				}
				if (len(arguments.event.getValue('mc_siteInfo.swodBrand'))) {
					local.SWODheading = arguments.event.getValue('mc_siteInfo.swodBrand');
					local.SWODRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWODRegistrants&gridmode=reggrid&mode=stream&mid=#local.rc.memberID#";

					local.editSWODProgram = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWODProgram',navMethod='listSWOD');
					local.viewSWODProgressLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewProgress') & "&mode=direct";
				}
				if (len(arguments.event.getValue('mc_siteInfo.swcpBrand'))) {
					local.SWCPheading = arguments.event.getValue('mc_siteInfo.swcpBrand');
					local.SWCPRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=seminarWebJSON&meth=getSWCPRegistrants&gridmode=reggrid&mode=stream&mid=#local.rc.memberID#";

					local.editSWCPProgram = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWCPProgram',navMethod='listSWCP');
					local.viewSWCPProgressLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewProgressSWCP')& "&mode=direct";
					local.viewSWCPProgressDetailLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewProgress')&"&mode=direct";
				}
				</cfscript>
			</cfif>
		</cfif>
		<cfset local.arrNodes = XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Website']/navitem[@navName='Applications']/navitem[@navName='Store']")>
		<cfif structKeyExists(session.mcastruct.strNavKeys, local.arrNodes[1].xmlAttributes.navKey) and  structKeyExists(local.tmpMemSetRights,"ViewStore") and local.tmpMemSetRights.ViewStore eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Store', id='storeView', fn='dsp_memberForm_storeView' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
					local.objAdminStore = CreateObject("component", "model.admin.store.store");
					local.storeInfo = local.objAdminStore.getStoreInfo(arguments.event);
					local.link.addOrder	 = buildLinkToTool(toolType='StoreAdmin',mca_ta='addOrder')&"&sa=regStoreUser&regaction=usemid&mid=#local.rc.memberID#";
					local.link.manageOrder	 = buildLinkToTool(toolType='StoreAdmin',mca_ta='manageOrder');
					local.link.sendOrderDetail	 = buildLinkToTool(toolType='StoreAdmin',mca_ta='sendOrderDetail');
					local.siteResourceID = local.storeInfo.siteResourceID;
					local.storeID = local.storeInfo.storeID;
					local.storeOrderLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=storeJSON&meth=getStoreOrderList&mode=stream&storeSRID=#local.siteResourceID#&storeID=#local.storeInfo.storeID#&mid=#local.rc.memberID#";
					local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
					local.addPaymentURL = local.transAdminTool & "&mca_ta=addPayment&mode=direct";
					this.link.allocatePayment = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";
					local.TransactionAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
					local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));
					local.allocatePaymentURL = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";
				</cfscript>
				<cfquery name="local.qryStatus" datasource="#application.dsn.membercentral.dsn#">
					select orderStatusID, statusName
					from dbo.store_OrderStatus
					order by orderStatusID
				</cfquery>
			</cfif>
		</cfif>
		
		<cfif arguments.event.getValue('mc_siteInfo.sf_tasks') and structKeyExists(local.tmpMemSetRights,"ViewTasks") and local.tmpMemSetRights.ViewTasks eq 1>
			<cfset local.ap = ArrayLen(local.arrTabs)+1>
			<cfset local.arrTabs[local.ap] = { title='Tasks', id='tasks', fn='dsp_memberForm_tasks' }>
			<cfif currTab EQ local.arrTabs[local.ap].id>
				<cfscript>
					local.objTask = CreateObject('component', 'model.admin.tasks.task');
					local.tmpTasksRights = local.objTask.getMemberTaskAdminRights(siteID=local.rc.mc_siteinfo.siteID);
					local.qryWorkspaces = local.objTask.getWorkspaces(siteID=local.rc.mc_siteinfo.siteID);
					local.qryProjects = local.objTask.getSiteProjects(siteID=local.rc.mc_siteinfo.siteID);
					local.qryTaskStatuses = local.objTask.getTaskStatuses();
					
					local.taskListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=tasksJSON&meth=getTasks&mode=stream&memberID=#local.rc.memberID#";
					local.taskViewLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='viewTask') & '&mode=stream';
					local.taskAddLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='addTask') & '&aMemberID=#local.strMember.qryMember.memberid#&mode=stream';
					local.massEmailTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='massEmailTasks') & '&memberID=#local.rc.memberID#&mode=direct';
					local.massEditTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='massEditTasks') & "&memberID=#local.rc.memberID#&mode=stream";
					local.exportTasksPromptLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='exportTasksPrompt') & "&memberID=#local.rc.memberID#&mode=direct";
					local.exportTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='exportTasks') & "&memberID=#local.rc.memberID#&mode=stream";
					local.taskChangeHistoryLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='showTaskChangeHistory') & "&mode=stream";
					local.linkCCToTaskLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='linkCCToTask') & "&mode=direct";
				</cfscript>
			</cfif>
		</cfif>

		<cfif listFindNoCase('profile,emailactivity',currTab)>
			<cfset local.manageMemConsentListsLink = buildLinkToTool(toolType='EmailPreferencesAdmin',mca_ta='manageMemberConsentLists') & "&mID=#local.rc.memberID#&mode=direct">
		</cfif>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_updateViewedMemberTime">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rc.memberID#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfset local.startDateFrom = arguments.event.getValue('fDateFrom','')>
		<cfset local.startDateTo = arguments.event.getValue('fDateTo','')>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberForm.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getMemberSubscriptionUpdateHistory" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="limit" type="numeric" required="false" default="0">

		<cfset var local = StructNew()>
		<cfset local.retValue = StructNew()>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryStatusHistory">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			declare @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			declare @systemMemberID int = dbo.fn_ams_getMCSystemMemberID();
			declare @activeMemberID int;
			declare @tblSubHistory table (id int identity(1,1), dateRecorded datetime, updateDate datetime, [description] varchar(max), subStartDate datetime, subEndDate datetime, previousStatus varchar(30), ACTORMEMBERID int, subscriberID int);
			
			select @activeMemberID = activeMemberID from dbo.ams_members where orgID = @orgID and memberID = @memberID;
			
			insert into @tblSubHistory (dateRecorded, updateDate, [description], subStartDate, subEndDate, previousStatus, ACTORMEMBERID, subscriberID)
			select sh.dateRecorded, sh.updateDate,
				case 
					when sh.oldStatusID is null then subs.subscriptionName + ' was created'
					when st.statusCode = 'A' then subs.subscriptionName + ' was made active'
					when st.statusCode = 'I' then subs.subscriptionName + ' was made inactive'
					when st.statusCode = 'O' then subs.subscriptionName + ' was changed to billed'
					when st.statusCode = 'P' then subs.subscriptionName + ' was accepted'
					when st.statusCode = 'E' then subs.subscriptionName + ' was expired'
					when st.statusCode = 'X' then subs.subscriptionName + ' offer was expired'
					when st.statusCode = 'D' then subs.subscriptionName + ' was deleted'
				else '' end as [description],
				s.subStartDate, s.subEndDate, CASE WHEN ost.statusID IS NOT NULL THEN ost.statusName ELSE 'N/A' END AS previousStatus,
				mEBM.activeMemberID as ACTORMEMBERID, s.subscriberID
			from dbo.sub_statusHistory sh
			inner join dbo.sub_subscribers s on s.orgID = @orgID and s.subscriberID = sh.subscriberID
			inner join dbo.sub_subscriptions subs on subs.orgID = @orgID and subs.subscriptionID = s.subscriptionID 
			inner join dbo.ams_members m on m.orgID = @orgID and m.memberID = s.memberID
				and m.activeMemberID = @activeMemberID
			inner join dbo.sub_statuses st on st.statusID = sh.statusID
			LEFT OUTER JOIN dbo.sub_statuses ost ON ost.statusID = sh.oldStatusID
			inner join dbo.ams_members mEBM on mEBM.memberID = sh.enteredByMemberID
			where sh.orgID = @orgID
				union all
			select psh.dateRecorded, psh.updateDate,
				subs.subscriptionName + ' - ' + ps.statusName,
				s.subStartDate, s.subEndDate, 'N/A' AS previousStatus,
				mEBM.activeMemberID as ACTORMEMBERID, s.subscriberID
			from dbo.sub_paymentStatusHistory psh
			inner join dbo.sub_subscribers s on s.orgID = @orgID 
				and s.subscriberID = psh.subscriberID
			inner join dbo.sub_subscriptions subs on subs.orgID = @orgID and subs.subscriptionID = s.subscriptionID 
			inner join dbo.ams_members m on m.orgID = @orgID and m.memberID = s.memberID 
				and m.activeMemberID = @activeMemberID
			inner join dbo.sub_paymentStatuses ps on ps.statusID = psh.paymentStatusID
			inner join dbo.ams_members mEBM on mEBM.memberID = psh.enteredByMemberID
			where psh.orgID = @orgID 
			and psh.paymentStatusID in (1,2)
				union all
			select el.dateSent as dateRecorded, el.dateSent as updateDate,
				subs.subscriptionName + ' - ' + ls.statusDescription,
				s.subStartDate, s.subEndDate, 'N/A' AS previousStatus,
				isnull(mEBM.activeMemberID,@systemMemberID) as ACTORMEMBERID, s.subscriberID
			from dbo.ams_emailLog el
			inner join ams_emailLogStatuses ls on ls.statusID = el.emailStatusID
			inner join dbo.sub_subscribers s on s.subscriberID = el.subscriberID
			inner join dbo.sub_subscriptions subs on subs.orgID = @orgID and subs.subscriptionID = s.subscriptionID 
			inner join dbo.ams_members m on m.orgID = @orgID and m.memberID = s.memberID
				and m.activeMemberID = @activeMemberID
			left outer join dbo.ams_members mEBM 
				on mEBM.memberID = el.actorMemberID
			order by sh.updateDate;

			select tbl.dateRecorded, tbl.updateDate, tbl.[description], tbl.subStartDate, tbl.subEndDate, tbl.previousStatus, tbl.ACTORMEMBERID, tbl.subscriberID, m.lastname, m.firstname
			from @tblSubHistory as tbl
			inner join dbo.ams_members m on m.memberID = tbl.ACTORMEMBERID
			order by tbl.updateDate DESC, tbl.description;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfif arguments.limit is 0>
			<cfset local.retValue.qryStatusHistory = local.qryStatusHistory>
			<cfset local.retValue.foundCount = local.qryStatusHistory.recordCount>
			<cfset local.retValue.totalCount = local.qryStatusHistory.recordCount>
		<cfelse>
			<cfquery dbtype="query" name="local.retValue.qryStatusHistory" maxrows="#arguments.limit#">
				select dateRecorded, updateDate, description, subStartDate, subEndDate, previousStatus, ACTORMEMBERID, subscriberID, lastname, firstname
				from [local].qryStatusHistory
				order by updateDate DESC, description
			</cfquery>
			
			<cfset local.retValue.foundCount = local.retValue.qryStatusHistory.recordCount>
			<cfset local.retValue.totalCount = local.qryStatusHistory.recordCount>
		</cfif>
		
		<cfreturn local.retValue>
	</cffunction>

	<cffunction name="editInfo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objMember = CreateObject("component","members")>

		<cfset local.orgid = arguments.event.getValue('mc_siteinfo.orgid')>
		<cfset local.memberID = local.objMember.getMember_validcheck(orgID=local.orgid, memberID=arguments.event.getValue('memberID',0))>

		<cfif arguments.event.getValue('memberID',0) gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR checkRights(arguments.event,'EditOrg'))>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
		<cfelseif arguments.event.getValue('memberID',0) is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg'))>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
		<cfelseif arguments.event.getValue('memberID',0) gt 0 and local.memberID is 0>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=2" addtoken="no">
		</cfif>

		<cfset local.qryMember = local.objMember.getMember_demo(memberid=local.memberid)>
		<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=local.orgID)>
		<cfset local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(orgID=local.orgID)>
		<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=local.orgID)>
		<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=local.orgID)>
		<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.orgID)>
		<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(orgID=local.orgID)>

		<cfset local.qryMemberAddresses = local.objMember.getMember_addresses(orgid=local.orgID, memberid=local.memberid)>
		<cfset local.qryMemberAddressTags = local.objMember.getMember_addressTags(orgid=local.orgID, memberID=local.memberID)>
		<cfset local.qryStates = application.objCommon.getStates()>
		<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(orgID=local.orgID)>
		<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberid=local.memberID, orgID=local.orgID)>
		<cfset local.qryMemberEmailTags = local.objMember.getMember_emailTags(memberID=local.memberID)>
		<cfset local.qryMemberWebsites = application.objMember.getMemberWebsites(memberid=local.memberID, orgID=local.orgID)>

		<cfset local.isMemberInLoginGroup = local.objMember.isMemberInLoginGroup(arguments.event.getValue('mc_siteinfo.siteid'),local.memberID)>
		<cfif local.isMemberInLoginGroup and local.qryMember.status eq "A" and arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1>
			<cfset local.stUserName = application.objUser.login_getUsername(memberID=local.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		</cfif>
		
		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.hasPrefix, o.usePrefixList, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qryOrgMemberFields.usePrefixList is 1>
			<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=local.orgID)>
		</cfif>

		<cfif local.memberID is 0>
			<cfset local.qryCustomAddFieldsetID = getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='newacct')>
			<cfset local.customAddfieldsetInfo = { fieldsetName=local.qryCustomAddFieldsetID.fieldsetName, nameFormat=local.qryCustomAddFieldsetID.nameFormat, showHelp=local.qryCustomAddFieldsetID.showHelp }>
			<cfset local.xmlFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryCustomAddFieldsetID.fieldsetID), usage="memberAdminNew")>
			<cfset local.xmlAdditionalData_Member = local.objMember.getMemberAdditionalData(memberid=local.memberID)>
			<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=local.orgID)>
			<cfset local.qryOrgProfessionalLicenseTypes = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.orgID)>
			<cfset local.qryOrgProfessionalLicenseStatuses = local.objMember.getOrgProfessionalLicenseStatuses(orgid=local.orgID)>
			<cfset local.qryOrgProfLicenseLabels = application.objOrgInfo.getOrgProfLicenseLabels(orgID=local.orgID)>

			<cfset local.memberNamePrinted = "New Member Information">
		<cfelse>
			<cfset local.memberNamePrinted = "#local.qryMember.prefix# #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix# #local.qryMember.professionalsuffix#">
			<cfset local.memberNamePrinted = ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL") & " (#local.qryMember.memberNumber#)">
		</cfif>

		<cfset local.MemberAdminSRID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID')>
		<cfset local.tmpMemberRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.MemberAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif local.memberID gt 0>
			<cftry>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_updateViewedMemberTime">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfset local.saveDemoURL = buildCurrentLink(arguments.event,"saveDemo") & "&mode=stream">
		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberForm_demographicsEdit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showAddressAndFieldSetData" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objMember = CreateObject("component","members")>
			
		<cftry>
			<cfset local.memberID = int(val(arguments.event.getValue('memberID',0)))>
			<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.qryMemberAddresses = local.objMember.getMember_addresses(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.memberID)>
			<cfset local.qryMemberAddressTags = local.objMember.getMember_addressTags(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.memberID)>
			<cfset local.qryMemberAddressData = application.objMember.getMemberAddressData(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

			<cfset local.qryFieldsetID = getLocatorFieldsetID(siteResourceID=this.siteResourceID, area='main')>
			<cfset local.fieldsetInfo = StructNew()>
			<cfset local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName>
			<cfset local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat>
			<cfset local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp>
			<cfset local.xmlFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberAdminMain")>

			<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.qryMemberEmailTags = local.objMember.getMember_emailTags(memberID=local.memberID)>
			<cfset local.qryMemberWebsites = application.objMember.getMemberWebsites(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.qrySuppressedMemberEmails = CreateObject('component', 'model.admin.emailBlast.suppressionListEmails').getSuppressedEmailsForMember(
				siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.memberID)>
			<cfset local.suppressedEmailsList = valueList(local.qrySuppressedMemberEmails.email)>

			<cfset local.objEmailPreferences = createObject("component","model.admin.emailPreferences.emailPreferences")>
			<cfset local.qryOptOutLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out")>
			<cfset local.qryOptInLists = local.objEmailPreferences.getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-In")>

			<cfif local.qryOptOutLists.recordCount>
				<cfset local.inOptOutListCount = local.objEmailPreferences.getConsentListMemberCount(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID, listMode="Opt-Out")>
			</cfif>
			<cfif local.qryOptInLists.recordCount>
				<cfset local.inOptInListCount = local.objEmailPreferences.getConsentListMemberCount(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID, listMode="Opt-In")>
			</cfif>

			<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.xmlAdditionalData_Member = local.objMember.getMemberAdditionalData(memberid=local.memberID)>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_demographics_addrfieldset.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load field set data for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageLinkedRecords" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.rc.memberID = int(val(arguments.event.getValue('memberID',0)))>
		<cfset local.qryMember = local.objMember.getMember_demo(local.rc.memberID)>
		<cfset local.showMemberPhotosInSearchResults = xmlSearch(this.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)') neq "false" ? 1 : 0>

		<cfset local.getLinkedRecordsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getLinkedRecords&memberID=#arguments.event.getValue('memberID',0)#&mode=stream">
		<cfset local.getLinkedRecordsWithSelectedDataLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getLinkedRecordsWithSelectedData&memberID=#arguments.event.getValue('memberID',0)#&mode=stream">
		<cfset local.copyRecordRelDataFromLink = buildCurrentLink(arguments.event,"copyRecordRelationshipForm") & "&mode=stream">
		<cfset local.editMemberURL = buildCurrentLink(arguments.event,"edit")>

		<cfset local.orgWebsiteTypes = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.rc.mc_siteinfo.orgID)>
		<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfset local.qryMemRoleTypes = local.objMember.getLinkedMemberRoleTypes(orgID=local.rc.mc_siteinfo.orgID, memberid=local.rc.memberID)>
		<cfset local.qryMemRecordTypes = local.objMember.getLinkedMemberRecordTypes(orgID=local.rc.mc_siteinfo.orgID, memberid=local.rc.memberID)>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.memberNamePrinted">
			<cfoutput>
			<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.qryMember.prefix)>#local.qryMember.prefix#</cfif> 
			#local.qryMember.firstname# 
			<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.qryMember.middlename)>#local.qryMember.middlename#</cfif> 
			#local.qryMember.lastname# 
			<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.qryMember.suffix)>#local.qryMember.suffix#</cfif> 
			<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.qryMember.professionalsuffix)>#local.qryMember.professionalsuffix#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.memberNamePrinted = ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>
			
		<cfquery name="local.availableRecordRelationships" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('memberID',0)#">;
			
			if exists (select memberID from dbo.ams_members em inner join dbo.ams_recordTypes ert on em.recordTypeID = ert.recordTypeID and em.memberID = @memberID and ert.isPerson = 1)
				select rtrt.recordTypeRelationshipTypeID, rt.recordTypeName as thisRecordTypeName, rt.recordTypeCode as thisRecordTypeCode, rt.isPerson as thisIsPerson, rt.isOrganization as thisIsOrganization, rrt.relationshipTypeID, rrt.relationshipTypeName, rrt.relationshipTypeCode, linkingRT.recordTypeName as linkingRecordTypeName, linkingRT.recordTypeCode as thisRecordTypeCode, linkingRT.isPerson as linkingIsPerson, linkingRT.isOrganization as linkingIsOrganiztion
				from dbo.ams_members m
				inner join dbo.ams_recordTypes rt on rt.recordTypeID = m.recordTypeID
					and m.memberID = @memberID
				inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.childRecordTypeID = m.recordTypeID and rtrt.isActive = 1
				inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
				inner join dbo.ams_recordTypes linkingRT on rtrt.masterRecordTypeID = linkingRT.recordTypeID
				order by linkingRecordTypeName, rrt.relationshipTypeName;
			else
				select rtrt.recordTypeRelationshipTypeID, rt.recordTypeName as thisRecordTypeName, rt.recordTypeCode as thisRecordTypeCode, rt.isPerson as thisIsPerson, rt.isOrganization as thisIsOrganization, rrt.relationshipTypeID, rrt.relationshipTypeName, rrt.relationshipTypeCode, linkingRT.recordTypeName as linkingRecordTypeName, linkingRT.recordTypeCode as thisRecordTypeCode, linkingRT.isPerson as linkingIsPerson, linkingRT.isOrganization as linkingIsOrganiztion
				from dbo.ams_members m
				inner join dbo.ams_recordTypes rt on rt.recordTypeID = m.recordTypeID
					and m.memberID = @memberID
				inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.masterRecordTypeID = m.recordTypeID and rtrt.isActive = 1
				inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
				inner join dbo.ams_recordTypes linkingRT on rtrt.childRecordTypeID = linkingRT.recordTypeID
				order by linkingRecordTypeName, rrt.relationshipTypeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.thisMemberRole = local.availableRecordRelationships.recordcount and local.availableRecordRelationships.thisIsPerson ? "child" : "parent">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_manageLinkedRecords.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showProfessionalLicenses" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.qryOrgProfLicenseLabels = application.objOrgInfo.getOrgProfLicenseLabels(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryProLicenses = CreateObject("component","members").getMember_prolicenses(memberID=arguments.event.getValue('memberID',0), orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_demographics_licenses.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load licenses for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showGroups" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.qryMemberGroups = CreateObject("component","members").getMember_groups(memberid=arguments.event.getValue('memberID',0), orgID=arguments.event.getValue('mc_siteinfo.orgID'), featuredOnly=0, includeAdminSystemGroups=1)>
			<cfquery dbtype="query" name="local.qryFeatureSorted">
				select groupID, isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect, groupName, groupDesc, 
					groupPath, isFeatured, imageExt, manualDateAdded, manualAddedBy
				from [local].qryMemberGroups
				order by isFeatured DESC, thePath
			</cfquery>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_demographics_groups.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load group membership for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showChangeHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
			
		<cftry>
			<cfset local.historyObj = CreateObject('component', 'model.system.platform.history')>

			<cfset local.auditTrailFlag = arguments.event.getValue('at','')>
			<cfif local.auditTrailFlag eq 'all'>
				<cfset local.memberAuditTrail = local.historyObj.getMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=arguments.event.getValue('memberID',0))>
			<cfelse>
				<cfset local.memberAuditTrail = local.historyObj.getMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=arguments.event.getValue('memberID',0), limit="5")>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_demographics_changeHistory.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load change history for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showConditionGroupChangeHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
			
		<cftry>
			<cfset local.tmpSQL = prepareConditionGroupChangeHistorySQL(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=arguments.event.getValue('memberID',0))>

			<cfquery name="local.getMemberHistory" datasource="#application.dsn.platformStatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				#preserveSingleQuotes(local.tmpSQL)#

				select runDate, detail
				from ##tmpChangeLogForMember
				order by runDate desc, detail;

				IF OBJECT_ID('tempdb..##tmpChangeLogForMember') IS NOT NULL
					DROP TABLE ##tmpChangeLogForMember;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_demographics_conditionChangeHistory.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load change history for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportConditionGroupChangeHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "ConditionGroupChangeHistory.csv">
		<cfset local.tmpSQL = prepareConditionGroupChangeHistorySQL(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=arguments.event.getValue('memberID',0))>

		<cfquery name="local.qryExportMemberHistory" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			#preserveSingleQuotes(local.tmpSQL)#

			DECLARE @selectsql varchar(max) = '
				SELECT runDate as Date, detail as Description, ROW_NUMBER() OVER(order by runDate desc, detail asc) as mcCSVorder 
				*FROM* ##tmpChangeLogForMember';
			EXEC memberCentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpChangeLogForMember') IS NOT NULL
				DROP TABLE ##tmpChangeLogForMember;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=5" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="prepareConditionGroupChangeHistorySQL" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qryString = "">

		<cfsavecontent variable="qryString">
			<cfoutput>
				declare @orgID int, @memberID int;
				set @orgID  = #val(arguments.orgID)#;
				set @memberID = #val(arguments.memberID)#;

				IF OBJECT_ID('tempdb..##tmpChangeLogForMember') IS NOT NULL
					DROP TABLE ##tmpChangeLogForMember;
				CREATE TABLE ##tmpChangeLogForMember (runDate datetime, detail varchar(max));

				INSERT INTO ##tmpChangeLogForMember (runDate, detail)
				select cr.runDate, case when cc.isAdded = 1 then 'Added to' else 'Removed from' end + ' condition ' + c.verbose
				from dbo.cache_conditionsLogRunChanges as cc
				inner join dbo.cache_conditionsLogRun as cr on cr.runID = cc.runID
				inner join membercentral.dbo.ams_virtualGroupConditions as c on c.conditionID = cc.conditionID
				where cc.memberID = @memberID;

				INSERT INTO ##tmpChangeLogForMember (runDate, detail)
				select gr.runDate, case when gc.isAdded = 1 then 'Added to' else 'Removed from' end + ' group ' + g.groupPathExpanded
				from dbo.cache_groupsLogRunChanges as gc
				inner join dbo.cache_groupsLogRun as gr on gr.runID = gc.runID
				inner join membercentral.dbo.ams_groups as g on g.groupID = gc.groupID
				where gc.memberID = @memberID;

				INSERT INTO ##tmpChangeLogForMember (runDate, detail)
				select tl.dateTriggered, 'Triggered processing of ' + case processType
					when 'ConditionsAndGroups' then 'conditions and groups'
					when 'ConditionsAndGroupsChanged' then 'conditions and groups if conditions changed'
					when 'GroupsOnly' then 'groups'
					when 'ConditionsOnly' then 'conditions'
					when 'ConditionsOnlyNonImm' then 'report filters'
					end	
				from dbo.cache_conditionsLogRunMembers as cm
				inner join dbo.cache_conditionsLogRun as r on r.runID = cm.runID
				inner join dbo.cache_conditionsTriggerLog as tl on tl.logID = r.triggerLogID
				where cm.memberID = @memberID
				or (cm.memberID = 0 and tl.xmlData.value('(/mc/@o)[1]','int') = @orgID);
			</cfoutput>
		</cfsavecontent>

		<cfreturn qryString>
	</cffunction>

	<cffunction name="saveAD" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cftry>
			<cfscript>
				local.objMember = CreateObject("component","members");

				// get member
				arguments.event.setValue('memberid',int(val(arguments.event.getValue('memberID',0))));
				local.qryMember = local.objMember.getMember_demo(arguments.event.getValue('memberID'));
		
				if (arguments.event.getValue('memberID') gt 0) 
					arguments.event.setValue('orgID',local.qryMember.orgID);
				else
					arguments.event.setValue('orgID',arguments.event.getValue('mc_siteinfo.orgid'));
		
				local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=arguments.event.getValue('orgID'));
		
				// check rights
				if (arguments.event.getValue('memberID') gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and arguments.event.getValue('orgID') is arguments.event.getValue('mc_siteinfo.orgid'))))
					throw(message="No rights");
				else if (arguments.event.getValue('memberID') is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg')))
					throw(message="No rights");
		
				if (isXMLDoc(local.xmlAdditionalData) and arrayLen(local.xmlAdditionalData.XmlRoot.XmlChildren) gt 0) {
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getValue('memberID'), sitecode=arguments.event.getValue('mc_siteinfo.sitecode'));

					local.objMember.saveAdditionalData(event=arguments.event, objSaveMember=local.objSaveMember, xmlAdditionalData=local.xmlAdditionalData);

					local.arrDocumentCols = xmlSearch(local.xmlAdditionalData,"//column[@dataTypeCode='DOCUMENTOBJ']");
					if(arrayLen(local.arrDocumentCols)) {
						for (local.i=1; local.i<=arrayLen(local.arrDocumentCols); local.i++) {
							local.columnID = local.arrDocumentCols[local.i].xmlAttributes.columnID;
							local.columnName = local.arrDocumentCols[local.i].xmlAttributes.columnName;
							if (arguments.event.getValue('md_#local.columnID#_removeDoc',0) is 1 AND arguments.event.getValue('md_#local.columnID#_newFile',0) NEQ 1 AND arguments.event.getValue('md_#local.columnID#_old',0) GT 0) {
								local.objMember.deleteMemberCustomDocument(memberID=arguments.event.getValue('memberID'), columnID=local.columnID, columnName=local.columnName, siteResourceID=arguments.event.getValue('md_#local.columnID#_old'));
							}
						}
					}

					local.strResult = local.objSaveMember.saveData();
				}
			</cfscript>
			<cfset local.saveSuccess = 1>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.saveSuccess = 0>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="c">
					<br/>
					<img src="/assets/common/images/indicator.gif" width="100" height="100">
					<br/><br/>
					<b>Loading Custom Tab...</b>
					<br/>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveMemberCustomSingleDocument" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.historyMessages = ArrayNew(1)>
		<cfset local.columnID = arguments.event.getValue('cid',0)>
		<cfset local.columnName = urlDecode(arguments.event.getValue('cname',''))>

		<cfset local.oldVal = arguments.event.getTrimValue('coldval','')>
		<cfset local.newVal = arguments.event.getTrimValue('md_#local.columnID#','')>
		<cfif len(local.newVal)>
			<cfset local.fileUploaded =  application.objCustomPageUtils.saveMemberCustomSingleDocument(cid=local.columnID, memberID=arguments.event.getValue('memberid'))>
			<cfif local.fileUploaded>
				<cfset ArrayAppend(local.historyMessages, "#local.columnName# was #val(local.oldVal) gt 0 ? 'updated' : 'added'#.")>
			</cfif>
		</cfif>

		<cfif arrayLen(local.historyMessages)>
			<cfset createObject('component','model.system.platform.history').addMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'),
				actorMemberID=session.cfcuser.memberdata.memberID, receiverMemberID=val(arguments.event.getValue('memberid')), 
				mainMessage="Member Info Updated", messages=local.historyMessages)>
		</cfif>

		<cfset local.data = "Uploaded file successfully.">

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveAddPhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cftry>
			<cfscript>
				local.objMember = CreateObject("component","members");

				// get member
				arguments.event.setValue('memberid',int(val(arguments.event.getValue('memberID',0))));
				local.qryMember = local.objMember.getMember_demo(arguments.event.getValue('memberID'));
				arguments.event.setValue('memberID',val(local.qryMember.memberid));
				
				// check rights
				if (arguments.event.getValue('memberID') gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and arguments.event.getValue('orgID') is arguments.event.getValue('mc_siteinfo.orgid'))))
					throw(message="No rights");
				else if (arguments.event.getValue('memberID') is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg')))
					throw(message="No rights");

				local.objMember.addMemberPhoto(arguments.event);
			</cfscript>
			<cfset local.saveSuccess = 1>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.saveSuccess = 0>
		</cfcatch>
		</cftry>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="mt-4 text-center">
					<div class="spinner-border"></div><div class="font-weight-bold mt-2">Please wait...</div>
				</div>
				<script language="javascript">
					top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#arguments.event.getValue('memberID')#&msg=#local.saveSuccess#&tab=profile';
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRemovePhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cftry>
			<cfscript>
				local.objMember = CreateObject("component","members");

				// get member
				arguments.event.setValue('memberid',int(val(arguments.event.getValue('memberID',0))));
				local.qryMember = local.objMember.getMember_demo(arguments.event.getValue('memberID'));
				arguments.event.setValue('memberID',val(local.qryMember.memberid));
				
				// check rights
				if (arguments.event.getValue('memberID') gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and arguments.event.getValue('orgID') is arguments.event.getValue('mc_siteinfo.orgid'))))
					throw(message="No rights");
				else if (arguments.event.getValue('memberID') is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg')))
					throw(message="No rights");
					
				local.objMember.removeMemberPhoto(arguments.event);
			</cfscript>
			<cfset local.saveSuccess = 1>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.saveSuccess = 0>
		</cfcatch>
		</cftry>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="mt-4 text-center">
					<div class="spinner-border"></div><div class="font-weight-bold mt-2">Please wait...</div>
				</div>
				<script language="javascript">
					top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#arguments.event.getValue('memberID')#&msg=#local.saveSuccess#&tab=profile';
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadPhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			local.objMember = CreateObject("component","members");

			// get member
			arguments.event.setValue('memberid',int(val(arguments.event.getValue('memberID',0))));
			local.qryMember = local.objMember.getMember_demo(arguments.event.getValue('memberID'));
			arguments.event.setValue('memberID',val(local.qryMember.memberid));

			local.photofileExists = fileexists(application.paths.localUserAssetRoot.path & LCASE(arguments.event.getValue('mc_siteinfo.orgcode')) & "/memberphotos/" & LCASE(local.qryMember.membernumber) & ".jpg");
		</cfscript>
		
		<cfif local.photofileExists>
			<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#application.paths.localUserAssetRoot.path##LCASE(arguments.event.getValue('mc_siteinfo.orgcode'))#/memberphotos/#LCASE(local.qryMember.membernumber)#.jpg", displayName="#LCASE(local.qryMember.membernumber)#.jpg", deleteSourceFile=0)>
		</cfif>

		<cfreturn returnAppStruct("Photo does not exist.","echo")>
	</cffunction>

	<cffunction name="saveDemo" access="public" output="false" returntype="struct" hint="Save demographic info">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		local.objMember = CreateObject("component","members");

		// clean incoming member
		local.incomingMemberID = int(val(arguments.event.getValue('memberID',0)));
		local.qryMember = local.objMember.getMember_demo(local.incomingMemberID);
		local.incomingMemberID = val(local.qryMember.memberID);
		arguments.event.setValue('memberID',local.incomingMemberID);
		</cfscript>

		<!--- save changes --->
		<cftry>
			<!--- check rights --->
			<cfif (arguments.event.getValue('memberID') gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and arguments.event.getValue('orgID') is arguments.event.getValue('mc_siteinfo.orgid'))))>
				<cfthrow message="Unable to save demographics.">
			<cfelseif (arguments.event.getValue('memberID') is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg')))>
				<cfthrow message="Unable to save demographics.">
			</cfif>
	
			<!--- save demographics, address, emails, websites, prof licenses, and custom fields --->
			<cfset local.strSaveResult = local.objMember.saveDemographics(event=arguments.event)>
			<cfif NOT local.strSaveResult.success>
				<cfthrow message="Unable to save demographics.">
			</cfif>

			<!--- change username. uses local.incomingMemberID because the saveDemographics call sets memberID into event --->
			<cfif local.incomingMemberID gt 0 and arguments.event.getValue('mc_siteinfo.useRemoteLogin') is not 1 and len(arguments.event.getTrimValue('newUserName',''))>
				<cfset application.objUser.login_setUsername(memberID=arguments.event.getValue('memberID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), username=arguments.event.getTrimValue('newUserName',''), recordedByMemberID=session.cfcuser.memberdata.memberID)>
			</cfif>

			<cfset local.finalSaveSuccess = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.finalSaveSuccess = false>
		</cfcatch>
		</cftry>

		<cfif local.finalSaveSuccess>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.MCModalUtils.hideModal();
					top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberID=#arguments.event.getValue('memberID')#';
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=4&mode=direct" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="addGroupManually" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.rc = arguments.event.getCollection();
			local.objMember = CreateObject("component","members");

			local.rc.memberID = int(val(arguments.event.getValue('memberID',0)));
			local.qryMember = local.objMember.getMember_demo(memberid=local.rc.memberID);
			
			local.resultsList = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=memberXML&meth=availableGroups&mode=stream&memberID=#local.rc.memberID#";
		</cfscript>
		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
			from dbo.organizations as o
			where o.orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfsavecontent variable="local.memberNamePrinted">
			<cfoutput>
			<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.qryMember.prefix)>#local.qryMember.prefix#</cfif> 
			#local.qryMember.firstname# 
			<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.qryMember.middlename)>#local.qryMember.middlename#</cfif> 
			#local.qryMember.lastname# 
			<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.qryMember.suffix)>#local.qryMember.suffix#</cfif> 
			<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.qryMember.professionalsuffix)>#local.qryMember.professionalsuffix#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.memberNamePrinted = encodeForHTML(ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL"))>
		<cfsavecontent variable="local.gridJS">
			<cfoutput>
			<script language="javascript">
			var mcg_gridQString = '#local.resultsList#';

			function addToGroup(gid,isv) {
				var addMemberResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') { 
						var rs = mcg_g.getSelectedId();
						var cs = mcg_g.getSelectedCellIndex();
						var prevCount = parseInt(mcg_g.cells(rs,cs+5).getValue()); 
						var newCount = prevCount + 1;
						mcg_g.cells(rs,cs+5).setValue(newCount);
						mcg_g.setCellExcellType(rs,cs+1,"img"); 
						mcg_g.cells(rs,cs+1).setValue("/assets/common/images/grid/accept.png^This member is manually assigned to this group.^^"); 
						mcg_g.cells(rs,cs).setValue("/assets/common/images/grid/cancel.png^Remove From Group^javascript:removeFromGroup(" + gid +"," + isv + ")^_self"); 
					} else { alert('There was a problem updating the group membership.'); }
					top.reqReload=1;
				};
				var objParams = { memberid:#local.rc.memberID#, groupid:gid };
				TS_AJX('GRPADM','addGroupMember',objParams,addMemberResult,addMemberResult,20000,addMemberResult);
			}
			function removeFromGroup(gid,isv) {
				var removeMemberResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') { 
						var rs = mcg_g.getSelectedId();
						var cs = mcg_g.getSelectedCellIndex();
						var prevCount = parseInt(mcg_g.cells(rs,cs+5).getValue()); 
						var newCount = prevCount - 1;
						mcg_g.cells(rs,cs+5).setValue(newCount);
						mcg_g.setCellExcellType(rs,cs+1,"ro"); 
						mcg_g.cells(rs,cs+1).setValue(""); 
						mcg_g.cells(rs,cs).setValue("/assets/common/images/grid/add.png^Add To Group^javascript:addToGroup(" + gid +","+isv+")^_self"); 
					} 
					else { alert('There was a problem updating the group membership.'); }
					top.reqReload=1;
				};
				var msg = 'Are you sure you want to remove this manual group assignment?\nClick OK to continue or CANCEL to return to the group listing.';
				if (confirm(msg)) {
					var objParams = { memberid:#local.rc.memberID#, groupid:gid };
					TS_AJX('GRPADM','removeGroupMember',objParams,removeMemberResult,removeMemberResult,20000,removeMemberResult);
				}
			}
			$(function(){
				let title='Group Assignments for #encodeForJavaScript(local.qryMember.memberNumber)# - #encodeForJavaScript(local.memberNamePrinted)#';
				<cfif len(local.qryMember.company)>
					title +='<h6>#encodeForHTML(local.qryMember.company)#</h6>';
				</cfif>
				top.$('##MCModalLabel').html(title);
			});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<br />
			<div class="p-2">Changes to group memberships are queued and refreshed as soon as possible.</div>
			<div id="mcg_gridbox" style="min-width:640px;height:300px;margin:6px 4px;"></div>
			<div style="min-width:640px;margin-bottom:20px;">
				<span class="fr">
					<i class="fa-solid fa-check-circle text-success"></i> Manually Assigned
					&nbsp; &nbsp; 
					<i class="fa-solid fa-check-circle text-primary"></i> Virtually Assigned
					&nbsp; &nbsp; 
					<i class="fa-solid fa-check-circle text-muted"></i> Indirectly Assigned
				</span>
			</div>
			<script>mcg_init();</script>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addProLicense" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.rc = arguments.event.getCollection();
			local.objMember = CreateObject("component","members");
			// get member ------------------------------------------------------------------------------- ::
			local.rc.memberID = int(val(arguments.event.getValue('memberID',0)));
			local.qryMember = local.objMember.getMember_demo(local.rc.memberID);
		
			local.listProLicensesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getProfessionalLicenses&memberID=#local.qryMember.memberid#&mode=stream';
		</cfscript>

		<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT hasPrefix, hasMiddleName, hasSuffix, hasProfessionalSuffix
			FROM dbo.organizations
			WHERE orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfsavecontent variable="local.memberNamePrinted">
			<cfoutput>
			<cfif local.qryOrgMemberFields.hasprefix is 1 and len(local.qryMember.prefix)>#local.qryMember.prefix#</cfif> 
			#local.qryMember.firstname# 
			<cfif local.qryOrgMemberFields.hasmiddlename is 1 and len(local.qryMember.middlename)>#local.qryMember.middlename#</cfif> 
			#local.qryMember.lastname# 
			<cfif local.qryOrgMemberFields.hassuffix is 1 and len(local.qryMember.suffix)>#local.qryMember.suffix#</cfif> 
			<cfif local.qryOrgMemberFields.hasprofessionalsuffix is 1 and len(local.qryMember.professionalsuffix)>#local.qryMember.professionalsuffix#</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfset local.memberNamePrinted = ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL")>

		<cfset local.qryOrgProfLicenseLabels = application.objOrgInfo.getOrgProfLicenseLabels(orgID=local.rc.mc_siteinfo.orgID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_memberProLicenses.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sendSingleWelcomeEmail" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.emailData = CreateObject('component','model.admin.welcomeEmail.welcomeEmailAdmin').findMembers(arguments.event,true,arguments.event.getValue('memberID'));
		</cfscript>
		
		<cfreturn returnAppStruct(local.emailData.data,"echo")>
	</cffunction>

	<cffunction name="copyRecordRelationshipForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.masterMemberID = int(val(arguments.event.getValue('masterMemberID',0)))>
	
		<cfif find("|",arguments.event.getTrimValue('fieldType',''))>
			<cfset local.fieldType = listFirst(arguments.event.getTrimValue('fieldType',''),"|")>
			<cfset local.fieldTypeID = listLast(arguments.event.getTrimValue('fieldType',''),"|")>
		<cfelse>
			<cfset local.fieldType = arguments.event.getTrimValue('fieldType','')>
			<cfset local.fieldTypeID = 0>
		</cfif>
		<cfset local.stepNumber =  arguments.event.getTrimValue('stepNumber',2)>
		<cfset local.formAttn =  arguments.event.getTrimValue('formAttn','')>	
		<cfset local.formAddressID =  arguments.event.getTrimValue('formAddressID','')>	
		<cfset local.formPhoneIDs =  arguments.event.getTrimValue('formPhoneIDs','')>
		<cfif listfindnocase("addressType,company,websiteType",local.fieldType)>
			<cfswitch expression="#local.fieldType#">
				<cfcase value="addressType">
					<cfset local.returnStruct.newheight = 640>
					<!--- get address/phone data for master member id for this addressTypeID --->
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddress">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT ma.addressID, mat.addressType,
							mat.hasAttn, mat.hasAddress2, mat.hasAddress3, mat.hasCounty,
							case when mat.hasAttn = 1 then ma.attn else '' end as attn, 
							ma.address1, 
							case when mat.hasAddress2 = 1 then ma.address2 else '' end as address2, 
							case when mat.hasAddress3 = 1 then ma.address3 else '' end as address3, 
							ma.city, ma.stateID, ma.postalCode, 
							case when mat.hasCounty = 1 then ma.county else '' end as county, 
							ma.countryID,
							(select countrycode from dbo.ams_countries where countryID = ma.countryID) as countrycode,
							ma.countryName as country, ma.stateCode, ma.stateName
						FROM dbo.ams_memberAddresses AS ma
						INNER JOIN dbo.ams_memberAddressTypes AS mat ON mat.addressTypeID = ma.addressTypeID
							AND mat.orgID = @orgID
							AND mat.addressTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fieldTypeID#">
						WHERE ma.orgID = @orgID
						AND ma.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.masterMemberID#">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPhones">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT mp.phone, mp.phoneTypeID, mpt.phoneType
						FROM dbo.ams_memberPhones AS mp 
						INNER JOIN dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID
							AND mpt.phoneTypeID = mp.phoneTypeID
						WHERE mp.orgID = @orgID
						AND mp.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.masterMemberID#">
						<cfif local.stepNumber EQ 3 AND local.formPhoneIDs NEQ "">
							AND mp.phoneTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.formPhoneIDs#"> )
						</cfif>
						AND mp.addressID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryAddress.addressID#"> 
						AND LEN(mp.phone) > 0;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
				</cfcase>
				<cfcase value="company">
					<cfset local.returnStruct.newheight = 550>
					<!--- get companyname for master member --->
					<cfquery name="local.qryCompany" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
						SELECT company
						FROM dbo.ams_members
						WHERE memberid = <cfqueryparam value="#local.masterMemberID#" cfsqltype="CF_SQL_INTEGER">
						AND orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
				</cfcase>
				<cfcase value="websiteType">
					<cfset local.returnStruct.newheight = 550>
					<!--- get website data for master member id for this websiteTypeID --->
					<cfquery name="local.qryWebsites" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT mw.websiteTypeID, mw.websiteID, mw.website, mwt.websiteType, mwt.websiteTypeDesc
						FROM dbo.ams_memberWebsites as mw
						INNER JOIN dbo.ams_memberWebsiteTypes as mwt on mwt.orgID = @orgID
							AND mwt.websiteTypeID = mw.websiteTypeID
						WHERE mw.orgID = @orgID
						AND mw.memberID = <cfqueryparam value="#local.masterMemberID#" cfsqltype="CF_SQL_INTEGER">
						AND	mw.websiteTypeID = <cfqueryparam value="#local.fieldTypeID#" cfsqltype="CF_SQL_INTEGER">
						ORDER BY mwt.websiteTypeOrder;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryWebsiteType">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
						SELECT mwt.websiteType
						FROM dbo.ams_memberWebsiteTypes mwt 
						where mwt.websiteTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.fieldTypeID)#">
						and mwt.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
				</cfcase>
			</cfswitch>
				
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_manageRecordRelationshipCopyDataFrom.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "Invalid Field Type.">
		</cfif>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateMemberPhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.rc = arguments.event.getCollection();
			local.objMember = CreateObject("component","members");
			// get member ------------------------------------------------------------------------------- ::
			local.rc.memberID = int(val(arguments.event.getValue('memberID',0)));
			local.qryMember = local.objMember.getMember_demo(local.rc.memberID);
			local.rc.memberID = int(val(local.qryMember.memberID));
		
			local.link.saveRemovePhoto = buildCurrentLink(arguments.event,"saveRemovePhoto") & "&mode=direct";
			local.link.saveAddPhoto = buildCurrentLink(arguments.event,"saveAddPhoto") & "&mode=direct";
			local.link.updateCropPhoto = buildCurrentLink(arguments.event,"updateCropPhoto") & "&mode=direct";
			local.link.downloadPhoto = buildCurrentLink(arguments.event,"downloadPhoto") & "&mode=direct";

			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(arguments.event.getValue('mc_siteinfo.sitecode'));

			local.fileExists = fileexists(application.paths.localUserAssetRoot.path & LCASE(arguments.event.getValue('mc_siteinfo.orgcode')) & "/memberphotos/" & LCASE(local.qryMember.membernumber) & ".jpg");
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_profilePicture.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="savePhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			local.rc = arguments.event.getCollection();
			local.loginProcess = FALSE;
			
			if (arguments.event.valueExists('loginProcessLink'))
				local.loginProcess = TRUE;
			
			local.objMember = CreateObject("component","members");
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(local.rc.mc_siteinfo.siteCode);
			
			// get member ------------------------------------------------------------------------------- ::
			local.rc.memberID = int(val(arguments.event.getValue('memberID',0)));
			local.qryMember = local.objMember.getMember_demo(local.rc.memberID);
			local.rc.memberID = int(val(local.qryMember.memberID));
		</cfscript>
			
		<!--- decrypt photodir --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.decryptDownloadURL(d=arguments.event.getValue('photodir',''))>
		<cfcatch type="Any">
			<cflocation url="#buildCurrentLink(arguments.event,"search")#" addtoken="false">
		</cfcatch>
		</cftry>

		<cfset local.memberFullImagePath_LG = "#local.strFolder.sourceFilePath#/LG_#local.rc.memberID#.jpg">
		<cfset local.memberFullImagePath_final_LG = "#local.strFolder.sourceFilePath#/final_LG_#local.rc.memberID#.jpg">
		<cfset local.photoUploaded = false>
		<cfset local.photoFolder = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.rc.mc_siteinfo.orgcode)#/memberphotos/">
		<cfset local.photoDestination = local.photoFolder & "#LCASE(local.qryMember.memberNumber)#.jpg">

		<!--- crop photo --->
		
		<cfif fileExists(local.memberFullImagePath_LG) and arguments.event.getValue('w',0) gt 0 and arguments.event.getValue('h',0) gt 0>
			<cfif arguments.event.getValue('x',0) LT 0>
				<cfset local.x = 0>
			<cfelse>
				<cfset local.x = Int(arguments.event.getValue('x',0))>
			</cfif>
			<cfif arguments.event.getValue('y',0) LT 0>
				<cfset local.y = 0>
			<cfelse>
				<cfset local.y = Int(arguments.event.getValue('y',0))>
			</cfif>
			<cfset local.cropPoints = application.objCommon.getManualCropPointsForThumbor(x=local.x,y=local.y,width=Int(arguments.event.getValue('w',0)),height=Int(arguments.event.getValue('h',0)))>
			<cfset local.command = "#local.cropPoints#/#local.qrySiteSettings.memberPhotoWidth#x#local.qrySiteSettings.memberPhotoHeight#">
			<cfset local.cropRequest = application.objCommon.thumborImageTranform(command=local.command,filePath=local.memberFullImagePath_LG, outputfilePath=local.photoDestination)>
			
			<cfset local.photoUploaded = local.cropRequest.success>

			<cfif local.cropRequest.success>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_saveMemberPhoto">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
					<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('memberid')#">
				</cfstoredproc>

				<cfset createObject('component','model.system.platform.history').addMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'),
					actorMemberID=session.cfcuser.memberdata.memberID, receiverMemberID=arguments.event.getValue('memberid'), 
					mainMessage="Member Photo Added (Admin)")>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div class="mt-4 text-center">
					<div class="spinner-border"></div><div class="font-weight-bold mt-2">Please wait...</div>
				</div>
				<script language="javascript">
					<cfif not local.photoUploaded>
					alert("There was a problem with your upload. Please provide a photo with the minimum size requirements specified in Member Settings.");
					</cfif>
					top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#arguments.event.getValue('memberID')#&msg=1&tab=profile';
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="updateCropPhoto" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.rc = arguments.event.getCollection();
			local.objMember = CreateObject("component","members");
			local.objWebsite = CreateObject('component', 'model.admin.website.website');
			local.qrySiteSettings = local.objWebsite.getSettings(local.rc.mc_siteinfo.siteCode);
			// get member ------------------------------------------------------------------------------- ::
			local.rc.memberID = int(val(arguments.event.getValue('memberID',0)));
			local.qryMember = local.objMember.getMember_demo(local.rc.memberID);
			local.rc.memberID = int(val(local.qryMember.memberID));
		</cfscript>

		<!--- upload the photo --->
		<cfif len(arguments.event.getTrimValue('newFile'))>
			
			<!--- create temp folder --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.imgDirEnc = local.strFolder.folderPathEnc>

			<!--- upload photo --->
			<cffile action="UPLOAD" filefield="newFile" destination="#local.strFolder.folderPath#" nameconflict="MAKEUNIQUE" result="local.uploadedPhoto">
			
			<!--- if uploaded --->
			<cfif local.uploadedPhoto.fileWasSaved>
				<cftry>
					<cfset local.photoExt = local.uploadedPhoto.serverfileext>
					<cfset local.serverDirectoryAndFileName = "#local.uploadedPhoto.ServerDirectory#/#local.uploadedPhoto.serverFileName#">
					<cfset local.serverDirectoryAndFileName_LG = "#local.uploadedPhoto.ServerDirectory#/LG_#local.rc.memberID#.jpg">

					<cfset local.processPhoto = application.objCommon.thumborImageTranform(command="fit-in/500x500",filePath="#local.serverDirectoryAndFileName#.#local.uploadedPhoto.serverfileext#", outputfilePath=local.serverDirectoryAndFileName_LG)>
					<cfset local.resultPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.serverDirectoryAndFileName_LG)>

					<cfset local.success = local.processPhoto.success>
					<cfset local.imageHeight = local.resultPhotoInfo.imageInfo.source.height>
					<cfset local.imageWidth = local.resultPhotoInfo.imageInfo.source.width>
					<!--- copy to /temp for preview purposes --->
					<cfset local.imgInTemp = "#createUUID()##createUUID()#.#local.uploadedPhoto.serverfileext#">
					<cffile action="copy" destination="#application.paths.RAIDTemp.path##local.imgInTemp#" source="#local.serverDirectoryAndFileName_LG#">

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="frm_profilePictureCrop.cfm">
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="frm_profilePictureCrop.cfm">
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="showDeleteInfo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objMember = CreateObject("component","members")>

		<cfset local.orgid = arguments.event.getValue('mc_siteinfo.orgid')>
		<cfset local.memberID = local.objMember.getMember_validcheck(orgID=local.orgid, memberID=arguments.event.getValue('memberID',0))>

		<cfif arguments.event.getValue('memberID',0) gt 0 AND NOT (checkRights(arguments.event,'EditAll') OR checkRights(arguments.event,'EditOrg'))>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
		<cfelseif arguments.event.getValue('memberID',0) is 0 AND NOT (checkRights(arguments.event,'AddAll') OR checkRights(arguments.event,'AddOrg'))>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
		<cfelseif arguments.event.getValue('memberID',0) gt 0 and local.memberID is 0>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=2" addtoken="no">
		</cfif>

		<cfset local.qryMember = local.objMember.getMember_demo(memberid=local.memberid)>
		<cfset local.memberNamePrinted = "#local.qryMember.prefix# #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix# #local.qryMember.professionalsuffix#">
		<cfset local.memberNamePrinted = ReReplace(trim(local.memberNamePrinted),"\s{2,}"," ","ALL") & " (#local.qryMember.memberNumber#)">

		<cfquery name="local.qryMemberHistory" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">, 
				@memberID int = <cfqueryparam value="#local.memberid#" cfsqltype="CF_SQL_INTEGER">, 
				@mhCount1 int, @mhCount2 int;
			declare @orgSites table (siteID int);

			insert into @orgSites (siteID)
			select siteID
			from dbo.sites
			where orgID = @orgID;

			select @mhCount1 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.memberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 1
			and mh.status = 'A';

			select @mhCount2 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.linkmemberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 1
			and mh.status = 'A';

			select isnull(@mhCount1,0) + isnull(@mhCount2,0) as theCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryRelationships" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">, 
				@memberID int = <cfqueryparam value="#local.memberid#" cfsqltype="CF_SQL_INTEGER">, 
				@mhCount1 int, @mhCount2 int;
			declare @orgSites table (siteID int);

			insert into @orgSites (siteID)
			select siteID
			from dbo.sites
			where orgID = @orgID;

			select @mhCount1 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.memberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 2
			and mh.status = 'A';

			select @mhCount2 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.linkmemberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 2
			and mh.status = 'A';

			select isnull(@mhCount1,0) + isnull(@mhCount2,0) as theCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryNotes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">, 
				@memberID int = <cfqueryparam value="#local.memberid#" cfsqltype="CF_SQL_INTEGER">, 
				@mhCount1 int, @mhCount2 int;
			declare @orgSites table (siteID int);

			insert into @orgSites (siteID)
			select siteID
			from dbo.sites
			where orgID = @orgID;

			select @mhCount1 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.memberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 3
			and mh.status = 'A';

			select @mhCount2 = count(distinct mh.historyID)
			from dbo.ams_memberHistory as mh 
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = mh.linkmemberID
				and mh.siteID in (select siteID from @orgSites)
			where m.activeMemberID = @memberID
			and mh.typeID = 3
			and mh.status = 'A';

			select isnull(@mhCount1,0) + isnull(@mhCount2,0) as theCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryTransactions" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select count(distinct t.transactionID) as theCount
			from dbo.tr_transactions as t
			inner join dbo.ams_members as m on m.orgID = @orgID
				and m.memberID = t.assignedToMemberID
			where m.activeMemberID = <cfqueryparam value="#local.memberid#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberForm_deleteInfo.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="delete" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objMember = CreateObject("component","members");
		
		// lookup member
		local.memberID = int(val(arguments.event.getValue('memberID',0)));
		local.qryMember = local.objMember.getMember_demo(local.memberID);
		
		// no member found
		if (local.qryMember.recordcount is 0)
			application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=2');

		// check rights
		if (NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and local.qryMember.orgID is arguments.event.getValue('mc_siteinfo.orgID'))))
			application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=1');

		// delete member
		local.apidelPayload = { _mcrecordedbymemberid:session.cfcuser.memberdata.memberID };
		application.objMCAPI.api(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), method="DELETE", endpoint="member/#local.qryMember.memberNumber#", payload=serializeJSON(local.apidelPayload));
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>top.closeDelete();</script>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
			
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Error</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>We're sorry, you do not have rights to this function.</b></cfcase>
							<cfcase value="2"><b>That member was not found.</b></cfcase>
							<cfcase value="3"><b>That member was not found. Try the merge again.</b></cfcase>
							<cfcase value="4">
								<b>There was a problem saving this member's information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
								<script language="javascript">top.reqReload=1;</script>
							</cfcase>
							<cfcase value="5"><b>Some error occured while exporting condition group change history.</b></cfcase>
							<cfcase value="6"><b>That member is not available.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getLists" access="public" output="false" returntype="query" hint="Get Lyris Lists for this Site">
		<cfargument name="siteID" type="Numeric" required="true">
		
		<cfset var qryLists = "">

		<cftry>
			<cfquery name="qryLists" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,1,0)#">
				SELECT mcl.listID, mcl.listName, mcl.siteResourceID, tlaL.descShort_ as listDesc, tlaLF.orgcode, tlaLF.dateentered, 
					tlaLF.datelastmodified, tlaLF.disabled, mcL.supportsMCThreadIndex, mcL.supportsMCThreadDigest, mcL.isLaunchedMCThreadDigest, mcL.isLaunchedMCThreadIndex
				FROM dbo.lists_lists mcL 
				INNER JOIN dbo.cms_siteResources lsr ON mcl.siteResourceID = lsr.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses srs on srs.siteResourceStatusID = lsr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
				INNER JOIN LYRIS.trialslyris1.dbo.lists_format tlaLF ON tlaLF.name = mcL.listName collate Latin1_General_CI_AI
				INNER JOIN LYRIS.trialslyris1.dbo.lists_ as tlaL ON tlaL.name_ = tlaLF.Name collate Latin1_General_CI_AI
				WHERE lsr.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			</cfquery>
		<cfcatch type="Any">
			<cfset qryLists = queryNew("listID, listName, siteResourceID, listDesc, orgcode, dateentered, datelastmodified, disabled, supportsMCThreadIndex, supportsMCThreadDigest, isLaunchedMCThreadDigest, isLaunchedMCThreadIndex")>
		</cfcatch>
		</cftry>

		<cfreturn qryLists>
	</cffunction>
	
	<cffunction name="resetLoginCrd" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.memberID = arguments.event.getValue('memberID')>
		<cfset local.getMFAMethodsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getMFAMethods&memberID=#local.memberID#&mode=stream">
		<cfscript>
			local.hasLoginPolicy = CreateObject('component', 'model.admin.website.website').hasLoginPolicy(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			if (local.hasLoginPolicy) {
				local.objLogin = CreateObject("component","model.login.login");
				local.qryLoginPolicyMethods = local.objLogin.getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.memberID);
				if (local.qryLoginPolicyMethods.recordCount) {
					local.arrConfiguredMethods = local.objLogin.getConfiguredLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteid'), memberID=local.memberID);	
				}
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_member_reset_login_crd.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="resetPassword" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objLogin = CreateObject("component","model.login.login");
			
			local.emailData = application.objMember.getMainEmail(arguments.event.getValue('memberID'));
			arguments.event.setValue('email',local.emailData.email);
			arguments.event.setValue('resetMemberID',arguments.event.getValue('memberID'));
			local.data = local.objLogin.adminResetRequest(arguments.event);
		</cfscript>
		
		<cflocation url="#buildCurrentLink(arguments.event,"edit")#&memberID=#arguments.event.getValue('memberID')#&mailMSG=#local.data#" addtoken="FALSE">
	</cffunction>
	
	<cffunction name="mergeMembers" access="public" output="false" returntype="struct" hint="Merge Members">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.rc.memberID = int(val(arguments.event.getValue('memberID',0)))>
		<cfset local.rc.mergeMemberIDs = arguments.event.getTrimValue('frmMergeMemberIDList','')>
		<cfset local.qryMember = local.objMember.getMember_demo(local.rc.memberID)>

		<!--- check rights --->
		<cfif (NOT (checkRights(arguments.event,'EditAll') OR (checkRights(arguments.event,'EditOrg') and local.qryMember.orgID is local.rc.mc_siteinfo.orgid)))>
			<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1&mode=direct" addtoken="no">
		</cfif>

		<cfswitch expression="#arguments.event.getValue('mergeact','chooseMergeAcct')#">

			<cfcase value="compare">
				<cfset local.qryAllMembers = local.objMember.getAllMergeMembers(memberID=local.rc.memberID, orgID=local.rc.mc_siteinfo.orgID, mergeMemberIDs=local.rc.mergeMemberIDs)>
				<cfset local.qryProtectedMemberAccounts = QueryFilter(local.qryAllMembers, function(thisRow){ return arguments.thisRow.isProtected EQ 1; })>
				<cfif local.qryAllMembers.recordcount LT 2 OR local.qryAllMembers.recordcount GT 4 OR local.qryProtectedMemberAccounts.recordCount>
					<cfsavecontent variable="local.data">
						<cfoutput>
						<div class="alert alert-danger">
							<cfif local.qryAllMembers.recordcount LT 2>
								Not enough members were selected for the merge.
							<cfelseif local.qryAllMembers.recordcount GT 4>
								You have selected #local.qryAllMembers.recordcount - 1# members. Select up to 3 to merge.
							<cfelseif local.qryProtectedMemberAccounts.recordCount>
								You cannot choose a protected member for the merge.
							</cfif>
						</div>
						</cfoutput>
					</cfsavecontent>
				<cfelse>
					<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.rc.mc_siteinfo.orgID)>
					<cfset local.qryOrgProfessionalLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.rc.mc_siteinfo.orgID)>

					<cfquery name="local.qryOrgMemberFields" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix
						from dbo.organizations as o
						where o.orgID = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(local.rc.mc_siteinfo.orgID)>
					
					<cfset local.strAllMembers = structNew()>
					<cfloop query="local.qryAllMembers">
						<cfset local.strAllMembers[local.qryAllMembers.memberid] = local.objMember.getMember(local.qryAllMembers.memberID,local.rc.mc_siteinfo.orgID)>
					</cfloop>
					
					<!--- get manual group assignments for all members (what it will be after merge) --->
					<cfquery name="local.qryNewManualGroups" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#local.rc.mc_siteinfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

						select distinct g.groupID, g.groupPathExpanded as groupPath
						from dbo.ams_memberGroups as mg
						inner join dbo.ams_groups as g on g.orgID = @orgID
							and g.groupID = mg.groupID
							and g.status = 'A'
							and g.hideOnGroupLists = 0
						where mg.orgID = @orgID
						and mg.memberID in (#valuelist(local.qryAllMembers.memberid)#);

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<cfquery name="local.qryNewChildRelationships" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#">;

						select distinct
							'Child' as memberRole,
							masterMember.memberNumber as masterMemberNumber,
							masterMember.memberID as masterMemberID,
							masterMember.firstname as masterFirstName,
							masterMember.lastname as masterLastName,
							masterMember.company as masterCompany,
							rrt.relationshipTypeName
						from dbo.ams_members m
						inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID 
							and rr.childMemberID = m.memberID
							and m.memberID in (#valuelist(local.qryAllMembers.memberid)#)
							and rr.isActive = 1
						inner join dbo.ams_members masterMember on masterMember.orgID = @orgID 
							and rr.masterMemberID = masterMember.memberID
							and masterMember.status in ('A','I')
						inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
						inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
						where m.orgID = @orgID;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfquery name="local.qryNewMasterRelationships" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#">;

						select distinct
							'Parent' as memberRole,
							rrt.relationshipTypeName,
							childMember.memberNumber as childMemberNumber,
							childMember.memberID as childMemberID,
							childMember.firstname as childFirstname,
							childMember.lastname as childLastName,
							childMember.company as childCompany
						from dbo.ams_members m
						inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID 
							and rr.masterMemberID = m.memberID
							and m.memberID in (#valuelist(local.qryAllMembers.memberid)#)
							and rr.isActive = 1
						inner join dbo.ams_members childMember on childMember.orgID = @orgID 
							and rr.childMemberID = childMember.memberID
							and childMember.status in ('A','I')
						inner join dbo.ams_recordTypesRelationshipTypes rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
						inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
						where m.orgID = @orgID;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfquery name="local.qryNewManualGroups" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#">;

						select distinct g.groupID, g.groupPathExpanded as groupPath
						from dbo.ams_memberGroups as mg
						inner join dbo.ams_groups as g on g.orgID = @orgID
							and g.groupID = mg.groupID
							and g.status = 'A'
							and g.hideOnGroupLists = 0
						where mg.orgID = @orgID
						and mg.memberID in (#valuelist(local.qryAllMembers.memberid)#);

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<!--- get any duplicate event registrations (duplicates after merge) --->
					<cfquery name="local.qryNewDupeRegistrations" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select e.eventid, et.startTime, ec.contentTitle
						from dbo.ev_registrants as r
						inner join dbo.ams_members as m on m.memberID = r.memberid
						inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID
							and r.recordedOnSiteID = reg.siteID
							and r.status = 'A'
							and reg.status = 'A'
						inner join dbo.ev_events as e on e.eventID = reg.eventID
							and e.siteID = reg.siteID
							and e.status = 'A'
						inner join dbo.sites as s on s.siteID = e.siteID
						inner join dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = s.defaultTimeZoneID
						inner join dbo.cms_contentLanguages as ec on ec.contentID = e.eventContentID and ec.languageID = 1
						where m.activeMemberID in (#valuelist(local.qryAllMembers.memberid)#)
						group by e.eventid, et.startTime, ec.contentTitle
						having count(r.memberID) > 1
						order by 2, 3;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfquery name="local.qryMNP" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT mnp.memberID,
							COUNT(mnp.mnpID) AS mnpCount,
							STRING_AGG(s.siteCode + '^***^' + COALESCE(mnp.UserName,np.username,'N/A') + '^***^' + ISNULL(CONVERT(varchar, mnp.dateLastLogin, 120),'N/A') + '^***^' + ISNULL(mnp.MFAPhoneNumber,'N/A'),'^---^') as mnpInfo
						FROM dbo.ams_memberNetworkProfiles AS mnp
						INNER JOIN dbo.ams_networkProfiles AS np ON np.profileID = mnp.profileID
						INNER JOIN dbo.sites AS s ON s.siteID = mnp.siteID
							AND s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rc.mc_siteinfo.orgID#">
						WHERE mnp.memberID IN (#valuelist(local.qryAllMembers.memberid)#)
						AND mnp.[status] = 'A'
						GROUP BY mnp.memberID;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<cfscript>
					local.arrSections = ArrayNew(1);
					
					// acct details section
					local.arrSection = ArrayNew(1);
					arrayAppend(local.arrSection,merge_showRowCreatedDate(qryAllMembers=local.qryAllMembers,colLabel='Date Created',domLabel='createddate'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='MCAccountStatus',domLabel='status',rdoValue='status',rdoLabel='statusFull'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='MCAccountType',domLabel='membertypeid',rdoValue='membertypeid',rdoLabel='membertype'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='MCRecordType',domLabel='recordtypeid',rdoValue='recordtypeid',rdoLabel='recordTypeName'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='MemberNumber',domLabel='membernumber',rdoValue='membernumber',rdoLabel='membernumber'));
					local.tmpstr = { data=local.arrSection, head='<b>Account Details</b>' };
					arrayAppend(local.arrSections, local.tmpstr);
			
					// demo data
					local.arrSection = ArrayNew(1);
					if (local.qryOrgMemberFields.hasPrefix is 1)
						arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Prefix',domLabel='prefix',rdoValue='prefix',rdoLabel='prefix'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='First Name',domLabel='firstname',rdoValue='firstname',rdoLabel='firstname'));
					if (local.qryOrgMemberFields.hasMiddleName is 1)
						arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Middle Name',domLabel='middlename',rdoValue='middlename',rdoLabel='middlename'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Last Name',domLabel='lastname',rdoValue='lastname',rdoLabel='lastname'));
					if (local.qryOrgMemberFields.hasSuffix is 1)
						arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Suffix',domLabel='suffix',rdoValue='suffix',rdoLabel='suffix'));
					if (local.qryOrgMemberFields.hasProfessionalSuffix is 1)
						arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Professional Suffix',domLabel='professionalsuffix',rdoValue='professionalsuffix',rdoLabel='professionalsuffix'));
					arrayAppend(local.arrSection,merge_showRowDefault(qryAllMembers=local.qryAllMembers,colLabel='Company',domLabel='company',rdoValue='company',rdoLabel='company'));
					arrayAppend(local.arrSection,merge_showRowMemberPhoto(qryAllMembers=local.qryAllMembers, photoPath=application.paths.localUserAssetRoot.path & LCASE(arguments.event.getValue('mc_siteinfo.orgcode')) & "/memberphotosth/"));
					local.tmpstr = { data=local.arrSection, head='<b>Basics</b>' };
					arrayAppend(local.arrSections, local.tmpstr);
					
					// contact info
					local.arrSection = ArrayNew(1);
					arrayAppend(local.arrSection,merge_showRowEmailTags(qryAllMembers=local.qryAllMembers,qryOrgEmailTags=local.qryOrgEmailTags,strAllMembers=local.strAllMembers));
					arrayAppend(local.arrSection,merge_showRowEmails(qryAllMembers=local.qryAllMembers,qryOrgEmails=local.qryOrgEmails,strAllMembers=local.strAllMembers));
					arrayAppend(local.arrSection,merge_showRowWebsites(qryAllMembers=local.qryAllMembers,qryOrgWebsites=local.qryOrgWebsites,strAllMembers=local.strAllMembers));
					arrayAppend(local.arrSection,merge_showRowAddressTags(qryAllMembers=local.qryAllMembers,qryOrgAddressTags=local.qryOrgAddressTags,strAllMembers=local.strAllMembers));
					arrayAppend(local.arrSection,merge_showRowAddresses(qryAllMembers=local.qryAllMembers,qryOrgAddresses=local.qryOrgAddresses,qryOrgPhones=local.qryOrgPhones,strAllMembers=local.strAllMembers));
					local.tmpstr = { data=local.arrSection, head='<b>Contact Information</b>' };
					arrayAppend(local.arrSections, local.tmpstr);
			
					// professional licenses
					local.arrSection = ArrayNew(1);
					arrayAppend(local.arrSection,merge_showRowLicenses(qryAllMembers=local.qryAllMembers,qryOrgProfessionalLicenses=local.qryOrgProfessionalLicenses,strAllMembers=local.strAllMembers));
					local.tmpstr = { data=local.arrSection, head='<b>Professional Licenses</b>' };
					arrayAppend(local.arrSections, local.tmpstr);

					// custom fields
					local.arrSection = ArrayNew(1);
					arrayAppend(local.arrSection,merge_showRowCustomFields(qryAllMembers=local.qryAllMembers,xmlAdditionalData=local.xmlAdditionalData,strAllMembers=local.strAllMembers));
					local.tmpstr = { data=local.arrSection, head='<b>Custom Fields</b>' };
					arrayAppend(local.arrSections, local.tmpstr);

					// login data
					if(local.qryMNP.recordCount gt 1){
						local.arrSection = ArrayNew(1);
						arrayAppend(local.arrSection,merge_showRowMNP(qryAllMembers=local.qryAllMembers, qryMNP=local.qryMNP));
						local.tmpstr = { data=local.arrSection, head='<b>Select the Member whose login information will be used for the newly created member</b>' };
						arrayAppend(local.arrSections, local.tmpstr);
					}
					
					</cfscript>
			
					<cfsavecontent variable="local.data">
						<cfoutput>
						<cfinclude template="dsp_mergeMemberForm.cfm">
						</cfoutput>
					</cfsavecontent>
				</cfif>
			</cfcase>

			<!--- chooseMergeAcct --->
			<cfdefaultcase> 
				<cfset local.qryMember = local.objMember.getMember_demo(local.rc.memberID)>
				<cfif local.qryMember.recordcount IS 0 OR local.qryMember.isProtected IS 1>
					<cfsavecontent variable="local.data">
						<cfoutput>
						<b>Sorry!</b><br/><br/>
						That member was not #local.qryMember.isProtected IS 1 ? 'available' : 'found'#.
						</cfoutput>
					</cfsavecontent>
				<cfelse>
					<cfset local.showMemberPhotosInSearchResults = xmlSearch(this.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)') EQ "true" ? true : false>
					<cfset local.matchStatement = local.objMember.getMergeMatchStatement(orgID=local.rc.mc_siteinfo.orgID)>

					<!--- auto close should be 0 -- otherwise results in a javascript error --->
					<cfset local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & '&mode=stream&autoClose=0&dispTitle=&retFunction=onSelectMergeMember'>

					<cfsavecontent variable="local.data">
						<cfinclude template="dsp_mergeMemberFormSelect.cfm">
					</cfsavecontent>
				</cfif>
			</cfdefaultcase>

		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="mergeMembersConfirm" access="public" output="false" returntype="struct" hint="Merge Members">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'', "typeID":0 }>
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.orgID = arguments.event.getValue('mc_siteinfo.orgid')>
		<cfset local.returnFunction = arguments.event.getTrimValue('returnFunction','')>
		<cfset var skipFormFieldsList = arguments.event.getTrimValue('skipFormFieldsList','')>
		
		<cfset local.chosenMemberNumber = arguments.event.getValue('rdo_membernumber_')>
		<cfset var chosenMemberID = application.objMember.getMemberIDByMemberNumber(memberNumber=local.chosenMemberNumber, orgID=local.orgID)>
		<cfset local.allMemberIDs = arguments.event.getValue('allMemberIDs',0)>
		<cfset local.mergeMemberIDs = local.allMemberIDs.listFilter(function(id){ return id != chosenMemberID; })>

		<cfquery name="local.qryMergingMemberNumbers" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT memberNumber 
			FROM dbo.ams_members 
			WHERE memberID IN (<cfqueryparam value="#local.mergeMemberIDs#" cfsqltype="CF_SQL_INTEGER" list="Yes">)
			AND orgID = <cfqueryparam value="#local.orgID#" cfsqltype="CF_SQL_INTEGER">
			AND [status] <> 'D';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;	
		</cfquery>

		<!--- already merged --->
		<cfif NOT local.qryMergingMemberNumbers.recordCount>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#chosenMemberID#';
					top.MCModalUtils.hideModal();
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfset local.defaultMemberDataMapping = {
			"MCAccountStatus": "rdo_status_",
			"MCAccountType": "rdo_membertypeid_",
			"MCRecordType": "rdo_recordtypeid_",
			"MemberNumber": "rdo_membernumber_",
			"Prefix": "rdo_prefix_",
			"FirstName": "rdo_firstname_",
			"MiddleName": "rdo_middlename_",
			"LastName": "rdo_lastname_",
			"Suffix": "rdo_suffix_",
			"ProfessionalSuffix": "rdo_professionalsuffix_",
			"Company": "rdo_company_"
		}>

		<cftry>
			<cfset local.payLoadData = {
				"_mcrecordedbymemberid": session.cfcuser.memberdata.memberID,
				"membernumber": ValueArray(local.qryMergingMemberNumbers,"memberNumber"),
				"memberdata": {}
			}>

			<cfloop collection="#local.defaultMemberDataMapping#" item="local.thisDataKey">
				<cfset local.thisFormFieldName = local.defaultMemberDataMapping[local.thisDataKey]>
				<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
					<cfif local.thisDataKey eq "MCAccountStatus">
						<cfset local.dataValue = arguments.event.getValue(local.thisFormFieldName,'') == 'A' ? 'Active' : 'Inactive'>
					<cfelseif local.thisDataKey eq "MCAccountType">
						<cfquery name="local.qryMemberType" datasource="#application.dsn.membercentral.dsn#">
							SELECT memberType
							FROM dbo.ams_memberTypes
							WHERE memberTypeID = <cfqueryparam value="#arguments.event.getValue(local.thisFormFieldName,0)#" cfsqltype="CF_SQL_VARCHAR">
						</cfquery>
						<cfset local.dataValue = local.qryMemberType.memberType>
					<cfelseif local.thisDataKey eq "MCRecordType">
						<cfquery name="local.qryRecordType" datasource="#application.dsn.membercentral.dsn#">
							SELECT recordTypeName
							FROM dbo.ams_recordTypes
							WHERE recordTypeID = <cfqueryparam value="#arguments.event.getValue(local.thisFormFieldName,0)#" cfsqltype="CF_SQL_VARCHAR">
							AND orgID = <cfqueryparam value="#local.orgID#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
						<cfset local.dataValue = local.qryRecordType.recordTypeName>
					<cfelse>
						<cfset local.dataValue = arguments.event.getValue(local.thisFormFieldName,'')>
					</cfif>

					<cfset StructAppend(local.payLoadData.memberdata, { "#local.thisDataKey#":local.dataValue })>
				</cfif>
			</cfloop>

			<cfif len(arguments.event.getValue('rdo_membernumberforlogindata_',''))>
				<cfset StructAppend(local.payLoadData.memberdata, {"MemberNumberForLoginData":arguments.event.getValue('rdo_membernumberforlogindata_')})>
			</cfif>

			<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=local.orgID)>
			<cfset local.qryOrgAddressTags = application.objOrgInfo.getOrgAddressTagTypes(orgID=local.orgID)>
			<cfset local.qryOrgAddresses = application.objOrgInfo.getOrgAddressTypes(orgID=local.orgID)>
			<cfset local.qryOrgPhones = application.objOrgInfo.getOrgPhoneTypes(orgID=local.orgID)>
			<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(orgID=local.orgID)>
			<cfset local.qryOrgEmails = application.objOrgInfo.getOrgEmailTypes(orgID=local.orgID)>
			<cfset local.qryOrgWebsites = application.objOrgInfo.getOrgWebsiteTypes(orgID=local.orgID)>
			<cfset local.qryOrgProfessionalLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.orgID)>

			<cfquery name="local.qryPayloadData" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpMemberData') IS NOT NULL 
					DROP TABLE ##tmpMemberData;
				CREATE TABLE ##tmpMemberData (autoID INT, name sysname, value varchar(max));

				DECLARE @orgID int, @email varchar(255), @website varchar(400), @attn varchar(100), @address1 varchar(100),
					@address2 varchar(100), @address3 varchar(100), @city varchar(75), @postalCode varchar(25), @county varchar(50),
					@country varchar(100), @stateCode varchar(4), @licenseNumber varchar(200), @licenseActiveDate varchar(12),
					@licenseStatusName varchar(200), @addressType varchar(20), @emailType varchar(20), @columnValue varchar(max);

				SET @orgID = <cfqueryparam value="#local.orgID#" cfsqltype="CF_SQL_INTEGER">;

				<cfloop query="local.qryOrgEmailTags">
					<cfset local.thisFormFieldName = "rdo_memberEmailTagID_#local.qryOrgEmailTags.emailTagTypeID#_">

					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						set @emailType = null;

						select @emailType = emailType
						from dbo.ams_memberEmailTypes 
						where orgID = @orgID 
						and emailTypeID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values(<cfqueryparam value="Designated #local.qryOrgEmailTags.emailTagType#_emailType" cfsqltype="CF_SQL_VARCHAR">, isnull(@emailType,''));
					</cfif>
				</cfloop>

				<cfloop query="local.qryOrgEmails">
					<cfset local.thisEmailTypeID = local.qryOrgEmails.emailTypeID>
					<cfset local.thisFormFieldName = "rdo_memberEmailID_#local.thisEmailTypeID#_">

					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						set @email = null;
						
						select @email = email
						from dbo.ams_memberEmails
						where orgID = @orgID
						and emailID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">
						and emailTypeID = <cfqueryparam value="#local.thisEmailTypeID#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values(<cfqueryparam value="#local.qryOrgEmails.emailType#" cfsqltype="CF_SQL_VARCHAR">, isnull(@email,''));
					</cfif>
				</cfloop>

				<cfloop query="local.qryOrgWebsites">
					<cfset local.thisWebsiteTypeID = local.qryOrgWebsites.websiteTypeID>
					<cfset local.thisFormFieldName = "rdo_memberWebsiteID_#local.thisWebsiteTypeID#_">
					
					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						set @website = null;
						
						select @website = website
						from dbo.ams_memberWebsites
						where orgID = @orgID
						and websiteID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">
						and websiteTypeID = <cfqueryparam value="#local.thisWebsiteTypeID#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values(<cfqueryparam value="#local.qryOrgWebsites.websiteType#" cfsqltype="CF_SQL_VARCHAR">, isnull(@website,''));
					</cfif>
				</cfloop>

				<cfloop query="local.qryOrgAddressTags">
					<cfset local.thisFormFieldName = "rdo_memberAddressTagID_#local.qryOrgAddressTags.addressTagTypeID#_">

					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						set @addressType = null;

						select @addressType = addressType
						from dbo.ams_memberAddressTypes 
						where orgID = @orgID 
						and addressTypeID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values(<cfqueryparam value="Designated #local.qryOrgAddressTags.addressTagType#_addressType" cfsqltype="CF_SQL_VARCHAR">, isnull(@addressType,''));
					</cfif>
				</cfloop>

				<cfloop query="local.qryOrgAddresses">
					<cfset local.thisAddressTypeID = local.qryOrgAddresses.addressTypeID>
					<cfset local.thisFormFieldName = "rdo_memberAddressID_#local.thisAddressTypeID#_">
					
					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						SELECT @attn=NULL, @address1=NULL, @address2=NULL, @address3=NULL, @city=NULL, @postalCode=NULL,
							@county=NULL, @country=NULL, @stateCode=NULL;

						SELECT
							@attn = case when mat.hasAttn = 1 then ma.attn else '' end,
							@address1 = ma.address1,
							@address2 = case when mat.hasAddress2 = 1 then ma.address2 else '' end,
							@address3 = case when mat.hasAddress3 = 1 then ma.address3 else '' end,
							@city = ma.city,
							@postalCode = ma.postalCode,
							@county = case when mat.hasCounty = 1 then ma.county else '' end,
							@country = ma.countryName,
							@stateCode = ma.stateCode
						FROM dbo.ams_memberAddresses AS ma
						INNER JOIN dbo.ams_memberAddressTypes AS mat ON mat.addressTypeID = ma.addressTypeID
							AND mat.orgID = @orgID
							AND ma.orgID = @orgID
							AND mat.addressTypeID = <cfqueryparam value="#local.thisAddressTypeID#" cfsqltype="CF_SQL_INTEGER">
						WHERE ma.addressID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_attn" cfsqltype="CF_SQL_VARCHAR">, isnull(@attn,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_Address1" cfsqltype="CF_SQL_VARCHAR">, isnull(@address1,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_Address2" cfsqltype="CF_SQL_VARCHAR">, isnull(@address2,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_Address3" cfsqltype="CF_SQL_VARCHAR">, isnull(@address3,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_county" cfsqltype="CF_SQL_VARCHAR">, isnull(@county,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_city" cfsqltype="CF_SQL_VARCHAR">, isnull(@city,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_postalCode" cfsqltype="CF_SQL_VARCHAR">, isnull(@postalCode,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_country" cfsqltype="CF_SQL_VARCHAR">, isnull(@country,'')),
						(<cfqueryparam value="#local.qryOrgAddresses.addressType#_stateprov" cfsqltype="CF_SQL_VARCHAR">, isnull(@stateCode,''));
					</cfif>

					<cfloop query="local.qryOrgPhones">
						<cfset local.thisFormFieldName = "rdo_memberAddressID_#local.thisAddressTypeID#_#local.qryOrgPhones.phoneTypeID#_">

						<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
							<cfset local.addressPhoneStruct = {"#local.qryOrgAddresses.addressType#_#local.qryOrgPhones.phoneType#":arguments.event.getValue(local.thisFormFieldName,'')}>
							<cfset StructAppend(local.payLoadData.memberdata,local.addressPhoneStruct)>
						</cfif>
					</cfloop>
				</cfloop>

				<cfloop query="local.qryOrgProfessionalLicenses">
					<cfset local.thisFormFieldName = "rdo_memberPLTypeID_#local.qryOrgProfessionalLicenses.PLTypeID#_">

					<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
						select @licenseNumber=null, @licenseActiveDate=null, @licenseStatusName=null;

						select @licenseNumber=pl.LicenseNumber, @licenseActiveDate=convert(varchar(12), pl.ActiveDate, 101), @licenseStatusName=pls.statusName
						from dbo.ams_memberProfessionalLicenses pl
						inner join dbo.ams_memberProfessionalLicenseTypes plt on plt.PLTypeID = pl.PLTypeID
							and plt.orgID = @orgID
						left outer join dbo.ams_memberProfessionalLicenseStatuses pls on pls.PLStatusID = pl.PLStatusID
							and pls.orgID = @orgID
						where pl.PLID = <cfqueryparam value="#int(val(arguments.event.getValue(local.thisFormFieldName,0)))#" cfsqltype="CF_SQL_INTEGER">;

						insert into ##tmpMemberData(name, value)
						values
						(<cfqueryparam value="#local.qryOrgProfessionalLicenses.PLName#_licenseNumber" cfsqltype="CF_SQL_VARCHAR">, isnull(@licenseNumber,'')),
						(<cfqueryparam value="#local.qryOrgProfessionalLicenses.PLName#_activeDate" cfsqltype="CF_SQL_VARCHAR">, isnull(@licenseActiveDate,'')),
						(<cfqueryparam value="#local.qryOrgProfessionalLicenses.PLName#_status" cfsqltype="CF_SQL_VARCHAR">, isnull(@licenseStatusName,''));
					</cfif>
				</cfloop>

				<cfloop array="#local.xmlAdditionalData.data.XMlChildren#" index="local.column">
					<cfset local.currColumnID = local.column.xmlattributes.columnID>

					<cfif local.column.xmlAttributes.isReadOnly EQ 0>
						<cfswitch expression="#local.column.xmlAttributes.displayTypeCode#">
							<cfcase value="DATE,TEXTBOX">
								<cfset local.thisFormFieldName = "rdo_ad_#local.currColumnID#_txt">
							</cfcase>
							<cfcase value="RADIO,CHECKBOX,SELECT">
								<cfset local.thisFormFieldName = "rdo_ad_#local.currColumnID#_opt">
							</cfcase>
							<cfcase value="DOCUMENT">
								<cfset local.thisFormFieldName = "rdo_ad_#local.currColumnID#_file">
							</cfcase>
							<cfcase value="TEXTAREA">
								<cfset local.thisFormFieldName = "rdo_ad_#local.currColumnID#_txtarea">
							</cfcase>
							<cfcase value="HTMLCONTENT">
								<cfset local.thisFormFieldName = "rdo_ad_#local.currColumnID#_content">
							</cfcase>
						</cfswitch>

						<cfif not listFindNoCase(skipFormFieldsList, local.thisFormFieldName)>
							<cfswitch expression="#local.column.xmlAttributes.displayTypeCode#">
								<cfcase value="DATE,TEXTBOX">
									<cfset local.valSelected = arguments.event.getValue(local.thisFormFieldName)>
									<cfif local.valSelected EQ '__NOVALUE__'>
										<cfset local.valSelected = ''>
									</cfif>
									<cfset StructAppend(local.payLoadData.memberdata, {"#local.column.xmlAttributes.columnName#":local.valSelected})>
								</cfcase>
								<cfcase value="RADIO,CHECKBOX,SELECT">
									<cfset local.valSelected = arguments.event.getValue(local.thisFormFieldName)>
									<cfif local.valSelected EQ '__NOVALUE__'>
										<cfset local.valSelected = ''>
									</cfif>

									<!--- if multiple, payload data needs to be a list of values, pipe separated --->
									set @columnValue = null;

									select @columnValue = 
										<cfif local.column.xmlAttributes.dataTypeCode eq "Integer">
											STRING_AGG(columnvalue,'|')
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Decimal2">
											STRING_AGG(columnvalue,'|')
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Date"> <!--- no multi support --->
											columnvalue
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Bit"> <!--- no multi support --->
											columnvalue
										<cfelse>
											STRING_AGG(columnvalue,'|')
										</cfif>
										FROM (
											select 
										<cfif local.column.xmlAttributes.dataTypeCode eq "Integer">
											distinct mdcv2.columnValueInteger 
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Decimal2">
											distinct mdcv2.columnValueDecimal2
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Date"> <!--- no multi support --->
											convert(varchar(10),mdcv2.columnValueDate,101)
										<cfelseif local.column.xmlAttributes.dataTypeCode eq "Bit"> <!--- no multi support --->
											case when mdcv2.columnValueBit = 1 then '1' when mdcv2.columnValueBit = 0 then '0' else '' end
										<cfelse>
											distinct mdcv2.columnValueString
										</cfif> as columnvalue
									from dbo.ams_memberDataColumns mdc
									left outer join dbo.ams_memberDataColumnValues mdcv2 on mdcv2.columnID = mdc.columnID and mdcv2.valueID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#local.valSelected#" list="true">)
									where mdc.columnID = <cfqueryparam value="#local.currColumnID#" cfsqltype="CF_SQL_INTEGER">
									and mdc.orgID = @orgID) as tmp;

									insert into ##tmpMemberData(name, value)
									values (<cfqueryparam value="#local.column.xmlAttributes.columnName#" cfsqltype="CF_SQL_VARCHAR">, isnull(@columnValue,''));
								</cfcase>
								<cfcase value="DOCUMENT">
									<cfset local.valSelected = int(val(arguments.event.getValue(local.thisFormFieldName,0)))>
									<cfif local.valSelected EQ '__NOVALUE__'>
										<cfset local.valSelected = 0>
									</cfif>

									set @columnValue = null;

									select @columnValue = dv.filename
									from dbo.cms_documents as d
									inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
									inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
									inner join dbo.sites as s on s.siteID = d.siteID
									where s.orgID = @orgID
									and d.siteResourceID = <cfqueryparam value="#local.valSelected#" cfsqltype="CF_SQL_INTEGER">;

									insert into ##tmpMemberData(name, value)
									values (<cfqueryparam value="#local.column.xmlAttributes.columnName#" cfsqltype="CF_SQL_VARCHAR">, isnull(@columnValue,''));
								</cfcase>
								<cfcase value="TEXTAREA,HTMLCONTENT">
									<cfset local.valSelected = int(val(arguments.event.getValue(local.thisFormFieldName,0)))>
									<cfif local.valSelected EQ '__NOVALUE__'>
										<cfset local.valSelected = 0>
									</cfif>
									
									<cfif (local.column.xmlAttributes.displayTypeCode eq "TEXTAREA" and local.column.xmlAttributes.dataTypeCode eq "CONTENTOBJ")
										or local.column.xmlAttributes.displayTypeCode eq "HTMLCONTENT">
										set @columnValue = null;

										select @columnValue = cv.rawContent
										from dbo.cms_content as c
										inner join dbo.sites as s on s.siteID = c.siteID
											AND s.orgID = @orgID
										inner join dbo.cms_contentLanguages as cl ON cl.siteID = s.siteID
											and cl.contentID = c.contentID 
											and cl.languageID = 1
										inner join dbo.cms_contentVersions as cv on cv.siteID = s.siteID
											and cv.contentID = cl.contentID
											and cv.contentLanguageID = cl.contentLanguageID 
											and cv.isActive = 1
										where c.siteID = s.siteID
										AND c.siteResourceID = <cfqueryparam value="#local.valSelected#" cfsqltype="CF_SQL_INTEGER">;

										insert into ##tmpMemberData(name, value)
										values (<cfqueryparam value="#local.column.xmlAttributes.columnName#" cfsqltype="CF_SQL_VARCHAR">, isnull(@columnValue,''));
									</cfif>
								</cfcase>
							</cfswitch>
						</cfif>
					</cfif>
				</cfloop>

				SELECT name, value
				FROM ##tmpMemberData;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfloop query="local.qryPayloadData">
				<cfset StructAppend(local.payLoadData.memberdata, {"#local.qryPayloadData.name#":local.qryPayloadData.value})>
			</cfloop>

			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), method="POST", endpoint="member/#local.chosenMemberNumber#/merge", payload=serializeJSON(local.payloadData))>

			<cfset local.newMemberID = application.objMember.getMemberIDByMemberNumber(memberNumber=local.strInsertResponse.data.membernumber, orgID=local.orgID)>
			<cfset local.returnStruct['success'] = not local.strInsertResponse.error>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : "")>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.returnStruct.success>
				<script language="javascript">
					<cfif len(local.returnFunction)>
						try {
							if (top.#local.returnFunction#) {
								var obj = { memberid:#local.newMemberID# };
								top.#local.returnFunction#(obj);
							} else {
								top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#local.newMemberID#';
								top.MCModalUtils.hideModal();
							}
						} catch(e) {
							top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#local.newMemberID#';
							top.MCModalUtils.hideModal();
						}
					<cfelse>
						top.location.href = '#buildCurrentLink(arguments.event,"edit")#&memberid=#local.newMemberID#';
						top.MCModalUtils.hideModal();
					</cfif>
				</script>
			<cfelse>
				<div class="alert alert-danger">
					<cfif len(local.returnStruct['errmsg']) or (isDefined("local.strInsertResponse") and arrayLen(local.strInsertResponse.messages))>
						<cfif len(local.returnStruct['errmsg'])>
							<div class="mb-2">#local.returnStruct['errmsg']#</div>
						</cfif>
						<cfif isDefined("local.strInsertResponse") and arrayLen(local.strInsertResponse.messages)>
							<div>#ArrayToList(local.strInsertResponse.messages,"<br/>")#</div>
						</cfif>
					<cfelse>
						Some error occured while merging members.
					</cfif>
					<button class="btn btn-sm btn-secondary mt-3" type="button" onClick="top.MCModalUtils.hideModal();">Close</button>
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="merge_showRowDefault" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="colLabel" type="string" required="yes">
		<cfargument name="domLabel" type="string" required="yes">
		<cfargument name="rdoValue" type="string" required="yes">
		<cfargument name="rdoLabel" type="string" required="yes">
	
		<cfset var local = structNew()>
		
		<!--- if both values are the same (or no values for either), dont show the row --->
		<cfset local.listOfValues = ArrayToList(arguments.qryAllMembers[arguments.rdoValue],chr(7))>
		<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
		<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
			<cfsavecontent variable="local.rowContent">
				<cfoutput>
				<input type="hidden" id="rdo_#arguments.domLabel#_hidden_" name="rdo_#arguments.domLabel#_" value="#replace(local.firstValue,chr(34),'&quot;','ALL')#">
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.rowContent">
				<cfoutput>
				<tr>
					<td class="font-weight-bold">#arguments.colLabel#</td>
					<cfloop query="arguments.qryAllMembers">
						<td id="cell_#arguments.domLabel#_#arguments.qryAllMembers.CurrentRow#_" onclick="selCell('#arguments.domLabel#',#arguments.qryAllMembers.currentrow#);" class="<cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>">
							<input type="radio" id="rdo_#arguments.domLabel#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_#arguments.domLabel#_" value="#encodeForHTMLAttribute(evaluate(arguments.rdoValue))#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>> #encodeForHTML(evaluate(arguments.rdoLabel))#
						</td>
					</cfloop>
				</tr>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.rowContent>
	</cffunction>

	<cffunction name="merge_showRowMNP" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryMNP" type="query" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.domLabel = "membernumberforlogindata">

		<cfsavecontent variable="local.rowContent">
			<cfoutput>
			<tr>
				<td class="font-weight-bold">Select the MemberNumber for Login Information</td>
				<cfset local.firstOptSelectionSet = false>
				<cfloop query="arguments.qryAllMembers">
					<cfquery name="local.qryMNPInfo" dbtype="query">
						select memberID, mnpCount, mnpInfo
						from [arguments].qryMNP
						where memberID = #arguments.qryAllMembers.memberID#
					</cfquery>
					<cfif local.qryMNPInfo.recordCount eq 1>
						<td id="cell_#local.domLabel#_#arguments.qryAllMembers.CurrentRow#_" onclick="selCell('#local.domLabel#',#arguments.qryAllMembers.currentrow#);" class="<cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" style="vertical-align:top;">
							<input type="radio" id="rdo_#local.domLabel#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_#local.domLabel#_" value="#encodeForHTMLAttribute(arguments.qryAllMembers.membernumber)#" <cfif not local.firstOptSelectionSet>checked</cfif>>
							#encodeForHTML(arguments.qryAllMembers.membernumber)#
							<cfset local.currentMNPInfoArray = listToArray(local.qryMNPInfo.mnpInfo,"^---^",true,true)>
							<cfloop array="#local.currentMNPInfoArray#" index="local.thisSiteMNPInfo">
								<cfset local.thisSiteMNPInfoArray = listToArray(local.thisSiteMNPInfo,"^***^",true,true)>
								<div class="mt-1 mb-2">
									<cfif local.qryMNPInfo.mnpCount gt 1 OR local.thisSiteMNPInfoArray[1] neq session.mcStruct.sitecode>
										Site: #local.thisSiteMNPInfoArray[1]#<br/>
									</cfif>
									Username: #local.thisSiteMNPInfoArray[2]#<br/>
									Last Login: #local.thisSiteMNPInfoArray[3]#<br/>
									<cfif ArrayIsDefined(local.thisSiteMNPInfoArray,4) AND len(local.thisSiteMNPInfoArray[4]) AND local.thisSiteMNPInfoArray[4] neq "N/A">
										<cfset local.thisMFAPhone = local.thisSiteMNPInfoArray[4]>
										MFA PHONE: #left(local.thisMFAPhone,5)##repeatString("*",len(local.thisMFAPhone) - 7)##right(local.thisMFAPhone,2)#
									</cfif>
								</div>
							</cfloop>
						</td>
						<cfset local.firstOptSelectionSet = true>
					<cfelse>
						<td id="cell_#local.domLabel#_#arguments.qryAllMembers.CurrentRow#_" class="<cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" style="vertical-align:top;">
							N/A
						</td>
					</cfif>
				</cfloop>
			</tr>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.rowContent>
	</cffunction>
	
	<cffunction name="merge_showRowMemberPhoto" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="photoPath" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.strPhotos = structNew()>
		<cfset local.listOfValues = "">
		
		<cfloop query="arguments.qryAllMembers">
			<cfset local.strPhotos[arguments.qryAllMembers.membernumber] = fileexists(arguments.photoPath & LCASE(arguments.qryAllMembers.membernumber) & ".jpg")>
			<cfif local.strPhotos[arguments.qryAllMembers.membernumber]>
				<cfset local.listOfValues = listAppend(local.listOfValues,arguments.qryAllMembers.membernumber,chr(7))>
			</cfif>
		</cfloop>

		<!--- if neither have photos or only one has photo, dont show the row --->
		<cfif listLen(local.listOfValues,chr(7)) lte 1>
			<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
			<cfsavecontent variable="local.rowContent">
				<cfoutput>
				<input type="hidden" id="rdo_memberPhoto_hidden_" name="rdo_memberPhoto_" value="#local.firstValue#">
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.rowContent">
				<cfoutput>
				<tr>
					<td class="align-top font-weight-bold">Member Photo</td>
					<cfloop query="arguments.qryAllMembers">
						<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberPhoto_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberPhoto',#arguments.qryAllMembers.currentrow#);">
							<input type="radio" id="rdo_memberPhoto_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberPhoto_" value="#arguments.qryAllMembers.membernumber#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;
							<cfif local.strPhotos[arguments.qryAllMembers.membernumber]>
								<img src="/memberphotosth/#LCASE(arguments.qryAllMembers.membernumber)#.jpg?cb=#getTickCount()#">
							<cfelse>
								<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
							</cfif>
						</td>
					</cfloop>
				</tr>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.rowContent>
	</cffunction>

	<cffunction name="merge_showRowEmails" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgEmails" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgEmails">
				<cfset local.thisEmailTypeID = arguments.qryOrgEmails.emailTypeID>
				<cfset local.listOfValues = "">
				<cfset local.OneEmailIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="font-weight-bold">Email: #arguments.qryOrgEmails.emailType#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberEmailType" dbtype="query">
								select emailID, email
								from [local].tmpStrMember.qryMemberEmails
								where emailTypeID = #local.thisEmailTypeID#
							</cfquery>
							<td id="cell_memberEmailID_#local.thisEmailTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberEmailID_#local.thisEmailTypeID#',#arguments.qryAllMembers.CurrentRow#);" class="<cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>">
								<input type="radio" id="rdo_memberEmailID_#local.thisEmailTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberEmailID_#local.thisEmailTypeID#_" value="#local.tmpMemberEmailType.emailID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>
								#local.tmpMemberEmailType.email#
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,local.tmpMemberEmailType.email,chr(7))>
							<cfif len(local.tmpMemberEmailType.email)>
								<cfset local.OneEmailIDToUse = local.tmpMemberEmailType.emailID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberEmailID_hidden_" name="rdo_memberEmailID_#local.thisEmailTypeID#_" value="#local.OneEmailIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
		
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		
		<cfreturn local.allrowContent>
	</cffunction>
	
	<cffunction name="merge_showRowWebsites" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgWebsites" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgWebsites">
				<cfset local.thisWebsiteTypeID = arguments.qryOrgWebsites.websiteTypeID>
				<cfset local.listOfValues = "">
				<cfset local.OneWebsiteIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="font-weight-bold">Website: #arguments.qryOrgWebsites.websiteType#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberWebsiteType" dbtype="query">
								select websiteID, website
								from [local].tmpStrMember.qryMemberWebsites
								where websiteTypeID = #local.thisWebsiteTypeID#
							</cfquery>
							<td id="cell_memberWebsiteID_#local.thisWebsiteTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberWebsiteID_#local.thisWebsiteTypeID#',#arguments.qryAllMembers.CurrentRow#);" class="<cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>">
								<input type="radio" id="rdo_memberWebsiteID_#local.thisWebsiteTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberWebsiteID_#local.thisWebsiteTypeID#_" value="#local.tmpMemberWebsiteType.websiteID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>
								#encodeForHTML(local.tmpMemberWebsiteType.website)#
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,encodeForHTML(local.tmpMemberWebsiteType.website),chr(7))>
							<cfif len(local.tmpMemberWebsiteType.website)>
								<cfset local.OneWebsiteIDToUse = local.tmpMemberWebsiteType.websiteID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberWebsiteID_hidden_" name="rdo_memberWebsiteID_#local.thisWebsiteTypeID#_" value="#local.OneWebsiteIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
		
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		
		<cfreturn local.allrowContent>
	</cffunction>
	
	<cffunction name="merge_showRowLicenses" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgProfessionalLicenses" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgProfessionalLicenses">
				<cfset local.thisPLTypeID = arguments.qryOrgProfessionalLicenses.PLTypeID>
				<cfset local.listOfValues = "">
				<cfset local.OnePLIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="align-top font-weight-bold">#arguments.qryOrgProfessionalLicenses.PLName#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberPLType" dbtype="query">
								select PLID, PLTypeID, LicenseNumber, statusName, activeDate
								from [local].tmpStrMember.qryProLicenses
								where PLTypeID = #local.thisPLTypeID#
							</cfquery>
							<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberPLTypeID_#local.thisPLTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberPLTypeID_#local.thisPLTypeID#',#arguments.qryAllMembers.CurrentRow#);">
								<table class="table table-sm table-borderless">
								<tr>
									<td class="align-top w-5"><input type="radio" id="rdo_memberPLTypeID_#local.thisPLTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberPLTypeID_#local.thisPLTypeID#_" value="#local.tmpMemberPLType.PLID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>></td>
									<td class="align-top">
										License: #encodeForHTML(local.tmpMemberPLType.LicenseNumber)#<br/>
										Status: #local.tmpMemberPLType.statusName#<br/>
										Date: #local.tmpMemberPLType.ActiveDate#
									</td>
								</tr>
								</table>
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,"#local.tmpMemberPLType.LicenseNumber#|#local.tmpMemberPLType.statusName#|#local.tmpMemberPLType.activeDate#",chr(7))>
							<cfif len(local.tmpMemberPLType.PLTypeID)>
								<cfset local.OnePLIDToUse = local.tmpMemberPLType.PLID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberPLTypeID_#local.thisPLTypeID#_hidden_" name="rdo_memberPLTypeID_#local.thisPLTypeID#_" value="#local.OnePLIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
		
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		
		<cfreturn local.allrowContent>
	</cffunction>

	<cffunction name="merge_showRowAddresses" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgAddresses" type="query" required="yes">
		<cfargument name="qryOrgPhones" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgAddresses">
				<cfset local.AddressType = arguments.qryOrgAddresses.AddressType>
				<cfset local.AddressTypeID = arguments.qryOrgAddresses.AddressTypeID>
				<cfset local.hasAttn = arguments.qryOrgAddresses.hasAttn>
				<cfset local.hasAddress2 = arguments.qryOrgAddresses.hasAddress2>
				<cfset local.hasAddress3 = arguments.qryOrgAddresses.hasAddress3>
				<cfset local.hasCounty = arguments.qryOrgAddresses.hasCounty>
				<cfset local.listOfValues = "">
				<cfset local.OneAddressIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="align-top font-weight-bold">#local.addressType#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberAddressType" dbtype="query">
								select addressID, attn, address1, address2, address3, city, stateCode, postalcode, county, country
								from [local].tmpStrMember.qryMemberAddresses
								where addressTypeID = #local.AddressTypeID#
							</cfquery>
							<cfsavecontent variable="local.thisaddrFull">
								<cfoutput>
								<cfif local.hasAttn and len(local.tmpMemberAddressType.attn)>#encodeForHTML(local.tmpMemberAddressType.attn)#<br/></cfif>
								<cfif len(local.tmpMemberAddressType.address1)>#encodeForHTML(local.tmpMemberAddressType.address1)#<br/></cfif>
								<cfif local.hasAddress2 and len(local.tmpMemberAddressType.address2)>#encodeForHTML(local.tmpMemberAddressType.address2)#<br/></cfif>
								<cfif local.hasAddress3 and len(local.tmpMemberAddressType.address3)>#encodeForHTML(local.tmpMemberAddressType.address3)#<br/></cfif>
								<cfif len(local.tmpMemberAddressType.city)>#encodeForHTML(local.tmpMemberAddressType.city)#</cfif> 
								<cfif len(local.tmpMemberAddressType.stateCode)>#encodeForHTML(local.tmpMemberAddressType.stateCode)#</cfif> 
								<cfif len(local.tmpMemberAddressType.postalcode)>#encodeForHTML(local.tmpMemberAddressType.postalcode)#</cfif>
								</cfoutput>
							</cfsavecontent>
							<cfif local.hasCounty and len(local.tmpMemberAddressType.county)>
								<cfsavecontent variable="local.thisaddrFull">
									<cfoutput>#local.thisaddrFull#<cfif len(local.thisaddrFull)><br/></cfif>#encodeForHTML(local.tmpMemberAddressType.county)#</cfoutput>
								</cfsavecontent>
							</cfif>
							<cfif len(local.tmpMemberAddressType.country)>
								<cfsavecontent variable="local.thisaddrFull">
									<cfoutput>#local.thisaddrFull#<cfif len(local.thisaddrFull)><br/></cfif>#encodeForHTML(local.tmpMemberAddressType.country)#</cfoutput>
								</cfsavecontent>
							</cfif>
							<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberAddressID_#local.addressTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberAddressID_#local.addressTypeID#',#arguments.qryAllMembers.CurrentRow#);">
								<table class="table table-sm table-borderless">
								<tr>
									<td class="align-top w-5"><input type="radio" id="rdo_memberAddressID_#local.addressTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberAddressID_#local.addressTypeID#_" value="#local.tmpMemberAddressType.addressID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>></td>
									<td class="align-top">#local.thisaddrFull#</td>
								</tr>
								</table>
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,trim(local.thisaddrFull),chr(7))>
							<cfif len(trim(local.thisaddrFull))>
								<cfset local.OneAddressIDToUse = local.tmpMemberAddressType.addressID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberAddressID_#local.addressTypeID#_hidden_" name="rdo_memberAddressID_#local.addressTypeID#_" value="#local.OneAddressIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
				<cfoutput>#local.rowContent#</cfoutput>
				
				<cfloop query="arguments.qryOrgPhones">
					<cfset local.phoneTypeID = arguments.qryOrgPhones.phoneTypeID>
					<cfset local.listOfValuesPhone = "">
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<tr>
							<td class="align-top font-weight-bold">#local.addressType# #arguments.qryOrgPhones.phoneType#</td>
							<cfloop query="arguments.qryAllMembers">
								<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
								<cfquery name="local.tmpMemberPhoneType" dbtype="query">
									select phone
									from [local].tmpStrMember.qryMemberAddresses
									where addressTypeID = #local.AddressTypeID#
									and phoneTypeID = #local.phoneTypeID#
								</cfquery>
								<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberAddressID_#local.addressTypeID#_#local.phoneTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberAddressID_#local.addressTypeID#_#local.phoneTypeID#',#arguments.qryAllMembers.CurrentRow#);">
									<input type="radio" id="rdo_memberAddressID_#local.addressTypeID#_#local.phoneTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberAddressID_#local.addressTypeID#_#local.phoneTypeID#_" value="#encodeForHTMLAttribute(local.tmpMemberPhoneType.phone)#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>> #encodeForHTML(local.tmpMemberPhoneType.phone)#	
								</td>
								<cfset local.listOfValuesPhone = listAppend(local.listOfValuesPhone,encodeForHTML(local.tmpMemberPhoneType.phone),chr(7))>
							</cfloop>
						</tr>
						</cfoutput>
					</cfsavecontent>
					<cfset local.firstValuePhone = ListFirst(local.listOfValuesPhone,chr(7))>
					<cfif ListValueCountNoCase(local.listOfValuesPhone,local.firstValuePhone,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValuesPhone,chr(7)) is 0>
						<cfsavecontent variable="local.rowContent">
							<cfoutput>
							<input type="hidden" id="rdo_memberAddressID_#local.addressTypeID#_#local.phoneTypeID#_hidden_" name="rdo_memberAddressID_#local.addressTypeID#_#local.phoneTypeID#_" value="#replace(local.firstValuePhone,chr(34),'&quot;','ALL')#">
							</cfoutput>
						</cfsavecontent>
					</cfif>
					<cfoutput>#local.rowContent#</cfoutput>
				</cfloop>
			</cfloop>
		</cfsavecontent>
		<cfreturn local.allrowContent>
	</cffunction>

	<cffunction name="merge_showRowAddressTags" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgAddressTags" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgAddressTags">
				<cfset local.AddressTagType = arguments.qryOrgAddressTags.AddressTagType>
				<cfset local.AddressTagTypeID = arguments.qryOrgAddressTags.AddressTagTypeID>
				<cfset local.listOfValues = "">
				<cfset local.OneAddressIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="align-top font-weight-bold"><i class="fa-solid fa-tag"></i> Designated #local.AddressTagType#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberAddressType" dbtype="query">
								select addressTypeID, addressType
								from [local].tmpStrMember.qryMemberAddressTags
								where addressTagTypeID = #local.AddressTagTypeID#
							</cfquery>
							<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberAddressTagID_#local.addressTagTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberAddressTagID_#local.addressTagTypeID#',#arguments.qryAllMembers.CurrentRow#);">
								<table class="table table-sm table-borderless">
								<tr>
									<td class="align-top w-5"><input type="radio" id="rdo_memberAddressTagID_#local.addressTagTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberAddressTagID_#local.addressTagTypeID#_" value="#local.tmpMemberAddressType.addressTypeID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>></td>
									<td class="align-top">#local.tmpMemberAddressType.addressType#</td>
								</tr>
								</table>
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,trim(local.tmpMemberAddressType.addressType),chr(7))>
							<cfif len(trim(local.tmpMemberAddressType.addressType))>
								<cfset local.OneAddressIDToUse = local.tmpMemberAddressType.addressTypeID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberAddressTagID_#local.addressTagTypeID#_hidden_" name="rdo_memberAddressTagID_#local.addressTagTypeID#_" value="#local.OneAddressIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		<cfreturn local.allrowContent>
	</cffunction>

	<cffunction name="merge_showRowEmailTags" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="qryOrgEmailTags" type="query" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.allrowContent">
			<cfloop query="arguments.qryOrgEmailTags">
				<cfset local.emailTagType = arguments.qryOrgEmailTags.emailTagType>
				<cfset local.emailTagTypeID = arguments.qryOrgEmailTags.emailTagTypeID>
				<cfset local.listOfValues = "">
				<cfset local.oneEmailIDToUse = "">
				<cfsavecontent variable="local.rowContent">
					<cfoutput>
					<tr>
						<td class="align-top font-weight-bold"><i class="fa-solid fa-tag"></i> Designated #local.emailTagType#</td>
						<cfloop query="arguments.qryAllMembers">
							<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]> 
							<cfquery name="local.tmpMemberEmailType" dbtype="query">
								select emailTypeID, emailType
								from [local].tmpStrMember.qryMemberEmailTags
								where emailTagTypeID = #local.emailTagTypeID#
							</cfquery>
							<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_memberEmailTagID_#local.emailTagTypeID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('memberEmailTagID_#local.emailTagTypeID#',#arguments.qryAllMembers.CurrentRow#);">
								<table class="table table-sm table-borderless">
								<tr>
									<td class="align-top w-5"><input type="radio" id="rdo_memberEmailTagID_#local.emailTagTypeID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_memberEmailTagID_#local.emailTagTypeID#_" value="#local.tmpMemberEmailType.emailTypeID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>></td>
									<td class="align-top">#local.tmpMemberEmailType.emailType#</td>
								</tr>
								</table>
							</td>
							<cfset local.listOfValues = listAppend(local.listOfValues,trim(local.tmpMemberEmailType.emailType),chr(7))>
							<cfif len(trim(local.tmpMemberEmailType.emailType))>
								<cfset local.oneEmailIDToUse = local.tmpMemberEmailType.emailTypeID>
							</cfif>
						</cfloop>
					</tr>
					</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="rdo_memberEmailTagID_#local.emailTagTypeID#_hidden_" name="rdo_memberEmailTagID_#local.emailTagTypeID#_" value="#local.oneEmailIDToUse#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		<cfreturn local.allrowContent>
	</cffunction>
	
	<cffunction name="merge_showRowCustomFields" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="xmlAdditionalData" type="xml" required="yes">
		<cfargument name="strAllMembers" type="struct" required="yes">
	
		<cfset var local = structNew()>
	
		<cfsavecontent variable="local.allrowContent">
			<cfloop array="#arguments.xmlAdditionalData.data.XMlChildren#" index="local.column">
				<cfset local.listOfValues = "">
				<cfset local.hiddenControlName = "">
				<cfsavecontent variable="local.rowContent">
				<cfoutput>
				<tr>
					<td class="align-top font-weight-bold">#local.column.xmlAttributes.columnName#</td>
					<cfloop query="arguments.qryAllMembers">
						<cfset local.tmpStrMember = arguments.strAllMembers[arguments.qryAllMembers.memberID]>
						<cfset local.xmlAdditionalData_Member = xmlParse(local.tmpStrMember.xmlAdditionalData)>
						<cfset local.memberColDataActualValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@actualColumnValue)")>
						<cfset local.memberColDataValue = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@columnValue)")>
						<cfset local.memberColDataValueID = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID)")>
						<td class="align-top <cfif arguments.qryAllMembers.currentrow is 1>bg-lightgreen<cfelse>text-grey</cfif>" id="cell_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" onClick="selCell('ad_#local.column.xmlAttributes.columnID#',#arguments.qryAllMembers.currentrow#);">
							<cfswitch expression="#local.column.xmlAttributes.displayTypeCode#">
								<cfcase value="DATE">
									<cfif len(local.memberColDataValue)>
										<cfset local.memberColDataValue = dateFormat(local.memberColDataValue,"m/d/yyyy")>
									</cfif>
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataValue,chr(7))>
									<cfif len(local.memberColDataValue) is 0>
										<cfset local.memberColDataValue = "__NOVALUE__">
										<cfset local.memberColDataValueShow = "">
									<cfelse>
										<cfset local.memberColDataValueShow = local.memberColDataValue>
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_txt" value="#local.memberColDataValue#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#local.memberColDataValueShow#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_txt">
								</cfcase>
								<cfcase value="TEXTBOX">
									<cfset local.listOfValues = listAppend(local.listOfValues,encodeForHTML(local.memberColDataValue),chr(7))>
									<cfif len(local.memberColDataValue) is 0>
										<cfset local.memberColDataValue = "__NOVALUE__">
										<cfset local.memberColDataValueShow = "">
									<cfelse>
										<cfset local.memberColDataValueShow = encodeForHTML(local.memberColDataValue)>
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_txt" value="#encodeForHTMLAttribute(local.memberColDataValue)#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#local.memberColDataValueShow#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_txt">
								</cfcase>
								<cfcase value="RADIO">
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataValueID,chr(7))>
									<cfif int(val(local.memberColDataValueID)) is 0>
										<cfset local.memberColDataValueID = "__NOVALUE__">
									</cfif>
									<cfif local.column.xmlAttributes.dataTypeCode eq "BIT" and local.memberColDataValue eq "1">
										<cfset local.memberColDataValue = "Yes">
									<cfelseif local.column.xmlAttributes.dataTypeCode eq "BIT" and local.memberColDataValue eq "0">
										<cfset local.memberColDataValue = "No">
									<cfelseif local.column.xmlAttributes.dataTypeCode eq "DATE">
										<cfset local.memberColDataValue = dateformat(local.memberColDataValue,'m/d/yyyy')>
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_opt" value="#local.memberColDataValueID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#local.memberColDataValue#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_opt">
								</cfcase>
								<cfcase value="CHECKBOX">
									<cfset local.memberColDataValueID = ReReplace(replaceNoCase(ArrayToList(XMLSearch(local.xmlAdditionalData_Member,"//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID")),'<?xml version="1.0" encoding="UTF-8"?>','','ALL'),"[^0-9,]","","ALL")>
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataValueID,chr(7))>
									<cfif len(local.memberColDataValueID) is 0>
										<cfset local.memberColDataValueID = "__NOVALUE__">
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_opt" value="#local.memberColDataValueID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#replace(local.memberColDataActualValue,'|','<br/>','ALL')#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_opt">
								</cfcase>
								<cfcase value="SELECT">
									<cfif local.column.xmlattributes.allowMultiple is 1>
										<cfset local.memberColDataValueID = ReReplace(replaceNoCase(ArrayToList(XMLSearch(local.xmlAdditionalData_Member,"//column[@columnID=#local.column.xmlAttributes.columnID#]/@valueID")),'<?xml version="1.0" encoding="UTF-8"?>','','ALL'),"[^0-9,]","","ALL")>
										<cfset local.memberColDataActualValue = encodeForHTML(replaceNoCase(ArrayToList(XMLSearch(local.xmlAdditionalData_Member,"//column[@columnID=#local.column.xmlAttributes.columnID#]/@actualColumnValue")),'<?xml version="1.0" encoding="UTF-8"?>','','ALL'))>
									</cfif>
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataValueID,chr(7))>
									<cfif len(local.memberColDataValueID) is 0>
										<cfset local.memberColDataValueID = "__NOVALUE__">
									</cfif>
									<cfif local.column.xmlAttributes.dataTypeCode eq "BIT" and local.memberColDataActualValue eq "1">
										<cfset local.memberColDataActualValue = "Yes">
									<cfelseif local.column.xmlAttributes.dataTypeCode eq "BIT" and local.memberColDataActualValue eq "0">
										<cfset local.memberColDataActualValue = "No">
									<cfelseif local.column.xmlAttributes.dataTypeCode eq "DATE">
										<cfset local.memberColDataActualValue = dateformat(replaceNoCase(local.memberColDataActualValue,'T',' '),"m/d/yyyy")>
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_opt" value="#local.memberColDataValueID#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#replace(local.memberColDataActualValue,'|','<br/>','ALL')#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_opt">
								</cfcase>
								<cfcase value="DOCUMENT">
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataActualValue,chr(7))>
									<cfif int(val(local.memberColDataActualValue)) is 0>
										<cfset local.memberColDataActualValue = "__NOVALUE__">
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_file" value="#local.memberColDataActualValue#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#local.memberColDataValue#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_file">
								</cfcase>
								<cfcase value="TEXTAREA">
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataActualValue,chr(7))>
									<cfswitch expression="#local.column.xmlAttributes.dataTypeCode#">
									<cfcase value="CONTENTOBJ">
										<cfif int(val(local.memberColDataActualValue)) is 0>
											<cfset local.memberColDataActualValue = "__NOVALUE__">
										</cfif>
									</cfcase>
									</cfswitch>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_txtarea" value="#replace(local.memberColDataActualValue,chr(34),'&quot;','ALL')#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#left(HTMLEditFormat(local.memberColDataValue),50)#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_txtarea">
								</cfcase>
								<cfcase value="HTMLCONTENT">
									<cfset local.listOfValues = listAppend(local.listOfValues,local.memberColDataActualValue,chr(7))>
									<cfif int(val(local.memberColDataActualValue)) is 0>
										<cfset local.memberColDataActualValue = "__NOVALUE__">
									</cfif>
									<input type="radio" id="rdo_ad_#local.column.xmlAttributes.columnID#_#arguments.qryAllMembers.CurrentRow#_" name="rdo_ad_#local.column.xmlAttributes.columnID#_content" value="#local.memberColDataActualValue#" <cfif arguments.qryAllMembers.currentRow is 1>checked</cfif>>&nbsp;#left(ReReplace(local.memberColDataValue, "<[^<|>]+?>", "","ALL"),50)#
									<cfset local.hiddenControlName = "rdo_ad_#local.column.xmlAttributes.columnID#_content">
								</cfcase>
							</cfswitch>
						</td>
					</cfloop>
				</tr>
				</cfoutput>
				</cfsavecontent>
				<cfset local.firstValue = ListFirst(local.listOfValues,chr(7))>
				
				<cfif ListValueCountNoCase(local.listOfValues,local.firstValue,chr(7)) eq arguments.qryAllMembers.recordcount OR listLen(local.listOfValues,chr(7)) is 0>
					<cfsavecontent variable="local.rowContent">
						<cfoutput>
						<input type="hidden" id="#local.hiddenControlName#_hidden_" name="#local.hiddenControlName#" value="#replace(local.firstValue,chr(34),'&quot;','ALL')#">
						</cfoutput>
					</cfsavecontent>
				</cfif>
				<cfoutput>#local.rowContent#</cfoutput>
			</cfloop>
		</cfsavecontent>
		<cfreturn local.allrowContent>
	</cffunction>

	<cffunction name="editDocument" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin")>
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>

		<cfset local.rootSectionID = local.objSection.getRootSectionID(arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteinfo.siteid'), startSectionID=local.rootSectionID)>
			
		<cfset local.getMemberDocument = local.objDocument.getAdminDocumentData(arguments.event.getValue('documentID'),arguments.event.getValue('documentVersionID'),arguments.event.getValue('activeOnly',1),session.mcStruct.languageCode,arguments.event.getValue('activeSiteResource',0))>
		<cfset local.documentVersions = local.objMember.getMemberDocumentVersions(arguments.event.getValue('documentID'))>
		<cfset local.docStatus	= local.objDocumentAdmin.getDocumentStatuses()>
		<cfset local.formAction	= buildCurrentLink(arguments.event,"saveDocument") & "&mode=stream">
		
		<cfscript>
		arguments.event.setValue('siteResourceID',local.getMemberDocument.siteResourceID);
		arguments.event.setValue('documentID',local.getMemberDocument.documentID);
		arguments.event.setValue('docSectionID',local.getMemberDocument.sectionID);
		arguments.event.setValue('documentLanguageID',local.getMemberDocument.documentLanguageID);
		arguments.event.setValue('documentVersionID',local.getMemberDocument.documentVersionID);
		arguments.event.setValue('languageID',local.getMemberDocument.languageID);
		arguments.event.setValue('docTitle',local.getMemberDocument.docTitle);
		arguments.event.setValue('docDesc',local.getMemberDocument.docDesc);
		arguments.event.setValue('fileName',local.getMemberDocument.fileName);
		arguments.event.setValue('fileExt',local.getMemberDocument.fileExt);
		arguments.event.setValue('dateCreated',local.getMemberDocument.dateCreated);
		arguments.event.setValue('dateModified',local.getMemberDocument.dateModified);
		arguments.event.setValue('documentStatusID',local.getMemberDocument.siteResourceStatusID);
		arguments.event.setValue('author',local.getMemberDocument.author);
		arguments.event.setValue('memberID',arguments.event.getValue('memberID'));
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_document.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="viewDocument" access="public" output="false" returntype="void" hint="View document">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.objDocument = CreateObject("component","model.admin.documents.documentAdmin")>
		<cfset local.getMemberDocument = local.objMember.getMemberDocument(memberDocumentID=arguments.event.getValue('_mdid',0))>
		<cfset local.docStatus	= local.objDocument.getDocumentStatuses()>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_document.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addDocument" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<!--- Load Objects --->
		<cfset local.objMember = CreateObject("component","members")>
		<cfset local.objDocument = CreateObject("component","model.admin.documents.documentAdmin")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>
		
		<!--- Get Data --->
		<cfset local.getMemberDocument = local.objMember.getMemberDocument(memberDocumentID=arguments.event.getValue('_mdid',0))>
		<cfset local.docStatus	= local.objDocument.getDocumentStatuses()>
		<cfset local.formAction	= buildCurrentLink(arguments.event,"saveDocument") & "&mode=stream">
		<cfset local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfset local.rootSectionID	= local.objSection.getSectionFromSectionCode(siteID=local.orgDefaultSiteID, sectionCode="MCAMSMemberDocuments").sectionID>
		
		<cfscript>
		// set document information ----------------------------------------------------------------- ::
			arguments.event.setValue('siteResourceID',local.getMemberDocument.siteResourceID);
			arguments.event.setValue('documentID',local.getMemberDocument.documentID);
			arguments.event.setValue('docSectionID',local.rootSectionID);
			arguments.event.setValue('documentLanguageID',local.getMemberDocument.documentLanguageID);
			arguments.event.setValue('documentVersionID',local.getMemberDocument.documentVersionID);
			arguments.event.setValue('languageID',local.getMemberDocument.languageID);
			arguments.event.setValue('docTitle',local.getMemberDocument.docTitle);
			arguments.event.setValue('docDesc',local.getMemberDocument.docDesc);
			arguments.event.setValue('fileName',local.getMemberDocument.fileName);
			arguments.event.setValue('fileExt',local.getMemberDocument.fileExt);
			arguments.event.setValue('dateCreated',local.getMemberDocument.dateCreated);
			arguments.event.setValue('dateModified',local.getMemberDocument.dateModified);
			arguments.event.setValue('documentStatusID',local.getMemberDocument.siteResourceStatusID);
			arguments.event.setValue('memberID',arguments.event.getValue('memberID'));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_massUploadDocument.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveDocument" access="public" output="false" returntype="struct" hint="saves a group rule">
		<cfargument name="event" type="any">
		
		<!--- :: Load Objects  :: --->
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin")>
		<cfset local.objMembers = CreateObject("component","members")>
		<cftry>
			<cfset local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.orgDefaultSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=local.orgDefaultSiteID)>
			<cfset local.memberAdminSiteResourceID = application.objSiteInfo.getSiteInfo(local.orgDefaultSiteCode).memberAdminSiteResourceID>
			
			<cfif len(arguments.event.getValue('documentID'))>
				<!--- if there's a documentID then update the document --->
				<cfscript>
					//if( NOT local.security.edit ){ application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=1'); }
					// UPLOAD PROCEDURE ------------------------------------------------------------------------- ::
					local.DocID = arguments.event.getValue("documentID");
					local.documentVersionID = arguments.event.getValue("documentVersionID");
					local.newContributorMemberID = 0;
					local.data = "";
					// check to see if there is a new file to upload -------------------------------------------- ::
					if( arguments.event.getTrimValue('newFile') NEQ "" ){
						// if yes then set fileToUpload to the form variable newFile ------------------------------ ::
						arguments.event.setValue('fileToUpload','newFile');
						// pre set the fileUploaded variable to TRUE ---------------------------------------------- ::
						local.fileUploaded = TRUE;
						// try to upload the file to the proper destination --------------------------------------- ::
						try { local.newFile = local.objDocument.uploadFile("form.newFile"); } 
						// if if fails to upload the set the fileUploaded flag to FALSE --------------------------- ::
						catch(any excpt) { local.fileUploaded = FALSE; }
						// if file was uploaded then get the files new source data -------------------------------- ::
						if( local.fileUploaded ){
							local.objDocument.forceFileExtentionIfBlank(local.newFile);
							arguments.event.setValue('fileName',local.newFile.clientFile);
							arguments.event.setValue('fileExt',local.newFile.clientFileExt);
		
							local.newContributorMemberID = session.cfcuser.memberdata.memberID;
							// if a new file is being uploaded, then create a new version ------------------------------------------------------- ::
							local.documentVersionID = local.objDocument.insertVersion(orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
									sitecode=local.orgDefaultSiteCode, fileData=local.newFile, documentLanguageID=arguments.event.getValue('documentLanguageID'), 
									author=arguments.event.getTrimValue('author'), contributorMemberID=local.newContributorMemberID,
									recordedByMemberID=local.newContributorMemberID, oldFileExt=arguments.event.getValue('fileExt'));
						}	
						// error in upload - locate to message page and apply message --------------------------- ::
						else{ application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=3'); }
					}
					// if there's a new file uploaded, create new version, otherwise update current version	//				
					// Update Procedure ------------------------------------------------------------------------- ::
					local.fileData = { clientFile=arguments.event.getValue('fileName'), serverFileExt=arguments.event.getValue('fileExt') };
					local.loadDocument = local.objDocument.updateDocument(documentID=arguments.event.getTrimValue('documentID'), siteID=local.orgDefaultSiteID,
												docTitle=arguments.event.getTrimValue('docTitle'), docDesc=arguments.event.getTrimValue('docDesc'),
												fileData=local.fileData, sectionID=arguments.event.getTrimValue('docSectionID'), author=arguments.event.getTrimValue('author',''),
												contributorMemberID=session.cfcuser.memberdata.memberID, recordedByMemberID=session.cfcuser.memberdata.memberID,
												newFileUploaded=0);
				</cfscript>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.reloadDocPage();
						top.MCModalUtils.hideModal();
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo")>
			<cfelse>

				<!--- if there's no documentID then insert a new document --->
				<cfscript>
					var local = structNew();
					local.data.success = false;
					arguments.event.setValue('fileToUpload','newFile');
					local.fileUploaded = TRUE;
					try {
						local.newFile = local.objDocument.uploadFile("form.newFile");
						local.fileUploaded = local.newFile.uploadComplete;
					} 
					catch(any excpt) { local.fileUploaded = FALSE; }			
					if( local.fileUploaded ){
						// get new fileExt ------------------------------------------------------------------------ ::
						local.objDocument.forceFileExtentionIfBlank(local.newFile);
						arguments.event.setValue('fileName',local.newFile.clientFile);
						arguments.event.setValue('fileExt',local.newFile.serverFileExt);
						arguments.event.setValue('docTitle',local.newFile.clientFile);
						arguments.event.setValue('docDesc',local.newFile.clientFile);
						// insert new docData into database ------------------------------------------------------- ::
						local.insertResults = local.objDocument.insertDocument(siteID=local.orgDefaultSiteID, resourceType='ApplicationCreatedDocument', 
								parentSiteResourceID=local.memberAdminSiteResourceID, sectionID=arguments.event.getValue('docSectionID'), docTitle=arguments.event.getTrimValue('docTitle'),
								docDesc=arguments.event.getTrimValue('docDesc'), author=arguments.event.getTrimValue('author'), 
								fileData=local.newFile, isActive=1, isVisible=true, contributorMemberID=session.cfcuser.memberdata.memberid, 
								recordedByMemberID=session.cfcuser.memberdata.memberid, oldFileExt=arguments.event.getValue('fileExt'));

						local.objMembers.saveMemberDocument(memberID=arguments.event.getValue('memberID'), documentID=local.insertResults.documentID);
						memberDocumentIDResult = queryExecute(
							"SELECT memberDocumentID FROM dbo.ams_memberDocuments
							WHERE documentID = :documentID
							AND memberID = :memberID",
							{
								documentID: { value: local.insertResults.documentID, cfsqltype: "cf_sql_integer" },
								memberID: { value: arguments.event.getValue('memberID'), cfsqltype: "cf_sql_integer" }
							},
							{ datasource: "#application.dsn.membercentral.dsn#" } 
						);
						local.data.success = true;
						local.data.documentID = local.insertResults.documentID;
						local.data.memberDocumentID = memberDocumentIDResult.memberDocumentID;
						local.data.siteResourceID = local.insertResults.documentSiteResourceID;					
					}
				</cfscript>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to upload document for this member.">
		</cfcatch>
		</cftry>
		
		<cfreturn returnAppStruct(serializeJSON({ "body":local.data }),"echo")>
	</cffunction>
	
	<cffunction name="restoreDocumentVersion" access="public" output="false" returntype="struct" hint="makes a past version of a document the active version">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
            BEGIN TRY
				BEGIN TRAN;
					UPDATE dbo.cms_documentVersions
					SET isActive = 0
					WHERE documentLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dlID')#">
				
					UPDATE dbo.cms_documentVersions
					SET isActive = 1
					WHERE documentVersionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dvID')#">
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
				
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadDocPage();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getLocatorFieldsetID" access="public" output="no" returntype="query">
		<cfargument name="siteresourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		
		<cfset var qryFieldSet = "">
		
		<!--- validate areas --->
		<cfif NOT listFindNoCase("search,results,newacct,main,referral,linkedrecord",arguments.area)>
			<cfset arguments.area = "search">
		</cfif>
		
		<!--- get fieldset --->
		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 mfu.fieldsetID, mfs.fieldsetName, mfs.nameFormat, mfs.showHelp
			FROM dbo.ams_memberFieldUsage as mfu
			inner join dbo.ams_memberFieldSets mfs on mfs.fieldsetID = mfu.fieldsetID
			where mfu.siteResourceID = <cfqueryparam value="#arguments.siteresourceID#" cfsqltype="CF_SQL_INTEGER">
			and mfu.area = <cfqueryparam value="#arguments.area#" cfsqltype="CF_SQL_VARCHAR">
			order by mfu.fieldsetorder, mfu.fieldsetid;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn qryFieldSet>
	</cffunction>
	
	<cffunction name="getCustomFieldsetIDs" access="private" output="no" returntype="query">
		<cfargument name="siteresourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		<cfargument name="permissionMemberID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.fsQualifymemberFieldsetRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="memberFieldset", functionName="fsQualify")>
		
		<!--- validate areas --->
		<cfif NOT listFindNoCase("custom,copyinfo",arguments.area)>
			<cfset arguments.area = "custom">
		</cfif>
		
		<!--- get fieldset --->
		<cfquery name="local.qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteResourceID int, @area varchar(20), @functionID int;
			SET @area = <cfqueryparam value="#arguments.area#" cfsqltype="CF_SQL_VARCHAR">;
			SET @siteResourceID = <cfqueryparam value="#arguments.siteresourceID#" cfsqltype="CF_SQL_INTEGER">;
			SET @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fsQualifymemberFieldsetRFID#">;

			<cfif arguments.permissionMemberID gt 0>
				declare @memberID int, @groupPrintID int, @siteID int;
				SET @memberID = <cfqueryparam value="#arguments.permissionMemberID#" cfsqltype="CF_SQL_INTEGER">;
				SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = @memberID;
				SELECT @siteID = siteID FROM dbo.cms_siteResources WHERE siteResourceID = @siteResourceID;
			</cfif>

			declare @tmpFieldSet table (fieldsetID int, fieldsetName varchar(100), fieldSetOrder int);

			INSERT INTO @tmpFieldSet
			SELECT mfu.fieldsetID, mfs.fieldsetName, mfu.fieldsetOrder
			FROM dbo.ams_memberFieldUsage as mfu
			INNER JOIN dbo.ams_memberfieldsets mfs on mfs.fieldsetID = mfu.fieldsetID
			<cfif arguments.permissionMemberID gt 0>
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
					AND srfrp.siteResourceID = mfu.useSiteResourceID
					AND srfrp.functionID = @functionID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					AND gprp.rightPrintID = srfrp.rightPrintID
					AND gprp.groupPrintID = @groupPrintID
			</cfif>
			WHERE mfu.siteResourceID = @siteresourceID
			<cfif len(arguments.area)>
				AND mfu.area = @area
			</cfif>;

			SELECT fieldsetID, fieldsetName, fieldSetOrder, 1 as isApplicable, 1 as orderBy
			FROM @tmpFieldSet
			<cfif arguments.permissionMemberID gt 0>
				UNION ALL
				SELECT mfu.fieldsetID, mfs.fieldsetName, mfu.fieldsetOrder, 0 as isApplicable, 2 as orderBy
				FROM dbo.ams_memberFieldUsage as mfu
				INNER JOIN dbo.ams_memberFieldSets as mfs on mfs.fieldSetID = mfu.fieldsetID
				WHERE mfu.siteResourceID = @siteresourceID
				<cfif len(arguments.area)>
					AND mfu.area = @area
				</cfif>
				AND not exists (SELECT 1 FROM @tmpFieldSet WHERE fieldsetID = mfu.fieldsetID)
			</cfif>
			ORDER BY orderBy, fieldSetOrder, fieldsetID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn local.qryFieldSet>
	</cffunction>
	
	<cffunction name="showVCard" access="public" output="false" returntype="struct" hint="download the vCard for the member">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local 				= structNew();
			local.rc 				= arguments.event.getCollection();
			local.objMember 		= CreateObject("component","members");
			
			// get member 
			local.qryMember = local.objMember.getMember_demo(int(val(arguments.event.getValue('memberID',0))));
			local.rc.memberID = int(val(local.qrymember.memberID));

			// no member found 
			if (local.rc.memberID is 0) application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=2');
		</cfscript>

		<cfquery name="local.qryOrgData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select hasPrefix, hasMiddleName, hasSuffix, hasProfessionalSuffix
			from dbo.organizations
			where orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryMemberAddresses" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select top 1 mat.addressTypeOrder,mat.hasAttn,mat.hasAddress2,mat.hasAddress3,REPLACE(mat.addressType, ' ', '_') as addressType, ma.attn, ma.address1, ma.address2, ma.address3, ma.city, ma.stateName, ma.postalCode, ma.countryName as country
			from dbo.ams_memberAddresses ma
			inner join dbo.ams_memberAddressTypes mat on mat.orgID = @orgID
				and mat.addressTypeID = ma.addressTypeID
			where ma.orgID = @orgID
			and ma.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.memberID#">
			order by mat.addressTypeOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.qryMemberPhones" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.memberID#">;

			select top 1 mat.addressTypeOrder as prefOrder, mpt.phoneTypeOrder, mp.phone, 
				REPLACE(mat.addressType, ' ', '_') + REPLACE(RTRIM(REPLACE(mpt.phoneType, 'Phone', '')), ' ', '_') as phoneType
			from dbo.ams_memberPhones mp
			inner join dbo.ams_memberPhoneTypes mpt on mpt.orgID = @orgID
				and mpt.phoneTypeID = mp.phoneTypeID
			inner join dbo.ams_memberAddresses ma on ma.orgID = @orgID
				and ma.memberID = @memberID
				and ma.addressID = mp.addressID
			inner join dbo.ams_memberAddressTypes mat on mat.orgID = @orgID
				and mat.addressTypeID = ma.addressTypeID
			where mp.orgID = @orgID
			and mp.memberID = @memberID
			order by mat.addressTypeOrder, mpt.phoneTypeOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfquery name="local.qryMemberPrimaryEmail" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rc.memberID#">;

			select me.email
			from dbo.ams_memberEmails as me
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
				and metag.memberID = me.memberID
				and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
				and metagt.emailTagTypeID = metag.emailTagTypeID
				and metagt.emailTagType = 'Primary'
			where me.orgID = @orgID
			and me.memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showMember_VCard.cfm">
		</cfsavecontent>
				
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showListChangeHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
			
		<cftry>
			<cfset local.historyObj = CreateObject('component', 'model.system.platform.history')>

			<cfset local.auditTrailFlag = arguments.event.getValue('at','')>
			<cfif local.auditTrailFlag eq 'all'>
				<cfset local.listAuditTrail = local.historyObj.getListUpdateHistory(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=arguments.event.getValue('memberID',0))>
			<cfelse>
				<cfset local.listAuditTrail = local.historyObj.getListUpdateHistory(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=arguments.event.getValue('memberID',0), limit="5")>
			</cfif>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_memberForm_lists_changeHistory.cfm">
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data = "Unable to load list change history for this member.">
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addPanel" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any" />
		<cfset var local = structNew()>
		<!--- Load Objects --->
		<cfset local.objMember = CreateObject("component","members") />
		<cfset local.objDocument = CreateObject("component","model.admin.documents.documentAdmin") />
		<cfset local.objSystemDocument = CreateObject("component","model.system.platform.document") />
		<cfset local.objSection = CreateObject("component","model.system.platform.section") />
		<cfset local.objAdminReferrals = createObject("component","model.admin.referrals.referrals") />
		<cfset local.qryReferralSettings = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')) />
		<cfset local.referralID = local.qryReferralSettings.referralID />
		<cfset local.isPanelGroupDepend = local.qryReferralSettings.isPanelGroupDepend />
		
		<!--- Get Document Data --->
		<cfset local.qryGetAppDocument = local.objMember.getApplicationDocumentByID(applicationDocumentID=arguments.event.getValue('_pdid',0)) />
		<cfset local.docStatus	= local.objDocument.getDocumentStatuses()>
		<cfset local.formAction	= buildCurrentLink(arguments.event,"savePanel") & "&mode=stream">
		<cfset local.rootSectionID	= local.objSection.getRootSectionID(arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<!--- Get Panels Data --->
		<cfset local.qryGetPanels = local.objMember.getPanels(memberID=arguments.event.getValue('memberID'), referralID=local.referralID) />
		<cfset arguments.event.paramValue('referralID',local.referralID) />
		<cfset local.qryGetPanelMemberAppStatuses = local.objMember.getPanelMemberAppStatuses(event=arguments.event) />
		<cfset local.qryGetPanelMemberStatuses = local.objMember.getPanelMemberStatuses(event=arguments.event) />
		
		<!--- ::  get panelmemberchecklist fieldset :: --->
		<cfset local.referralSiteResourceID = local.qryReferralSettings.siteResourceID />
		<cfset local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.referralSiteResourceID, area='panelmemberchecklist') />
		<cfset local.fieldsetInfo = structNew() />
		<cfset local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName />
		<cfset local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat />
		<cfset local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp />
		<cfset local.xmlChecklistFields = createObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="panelMemberChecklist") />
		
		<cfset local.thisMember = local.objMember.getMember(memberid=arguments.event.getValue('memberID'),orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
		<cfset local.xmlAdditionalData_Member = local.thisMember.xmlAdditionalData />
		<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=arguments.event.getValue('mc_siteInfo.orgID')) />
		
		<cfscript>
		// set document information ----------------------------------------------------------------- ::
			arguments.event.setValue('siteResourceID',local.qryGetAppDocument.siteResourceID);
			arguments.event.setValue('documentID',local.qryGetAppDocument.documentID);
			arguments.event.setValue('docSectionID',local.rootSectionID);
			arguments.event.setValue('documentLanguageID',local.qryGetAppDocument.documentLanguageID);
			arguments.event.setValue('documentVersionID',local.qryGetAppDocument.documentVersionID);
			arguments.event.setValue('languageID',local.qryGetAppDocument.languageID);
			arguments.event.setValue('docTitle',local.qryGetAppDocument.docTitle);
			arguments.event.setValue('docDesc',local.qryGetAppDocument.docDesc);
			arguments.event.setValue('fileName',local.qryGetAppDocument.fileName);
			arguments.event.setValue('fileExt',local.qryGetAppDocument.fileExt);
			arguments.event.setValue('dateCreated',local.qryGetAppDocument.dateCreated);
			arguments.event.setValue('dateModified',local.qryGetAppDocument.dateModified);
			arguments.event.setValue('documentStatusID',local.qryGetAppDocument.siteResourceStatusID);
			arguments.event.setValue('memberID',arguments.event.getValue('memberID'));
			arguments.event.setValue('panelID',arguments.event.getValue('panelID',0));
			arguments.event.setValue('panelName',arguments.event.getValue('panelName',''));
			arguments.event.setValue('applicationDate',arguments.event.getValue('applicationDate',''));
			arguments.event.setValue('dateApplicationStatus',arguments.event.getValue('dateApplicationStatus',''));
			arguments.event.setValue('dateStatus',arguments.event.getValue('dateStatus',''));
			arguments.event.setValue('applicationStatusID',arguments.event.getValue('applicationStatusID',0));
			arguments.event.setValue('statusID',arguments.event.getValue('statusID',0));
			arguments.event.setValue('panelMemberNotes',arguments.event.getValue('panelMemberNotes',''));
			arguments.event.setValue('isCertified',arguments.event.getValue('isCertified',0));
			arguments.event.setValue('createdBy','');
			arguments.event.setValue('dateCreatedMemberPanel','');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_memberPanel.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editPanel" access="public" output="false" returntype="struct" hint="edit document">
		<cfargument name="Event" type="any" />
		<cfset var local = structNew()>
		<cfset local.objMember = CreateObject("component","members") />
		
		<cfset local.qryGetPanelMemberData = local.objMember.getPanelMemberByID(arguments.event.getValue('panelMemberID')) />
		<cfset local.qryGetPanelMemberAppDocument = local.objMember.getPanelMemberAppDocument(arguments.event.getValue('panelMemberID')) />
		
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin")>
		<cfset local.objSystemDocument = CreateObject("component","model.system.platform.document") />
		
		<!--- :: LOAD OBJECTS :: --->
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		<cfset local.objSection = CreateObject("component","model.system.platform.section")>
		<!--- :: GET SECTION INFORMATION :: --->
		<cfset local.rootSectionID	= local.objSection.getRootSectionID(arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteinfo.siteid'), startSectionID=local.rootSectionID)>
			
		<!--- :: GET DOCUMENT DATA :: --->
		<cfif not val(local.qryGetPanelMemberAppDocument.documentID)>
			<cfset local.getMemberDocument = local.objMember.getApplicationDocumentByID(applicationDocumentID=arguments.event.getValue('_pdid',0)) />
		<cfelse>
			<cfset arguments.event.setValue('mca_ta', 'editdocument') />
			<cfset local.getMemberDocument = local.objDocument.getAdminDocumentData(local.qryGetPanelMemberAppDocument.documentID,local.qryGetPanelMemberAppDocument.documentVersionID,arguments.event.getValue('activeOnly',1),session.mcStruct.languageCode,arguments.event.getValue('activeSiteResource',0))>
			<cfset local.documentVersions = local.objMember.getMemberDocumentVersions(local.qryGetPanelMemberAppDocument.documentID)>
		</cfif>
		<cfset local.docStatus	= local.objDocumentAdmin.getDocumentStatuses()>
		<cfset local.formAction	= buildCurrentLink(arguments.event,"savePanel") & "&mode=stream">
		
		<cfset local.objAdminReferrals = createObject("component","model.admin.referrals.referrals") />
		<cfset local.qryReferralSettings = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')) />
		<cfset local.referralID = local.qryReferralSettings.referralID />
		<cfset local.isPanelGroupDepend = local.qryReferralSettings.isPanelGroupDepend />		
		<cfset arguments.event.paramValue('referralID',local.referralID) />
		<cfset local.qryGetPanelMemberAppStatuses = local.objMember.getPanelMemberAppStatuses(event=arguments.event) />
		<cfset local.qryGetPanelMemberStatuses = local.objMember.getPanelMemberStatuses(event=arguments.event) />
		
		<!--- ::  get panelmemberchecklist fieldset :: --->
		<cfset local.referralSiteResourceID = local.qryReferralSettings.siteResourceID />
		<cfset local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.referralSiteResourceID, area='panelmemberchecklist') />
		<cfset local.fieldsetInfo = structNew() />
		<cfset local.fieldsetInfo.fieldsetName = local.qryFieldsetID.fieldsetName />
		<cfset local.fieldsetInfo.nameFormat = local.qryFieldsetID.nameFormat />
		<cfset local.fieldsetInfo.showHelp = local.qryFieldsetID.showHelp />
		<cfset local.xmlChecklistFields = createObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="panelMemberChecklist") />
		
		<cfset local.thisMember = local.objMember.getMember(memberid=local.qryGetPanelMemberData.memberID,orgID=arguments.event.getValue('mc_siteInfo.orgID')) />
		<cfset local.xmlAdditionalData_Member = local.thisMember.xmlAdditionalData />
		<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=arguments.event.getValue('mc_siteInfo.orgID')) />
		
		<cfscript>
		// set document information ----------------------------------------------------------------- ::
			arguments.event.setValue('siteResourceID',local.getMemberDocument.siteResourceID);
			arguments.event.setValue('documentID',local.getMemberDocument.documentID);
			arguments.event.setValue('docSectionID',local.getMemberDocument.sectionID);
			arguments.event.setValue('documentLanguageID',local.getMemberDocument.documentLanguageID);
			arguments.event.setValue('documentVersionID',local.getMemberDocument.documentVersionID);
			arguments.event.setValue('languageID',local.getMemberDocument.languageID);
			arguments.event.setValue('docTitle',local.getMemberDocument.docTitle);
			arguments.event.setValue('docDesc',local.getMemberDocument.docDesc);
			arguments.event.setValue('fileName',local.getMemberDocument.fileName);
			arguments.event.setValue('fileExt',local.getMemberDocument.fileExt);
			arguments.event.setValue('dateCreated',local.getMemberDocument.dateCreated);
			arguments.event.setValue('dateModified',local.getMemberDocument.dateModified);
			arguments.event.setValue('documentStatusID',local.getMemberDocument.siteResourceStatusID);
			if (val(local.qryGetPanelMemberAppDocument.documentID))
				arguments.event.setValue('author',local.getMemberDocument.author);
			arguments.event.setValue('memberID',local.qryGetPanelMemberData.memberID);
			arguments.event.setValue('panelID',local.qryGetPanelMemberData.panelID);
			arguments.event.setValue('panelName',local.qryGetPanelMemberData.panelName);
			arguments.event.setValue('applicationDate',local.qryGetPanelMemberData.applicationDate);
			arguments.event.setValue('dateApplicationStatus',local.qryGetPanelMemberData.dateApplicationStatus);
			arguments.event.setValue('dateStatus',local.qryGetPanelMemberData.dateStatus);
			arguments.event.setValue('applicationStatusID',local.qryGetPanelMemberData.applicationStatusID);
			arguments.event.setValue('statusID',local.qryGetPanelMemberData.statusID);
			arguments.event.setValue('panelMemberNotes',local.qryGetPanelMemberData.panelMemberNotes);
			arguments.event.setValue('isCertified',local.qryGetPanelMemberData.isCertified);
			arguments.event.setValue('createdBy',local.qryGetPanelMemberData.panelCreatedMember);
			arguments.event.setValue('dateCreatedMemberPanel',local.qryGetPanelMemberData.dateCreatedMemberPanel);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_memberPanel.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="savePanel" access="public" output="false" returntype="struct" hint="saves a group rule">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew() />
		<cfset local.data = "" />
		<!--- :: Load Objects  :: --->
		<cfset local.objDocument = CreateObject("component","model.system.platform.document") />
		<cfset local.objDocumentAdmin = CreateObject("component","model.admin.documents.documentAdmin") />
		<cfset local.objMembers = CreateObject("component","members") />
		
		<cfscript>
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.AdminReferralsSiteResourceID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).siteResourceID;

	
			local.panelMemberID = arguments.event.getValue('panelMemberID');
	
			if (local.panelMemberID)
				local.objMembers.updatePanelMember(arguments.event);
			else
				local.panelMemberID = local.objMembers.insertPanelMember(arguments.event);
		</cfscript>
		
		<cfif val(arguments.event.getValue('documentID')) and len(trim(form.newFile))>
			<!--- if there's a documentID than update the document --->
			<cfscript>
				//if( NOT local.security.edit ){ application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=1'); }
				// UPLOAD PROCEDURE ------------------------------------------------------------------------- ::
				local.DocID = arguments.event.getValue("documentID");
				local.documentVersionID = arguments.event.getValue("documentVersionID");
				local.newContributorMemberID = 0;
				// check to see if there is a new file to upload -------------------------------------------- ::
				if( arguments.event.getTrimValue('newFile') NEQ "" ){
					// if yes then set fileToUpload to the form variable newFile ------------------------------ ::
					arguments.event.setValue('fileToUpload','newFile');
					// pre set the fileUploaded variable to TRUE ---------------------------------------------- ::
					local.fileUploaded = TRUE;
					// try to upload the file to the proper destination --------------------------------------- ::
					try { local.newFile = local.objDocument.uploadFile("form.newFile"); } 
					// if if fails to upload the set the fileUploaded flag to FALSE --------------------------- ::
					catch(any excpt) { local.fileUploaded = FALSE; }
					// if file was uploaded then get the files new source data -------------------------------- ::
					if( local.fileUploaded ){
						local.objDocument.forceFileExtentionIfBlank(local.newFile);
						arguments.event.setValue('fileName',local.newFile.clientFile);
						arguments.event.setValue('fileExt',local.newFile.clientFileExt);
	
						local.newContributorMemberID = session.cfcuser.memberdata.memberID;
						// if a new file is being uploaded, then create a new version ------------------------------------------------------- ::
						local.documentVersionID = local.objDocument.insertVersion(
									orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
									sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), 
									fileData=local.newFile, 
									documentLanguageID=arguments.event.getValue('documentLanguageID'),
									author=arguments.event.getTrimValue('author'),
									contributorMemberID=local.newContributorMemberID,
									recordedByMemberID=local.newContributorMemberID,
									oldFileExt=arguments.event.getValue('fileExt'));
					}	
					// error in upload - locate to message page and apply message --------------------------- ::
					else{ application.objCommon.redirect('#buildCurrentLink(arguments.event,"message")#&message=3'); }
				}
				// if there's a new file uploaded, create new version, otherwise update current version	//				
				// Update Procedure ------------------------------------------------------------------------- ::
				local.fileData = { clientFile=arguments.event.getValue('fileName'), serverFileExt=arguments.event.getValue('fileExt') };
				local.loadDocument = local.objDocument.updateDocument(
												documentID=arguments.event.getTrimValue('documentID'),
												siteID=arguments.event.getTrimValue('mc_siteInfo.siteID'),
												docTitle=arguments.event.getTrimValue('docTitle'),
												docDesc=arguments.event.getTrimValue('docDesc'),
												fileData=local.fileData,
												sectionID=arguments.event.getTrimValue('docSectionID'),
												author=arguments.event.getTrimValue('author',''),
												contributorMemberID=session.cfcuser.memberdata.memberID,
												recordedByMemberID=session.cfcuser.memberdata.memberID,
												newFileUploaded=0
												);
			</cfscript>
		<cfelse>
			<cfif len(trim(form.newFile))>
				<!--- if there's no documentID than insert a new document --->
				<cfscript>
					arguments.event.setValue('fileToUpload','newFile');
					local.fileUploaded = TRUE;
					try {
						local.newFile = local.objDocument.uploadFile("form.newFile");
						local.fileUploaded = local.newFile.uploadComplete;
					} 
					catch(any excpt) { local.fileUploaded = FALSE; }			
					if( local.fileUploaded ){
						// get new fileExt ------------------------------------------------------------------------ ::
						local.objDocument.forceFileExtentionIfBlank(local.newFile);
						arguments.event.setValue('fileName',local.newFile.clientFile);
						arguments.event.setValue('fileExt',local.newFile.serverFileExt);
						// insert new docData into database ------------------------------------------------------- ::
							local.insertResults = local.objDocument.insertDocument(
												siteID=arguments.event.getValue('mc_siteinfo.siteid'),
												resourceType='ApplicationCreatedDocument',
												parentSiteResourceID=local.AdminReferralsSiteResourceID,
												sectionID=arguments.event.getValue('docSectionID'),
												docTitle=arguments.event.getTrimValue('docTitle'),
												docDesc=arguments.event.getTrimValue('docDesc'),
												author=arguments.event.getTrimValue('author'),
												fileData=local.newFile, 
												isActive=1,
												isVisible=true,
												contributorMemberID=session.cfcuser.memberdata.memberid,
												recordedByMemberID=session.cfcuser.memberdata.memberid,
												oldFileExt=arguments.event.getValue('fileExt'));
							
						local.objMembers.savePanelAppDocument(
								local.panelMemberID,
								local.insertResults.documentID
								);
						}
				</cfscript>
			</cfif><!--- //if len(trim(form.newFile)) --->
		</cfif>
		
		<cfif val(arguments.event.getValue('deletedDocumentID'))>
			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.event.getValue('mc_siteInfo.siteID'), documentID=arguments.event.getValue('deletedDocumentID'))>
			<cfset local.objMembers.savePanelAppDocument(local.panelMemberID, 0)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.refreshRefTabGrids();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addSubPanel" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objMembers = CreateObject("component","members");
			local.formLink = buildCurrentLink(arguments.event,"saveSubPanel") & "&mode=stream";
			local.panelMemberID = 0;
			local.panelID = arguments.event.getValue('panelID');
			local.memberID = arguments.event.getValue('memberID');
			local.objAdminReferrals = createObject("component","model.admin.referrals.referrals");
			local.referralID = local.objAdminReferrals.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).referralID;
			local.statusID = local.objMembers.getPanelStatusByName(statusName="Active",referralID=local.referralID).panelMemberStatusID;
			local.qryGetSubPanels = local.objMembers.getSubPanels(panelID=local.panelID, memberid=local.memberID);
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_subPanel.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="saveSubPanel" access="public" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfset var local = structNew()>
		<cfset local.data = "">
		<cfset local.objMembers = CreateObject("component","members")>		

		<cfloop list="#arguments.event.getValue('subPanelID')#" index="local.subPanelID">
			<cfset arguments.event.setValue('subPanelID',int(val(local.subPanelID)))>
			<cfset local.objMembers.insertPanelMember(arguments.event)>
		</cfloop>	

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshRefTabGrids();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="processMemberInQueue" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.platformQueue.dsn#" name="local.qryQueue">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">, null);

					EXEC dbo.queue_triggerMCGCache @runImmediately=1, @type='ConditionsAndGroups';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnstruct.success = true>
		<cfcatch type="Any">
			<!--- <cfset application.objError.sendError(cfcatch=cfcatch)> --->
			<cfset local.returnstruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="generateLoginAsMemberLink" access="public" output="false" returntype="struct">
		<cfargument name="memberid" type="numeric" required="yes">
		<cfargument name="username" type="string" required="yes">
		<cfargument name="sitecode" type="string" required="no" default="#session.mcStruct.sitecode#">
		<cfargument name="scheme" type="string" required="no" default="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#">
		<cfargument name="hostname" type="string" required="no" default="#application.objPlatform.getCurrentHostname()#">
		<cfargument name="returnURL" type="string" required="no" default="">
		
		
		<cfset var local = structNew()>

		<!--- this is purposely not limited to those who we allow in the interface since API allows generation of link for all accounts. --->
		<cftry>
			<cfset local.arrData = [
				getTickCount(),
				application.objSiteInfo.getSiteInfo(arguments.siteCode).siteid,
				arguments.memberid,
				arguments.username,
				session.cfcuser.memberdata.memberid,
				arguments.returnURL,
				getTickCount(),
				now()
			]>
			<cfset local.data["loginlink"] = '#arguments.scheme#://' & arguments.hostname & '/lam/' & encrypt(ArrayToList(local.arrData,"|"),"M@!8T$.LuX&C","CFMX_COMPAT","Hex")>
			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="generateLoginAsMemberLinkForAnotherSite" access="public" output="false" returntype="struct">
		<cfargument name="destinationSitecode" type="string" required="true">
		<cfargument name="destinationRelativeURL" type="string" required="false" default="/">
		<cfargument name="sitecodeForCurrentSite" type="string" required="false" default="#session.mcStruct.sitecode#">
		<cfargument name="hostnameForCurrentSite" type="string" required="false" default="#application.objPlatform.getCurrentHostname()#">
		
		<cfscript>
			var local = structNew();
			local.returnStruct = {"success"=false};

			local.allowLoginAsMember = false;
			local.currentSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=arguments.sitecodeForCurrentSite);
			local.destinationSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=arguments.destinationSitecode);
			
			// validate
			if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
				local.allowLoginAsMember = true;
			} else if (application.objUser.isSiteAdmin(cfcuser=session.cfcuser) AND local.currentSiteInfo.orgID EQ local.destinationSiteInfo.orgID) {
				local.allowLoginAsMember = true;
			}

			// invalid
			if (NOT local.allowLoginAsMember) {
				return local.returnStruct;
			}
			
			local.destinationHostname = application.objWebsite.getHostnameForAnotherSite(destinationSitecode=arguments.destinationSitecode, sitecodeForCurrentSite=arguments.sitecodeForCurrentSite, 
											hostnameForCurrentSite=arguments.hostnameForCurrentSite);
			
			if (len(local.destinationHostname)) {
				local.memberID = session.cfcuser.memberdata.memberid;
				local.username = session.cfcuser.memberdata.username;
				local.destinationURL = '#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#local.destinationHostname##arguments.destinationRelativeURL#';
				local.returnStruct = generateLoginAsMemberLink(
					memberid=local.memberID,
					username=local.username,
					sitecode=arguments.destinationSitecode,
					hostname=local.destinationHostname,
					returnURL=local.destinationURL);
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>
	<cffunction name="refreshAddressDistricting" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="aid" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfset createObject("component","model.scheduledTasks.tasks.districtMatching").queueSingleAddress(addressID=arguments.aid, orgID=arguments.mcproxy_orgID)>
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="forceRefreshMemberGroups" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_forceRefreshMemberGroups">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			</cfstoredproc>

			<cfset local.returnstruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnstruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="showLinkedRecordsImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleLinkRecordImportTemplate") & "&mode=stream";
			local.processImport = buildCurrentLink(arguments.event,"processLinkRecordImport");
			local.processUnlinkImport = buildCurrentLink(arguments.event,"processUnlinkRecordImport");
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importLinkedRecords.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleLinkRecordImportTemplate" access="public" output="false" returntype="struct" hint="Sample Import Template">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.showImport = buildCurrentLink(arguments.event,"showLinkedRecordsImport")>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = arguments.event.getValue('templateMode','linkedRecords') eq "unlinkRecords" ? "sampleUnlinkRecordsImportFile.csv" : "sampleLinkedRecordsImportFile.csv">

		<cfquery name="local.qryTempate" datasource="#application.dsn.memberCentral.dsn#">
			IF OBJECT_ID('tempdb..##tblLinkedRecords') IS NOT NULL 
				DROP TABLE ##tblLinkedRecords;

			select 'Req' as parentMemberNumber, 'Req' as childMemberNumber, 'Req' as Role
			into ##tblLinkedRecords;

			DECLARE @selectsql varchar(max) = '
				SELECT *, 1 as mcCSVorder 
				*FROM* ##tblLinkedRecords';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			-- drop the temp table
			IF OBJECT_ID('tempdb..##tblLinkedRecords') IS NOT NULL 
				DROP TABLE ##tblLinkedRecords;
		</cfquery>


		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#local.showImport#" addtoken="no">
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processLinkRecordImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.members.memberImport");
		local.showImportLink = buildCurrentLink(arguments.event,"showLinkedRecordsImport");

		// Build breadCrumb Trail
		appendBreadCrumbs(arguments.event,{ link='', text="Import Results" });
		</cfscript>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">
		<cfset local.prepResult = local.objImport.processLinkedRecordImport(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL=local.showImportLink)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processUnlinkRecordImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.members.memberImport");
		local.showImportLink = buildCurrentLink(arguments.event,"showLinkedRecordsImport");

		// Build breadCrumb Trail
		appendBreadCrumbs(arguments.event,{ link='', text="Import Results" });
		</cfscript>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">
		<cfset local.prepResult = local.objImport.processUnlinkRecordsImport(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL=local.showImportLink)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="isMemberInSiteAdminGroup" access="public" output="no" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryMemberIsSiteAdmin = "">

		<cfquery name="qryMemberIsSiteAdmin" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select dbo.fn_isSiteAdmin(<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">) as isSiteAdmin;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryMemberIsSiteAdmin.isSiteAdmin)>
	</cffunction>

	<cffunction name="isMemberASuperUser" access="private" output="no" returntype="boolean">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryIsMemberASuperUser = "">

		<cfquery name="qryIsMemberASuperUser" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select dbo.fn_IsSuperUser(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">) as isSuperUser;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn val(qryIsMemberASuperUser.isSuperUser)>
	</cffunction>

	<cffunction name="showMemberLoginHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.memberID = arguments.event.getValue('memberID',0)>
		<cfset local.loginHistoryListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberJSON&meth=getLoginHistory&mid=#local.memberID#&gridMode=memberRecord&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showMemberLoginHistory.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getCustomTabInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data['success'] = true>
		<cfset local.data['arrFields'] = arrayNew(1)>

		<cftry>
			<cfset arguments.fd = DeserializeJSON(arguments.fd)>
			<cfif not structKeyExists(arguments.fd,"mid")>
				<cfset arguments.fd['mid'] = 0>
			</cfif>
			<cfif not structKeyExists(arguments.fd,"fsid")>
				<cfset arguments.fd['fsid'] = 0>
			</cfif>
			<cfset local.jsonInsanityVars = "^~~~^">

			<!--- these set cache to 3s in attempt to get other ajax requests to use cached version --->
			<cfset local.xmlAdditionalData = getOrgAdditionalDataColumns(orgid=arguments.mcproxy_orgID, cache=3)>
			<cfset local.xmlAdditionalData_Member = CreateObject("component","members").getMemberAdditionalData(memberID=arguments.fd.mid, cache=3)>

			<cfif arguments.fd.fsid gt 0>
				<cfset local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets")>
				<cfset local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=arguments.fd.fsid, usage="memberAdminCustom")>
				<cfset local.showHelp = xmlSearch(local.xmlFields,"string(//@showHelp)")>
			<cfelse>
				<cfstoredproc procedure="ams_getMemberCustomFieldsNotInFieldSets" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					<cfprocresult name="local.xmlFields" resultset="1">
				</cfstoredproc>
				<cfset local.xmlFields = XMLParse(local.xmlFields.fieldsXML)>
				<cfset local.showHelp = 0>
			</cfif>

			<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
				<cfset local.columnID = local.thisfield.xmlattributes.mdColumnID>
				<cfset local.memberColArray = XMLSearch(local.xmlAdditionalData,"//column[@columnID=#local.columnID#]")>
				<cfset local.strField = {}>

				<cfset local.strField["columnID"] = local.columnID>
				<cfset local.strField["fieldLabel"] = htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)>
				<cfset local.strField["displayTypeCode"] = local.thisfield.xmlattributes.displayTypeCode>
				<cfset local.strField["dataTypeCode"] = local.thisfield.xmlattributes.dataTypeCode>
				<cfset local.strField["fieldCode"] = local.thisfield.xmlattributes.fieldCode>

				<cfif local.showHelp and len(trim(local.thisfield.xmlattributes.fieldDescription))>
					<cfset local.strField["showHelp"] = 1>
					<cfset local.strField["fieldDescription"] = JSStringFormat(trim(local.thisfield.xmlattributes.fieldDescription))>
				</cfif>
				<cfif local.thisfield.xmlattributes.isRequired>
					<cfset local.strField["isRequired"] = 1>
				</cfif>
				<cfif local.thisfield.xmlattributes.isReadOnly>
					<cfset local.strField["isReadOnly"] = 1>
				</cfif>
				<cfif local.thisfield.xmlattributes.allowNull>
					<cfset local.strField["allowNull"] = 1>
				</cfif>
				<cfif local.thisfield.xmlattributes.allowMultiple>
					<cfset local.strField["allowMultiple"] = 1>
				</cfif>

				<cfif listFindNoCase("SELECT,RADIO",local.thisfield.xmlattributes.displayTypeCode) and not local.thisfield.xmlattributes.allowMultiple>
					<cfset local.strField["value"] = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@valueID)")>
				<cfelseif local.thisfield.xmlattributes.displayTypeCode eq 'CHECKBOX' OR (local.thisfield.xmlattributes.displayTypeCode EQ 'SELECT' AND local.thisfield.xmlattributes.allowMultiple)>
					<cfset local.strField["value"] = listToArray(ReReplace(replaceNoCase(ArrayToList(XMLSearch(local.xmlAdditionalData_Member,"//column[@columnID=#local.columnID#]/@valueID")),'<?xml version="1.0" encoding="UTF-8"?>','','ALL'),"[^0-9,]","","ALL"))>
				<cfelseif local.thisfield.xmlattributes.displayTypeCode eq 'DOCUMENT'>
					<cfset local.strField["value"] = XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@actualColumnValue)")>
					<cfif len(local.strField.value)>
						<cfif not structKeyExists(local,"objDocument")>
							<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
						</cfif>
						<cfset local.strField["documentID"] = local.objDocument.getDocumentDataBySiteResourceID(siteResourceID=local.strField.value).documentID>
						<cfset local.strField["filename"] = local.jsonInsanityVars & XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@columnValue)")>
					<cfelse>
						<cfset local.strField["documentID"] = 0>
						<cfset local.strField["filename"] = "">
					</cfif>
				<cfelseif local.thisfield.xmlattributes.displayTypeCode eq 'HTMLCONTENT'>
					<cfset local.strField["valueSRID"] = val(XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@actualColumnValue)"))>
					<cfset local.strField["value"] = local.jsonInsanityVars & XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@columnValue)")>
					<cfif not local.thisfield.xmlattributes.isReadOnly and local.strField.valueSRID gt 0>
						<cfquery name="local.getContentID" datasource="#application.dsn.membercentral.dsn#">
							SELECT TOP 1 contentID
							FROM dbo.cms_content
							WHERE siteResourceID = <cfqueryparam value="#local.strField.valueSRID#" cfsqltype="CF_SQL_INTEGER">
							ORDER BY ContentID desc
						</cfquery>
						<cfset local.strField["contentID"] = val(local.getContentID.contentID)>
					<cfelse>
						<cfset local.strField["contentID"] = 0>
					</cfif>
					<cfset local.strField['rovalue'] = ReReplace(local.strField.value, "<[^<|>]+?>", "","ALL")>
					<cfset local.strField["value"] = XMLFormat(local.strField.value)>
				<cfelse>
					<cfset local.strField["value"] = local.jsonInsanityVars & XMLSearch(local.xmlAdditionalData_Member,"string(//column[@columnID=#local.columnID#]/@columnValue)")>
				</cfif>

				<cfif structKeyExists(local.memberColArray[1].xmlattributes,"defaultValueID") and listFindNoCase("RADIO,SELECT,CHECKBOX",local.thisfield.xmlattributes.displayTypeCode)>
					<cfset local.strField["defvalue"] = local.memberColArray[1].xmlattributes.defaultValueID>
				<cfelseif structKeyExists(local.memberColArray[1].xmlattributes,"defaultValue") and listFindNoCase("TEXTBOX,DATE",local.thisfield.xmlattributes.displayTypeCode)>
					<cfset local.strField["defvalue"] = local.memberColArray[1].xmlattributes.defaultValue>
				</cfif>

				<cfif isdefined("local.thisfield.xmlattributes.minValueDecimal2") and isdefined("local.thisfield.xmlattributes.maxValueDecimal2") and len(local.thisfield.xmlattributes.minValueDecimal2) and len(local.thisfield.xmlattributes.maxValueDecimal2)>
					<cfset local.strField["minValueDecimal2"] = local.thisfield.xmlattributes.minValueDecimal2>
					<cfset local.strField["maxValueDecimal2"] = local.thisfield.xmlattributes.maxValueDecimal2>
				<cfelseif isdefined("local.thisfield.xmlattributes.minValueInt") and isdefined("local.thisfield.xmlattributes.maxValueInt") and len(local.thisfield.xmlattributes.minValueInt) and len(local.thisfield.xmlattributes.maxValueInt)>
					<cfset local.strField["minValueInt"] = local.thisfield.xmlattributes.minValueInt>
					<cfset local.strField["maxValueInt"] = local.thisfield.xmlattributes.maxValueInt>
				<cfelseif isdefined("local.thisfield.xmlattributes.minChars") and isdefined("local.thisfield.xmlattributes.maxChars") and local.thisfield.xmlattributes.minChars gt 0 and local.thisfield.xmlattributes.maxChars gt 0>
					<cfset local.strField["minChars"] = local.thisfield.xmlattributes.minChars>
					<cfset local.strField["maxChars"] = local.thisfield.xmlattributes.maxChars>
				<cfelseif isdefined("local.thisfield.xmlattributes.minSelected") and isdefined("local.thisfield.xmlattributes.maxSelected") and local.thisfield.xmlattributes.minSelected gt 0 and local.thisfield.xmlattributes.maxSelected gt 0>
					<cfset local.strField["minSelected"] = local.thisfield.xmlattributes.minSelected>
					<cfset local.strField["maxSelected"] = local.thisfield.xmlattributes.maxSelected>
				<cfelseif isdefined("local.thisfield.xmlattributes.minValueDate") and isdefined("local.thisfield.xmlattributes.maxValueDate") and len(local.thisfield.xmlattributes.minValueDate) and len(local.thisfield.xmlattributes.maxValueDate)>
					<cfset local.strField["minValueDate"] = DateFormat(replaceNoCase(local.thisfield.xmlattributes.minValueDate,'T',' '),"m/d/yyyy")>
					<cfset local.strField["maxValueDate"] = DateFormat(replaceNoCase(local.thisfield.xmlattributes.maxValueDate,'T',' '),"m/d/yyyy")>
				<cfelseif local.thisfield.xmlAttributes.displayTypeCode eq "DATE">
					<cfset local.strField["minValueDate"] = "1/1/1753">
					<cfset local.strField["maxValueDate"] = "12/31/9999">
				</cfif>

				<cfloop array="#local.thisfield.XMlChildren#" index="local.thisOpt"> 
					<cfif structKeyExists(local.thisOpt.xmlAttributes,"valueID")>
						<cfif local.thisfield.xmlAttributes.dataTypeCode eq "STRING" and structKeyExists(local.thisOpt.xmlAttributes,"columnValueString")>
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueString>
						<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "DECIMAL2" and structKeyExists(local.thisOpt.xmlAttributes,"columnValueDecimal2")>
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueDecimal2>
						<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "INTEGER" and structKeyExists(local.thisOpt.xmlAttributes,"columnValueInteger")>
							<cfset local.thisOptColValue = local.thisOpt.xmlAttributes.columnValueInteger>
						<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "DATE" and structKeyExists(local.thisOpt.xmlAttributes,"columnValueDate")>
							<cfset local.thisOptColValue = dateformat(replaceNoCase(local.thisOpt.xmlAttributes.columnValueDate,'T',' '),"m/d/yyyy")>
						<cfelseif local.thisfield.xmlAttributes.dataTypeCode eq "BIT" and structKeyExists(local.thisOpt.xmlAttributes,"columnValueBit")>
							<cfset local.thisOptColValue = YesNoFormat(local.thisOpt.xmlAttributes.columnValueBit)>
						<cfelse>
							<cfset local.thisOptColValue = "">
						</cfif>

						<cfset local.strOpt = {}>
						<cfset local.strOpt["valueID"] = local.thisOpt.xmlAttributes.valueID>
						<cfset local.strOpt["value"] = local.jsonInsanityVars & local.thisOptColValue>

						<cfif NOT structKeyExists(local.strField,"opts")>
							<cfset local.strField["opts"] = arrayNew(1)>
						</cfif>
						<cfset arrayAppend(local.strField.opts, local.strOpt)>
					</cfif>
				</cfloop>

				<cfset arrayAppend(local.data.arrFields, local.strField)>
			</cfloop>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="mergeMatchStatement" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","members").getMergeMatchStatement(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSuppressionListEmails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>

		<cfset local.qrySuppressedMemberEmails = CreateObject('component', 'model.admin.emailBlast.suppressionListEmails').getSuppressedEmailsForMember(
				siteID=arguments.event.getValue('siteID'), memberID=arguments.event.getValue('memberID'))>
		<cfset var statusMapStr = { "Bounces":"Bounced", "Invalid Addresses":"Invalid", "Spam Reports":"Spam Reporting" }>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.qrySuppressedMemberEmails.recordCount>
					<cfinclude template="frm_manageSuppressionListEmails.cfm">
				<cfelse>
					<div class="alert alert-info">No email addresses found.</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSuppressedEmail" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>

		<cfset local.currentEmail = arguments.event.getValue('email','')>

		<cfquery name="local.qryEmailTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			SELECT me.emailTypeID, met.emailType, me.emailID, me.email
			FROM dbo.ams_memberEmails as me
			INNER JOIN dbo.ams_memberEmailTypes as met on met.orgID = @orgID
				AND met.emailTypeID = me.emailTypeID
			WHERE me.orgID = @orgID
			AND me.memberID = <cfqueryparam value="#arguments.event.getValue('memberID')#" cfsqltype="CF_SQL_INTEGER">
			AND me.email = <cfqueryparam value="#local.currentEmail#" cfsqltype="CF_SQL_VARCHAR">
			ORDER BY met.emailTypeOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.event.getValue('memberID'),orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryListMemberships = CreateObject("component","members").getMemberListMemberships(memberNumber=local.memberNumber,siteID=arguments.event.getValue('mc_siteInfo.siteID'),email=local.currentEmail)>

		<cfsavecontent variable="local.data">
		<cfoutput>
			<cfinclude template="frm_manageSuppressionListEmails_update.cfm">
		</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateSuppressedMemberEmail" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="oldEmailAddress" type="string" required="yes">
		<cfargument name="newEmailAddress" type="string" required="yes">
		<cfargument name="emailTypeIDs" type="string" required="yes" hint="comma separated emailTypeIDs">
		<cfargument name="lists" type="string" required="yes" hint="comma separated list names">
		
		<cfset var local = structNew()>
		<cfset local.returnstruct = { success = false }>
		<cfset local.objMembers = CreateObject("component","members")>
		<cfset local.mc_siteInfo = duplicate(application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode))>

		<cfif NOT isValid("regex", arguments.newEmailAddress, application.regEx.email)>
			<cfreturn local.returnstruct>
		</cfif>

		<cftry>
			<cfif listLen(arguments.emailTypeIDs)>
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.memberID, siteCode=local.mc_siteInfo.siteCode)>
				<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=local.mc_siteInfo.orgID)>
				<cfloop query="local.qryOrgEmailTypes">
					<cfif listFindNoCase(arguments.emailTypeIDs, local.qryOrgEmailTypes.emailTypeID)>
						<cfset local.objSaveMember.setEmail(type=local.qryOrgEmailTypes.emailType, value=arguments.newEmailAddress)>
					</cfif>
				</cfloop>
				<cfset local.strResult = local.objSaveMember.saveData(runImmediately=0, bypassListEmailUpdate=1)>
			</cfif>
			
			<cfif listLen(arguments.lists)>
				<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=arguments.memberID,orgID=local.mc_siteInfo.orgID)>
				<cfloop list="#arguments.lists#" index="local.thisListName">
					<cfset local.objMembers.updateListEmailByListName(orgCode=local.mc_siteInfo.orgCode, memberNumber=local.memberNumber,
						listName=local.thisListName, oldEmailAddr=arguments.oldEmailAddress, newEmailAddr=arguments.newEmailAddress)>
				</cfloop>
			</cfif>
			
			<cfset local.returnstruct.success = true>
		<cfcatch type="Any">
			<cfset local.returnstruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="viewListTroubleshooterIssues" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		
		<cfset local.qryLists = getLists(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
		<cfset local.groupLink = buildLinkToTool(toolType='GroupAdmin',mca_ta='list')>
		<cfset local.orgShortName = arguments.event.getValue('mc_siteinfo.orgShortName')>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_manageListTroubleshooterIssues.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="changeCPUserPW" access="public" output="false" returntype="struct" hint="Change Control Panel User Password">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>

		<cfset local.allowChangeCPUserPassword = arguments.event.getValue('mc_siteInfo.useRemoteLogin') is 0 and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberdata.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
		<cfif local.allowChangeCPUserPassword>
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_changePassword.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = '<div class="px-3">Not allowed.</div>'>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doChangeCPUserPW" access="public" output="false" returntype="struct">
		<cfargument name="cp" type="string" required="yes">
		<cfargument name="np" type="string" required="yes">
		<cfargument name="mcsk" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnstruct = { "success":false, "err":"" }>
		
		<cftry>
			<cfset local.cp = trim(arguments.cp)>
			<cfset local.np = trim(arguments.np)>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode)>

			<cfset local.allowChangeCPUserPassword = local.mc_siteInfo.useRemoteLogin is 0 and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberdata.orgcode EQ local.mc_siteInfo.orgCode>
			<cfif local.allowChangeCPUserPassword and application.objUser.verifySecurityKey(mcSecKey=arguments.mcsk)>
				<cfif not len(local.cp)>
					<cfset local.returnstruct.err &= 'Enter your current password.<br/>'>
				</cfif>
				<cfif not len(local.np)>
					<cfset local.returnstruct.err &= 'Enter a new password.<br/>'>
				</cfif>
				<cfif len(local.cp) and len(local.np) and NOT compare(local.cp,local.np)>
					<cfset local.returnstruct.err &= 'Enter a different password.<br/>'>
				</cfif>
				<cfif len(local.returnstruct.err)>
					<cfreturn local.returnstruct>
				</cfif>

				<!--- verify current password before allowing change --->
				<cfset local.correctCurrentPassword = application.objUser.login_checkPassword(memberid=session.cfcuser.memberdata.memberID, siteid=local.mc_siteInfo.siteID, password=local.cp)>
				<cfif NOT local.correctCurrentPassword>
					<cfset local.returnstruct.err = "Invalid current password.">
					<cfreturn local.returnstruct>
				</cfif>
				
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfset application.objUser.login_setPasswordSuperUser(memberID=session.cfcuser.memberdata.memberID, networkProfileID=session.cfcuser.memberdata.profileID, password=local.np)>
				<cfelse>
					<cfset application.objUser.login_setPassword(memberID=session.cfcuser.memberdata.memberID, siteID=local.mc_siteInfo.siteID, password=local.np, recordedByMemberID=session.cfcuser.memberdata.memberID)>
				</cfif>

				<cfset local.returnstruct.success = true>
			<cfelse>
				<cfset local.returnstruct.err = 'Unable to change password.'>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnstruct.err = 'Unable to change password.'>
		</cfcatch>
		</cftry>

		<cfreturn local.returnstruct>
	</cffunction>

	<cffunction name="setDefaultNavMenu" access="public" output="false" returntype="struct" hint="Set Control Panel Default Nav Menu">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.objNavBuilder	= createObject("component","model.admin.navBuilder")>
		
		<cfset local.newNavID = arguments.event.getValue('new_mca_a')>
		<cfset local.newNavStruct = local.objNavBuilder.populateCurrentNavStruct(mca_a=local.newNavID)>
		<cfif local.newNavStruct.navAreaID EQ 4>
			<cfset local.newNavStruct.arrCrumbs.append({ link:'', text:local.newNavStruct.navName })>
		</cfif>
		<cfset local.newNavPathHTML = local.newNavStruct.arrCrumbs.reduce(function(currNavPath, strNav) { 
											return currNavPath & '<li class="breadcrumb-item">' & strNav.text & '</li>';
										}, '')>

		<cfquery name="local.qryDefaultNavItem" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT navKey
			FROM dbo.admin_defaultMemberNavItem
			WHERE memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strSavedDefault = {}>
		<cfif local.qryDefaultNavItem.recordCount>
			<cfset local.currNavStruct = local.objNavBuilder.populateCurrentNavStruct(mca_a=listFirst(local.qryDefaultNavItem.navKey,'.'))>
			<cfif local.currNavStruct.navAreaID EQ 4>
				<cfset local.currNavStruct.arrCrumbs.append({ link:'', text:local.currNavStruct.navName })>
			</cfif>
			<cfset local.currNavPathHTML = local.currNavStruct.arrCrumbs.reduce(function(currNavPath, strNav) { 
												return currNavPath & '<li class="breadcrumb-item">' & strNav.text & '</li>';
											}, '')>
			
			<cfset local.strSavedDefault.insert("navKey",local.qryDefaultNavItem.navKey)>
			<cfset local.strSavedDefault.insert("navPathHTML",local.currNavPathHTML)>
		</cfif>

		<cfset local.canModifyNav = structIsEmpty(local.strSavedDefault) OR local.newNavStruct.navKey NEQ local.qryDefaultNavItem.navKey>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_setDefaultNavMenu.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveMemberDefaultNavItem" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="navKey" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = {}>

		<cftry>
			<cfquery name="local.qrySaveMemberDefaultNavItem" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				
				DECLARE @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@navKey varchar(9) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.navKey#">;

				IF @navKey <> ''
					MERGE dbo.admin_defaultMemberNavItem AS t USING (VALUES (@memberID, @siteID, @navKey)) AS s (memberID, siteID, navKey)
						ON s.memberID = t.memberID AND s.siteID = t.siteID
					WHEN MATCHED THEN
						UPDATE SET t.navKey = s.navKey
					WHEN NOT MATCHED THEN
						INSERT(memberID, siteID, navKey) 
						VALUES(s.memberID, s.siteID, s.navKey);
				ELSE
					DELETE FROM dbo.admin_defaultMemberNavItem
					WHERE memberID = @memberID
					AND siteID = @siteID;
			</cfquery>

			<cfset local.strReturn["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.strReturn["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="merge_showRowCreatedDate" access="private" output="no" returntype="string">
		<cfargument name="qryAllMembers" type="query" required="yes">
		<cfargument name="colLabel" type="string" required="yes">
		<cfargument name="domLabel" type="string" required="yes">
	
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.rowContent">
			<cfoutput>
			<tr>
				<td class="font-weight-bold">#arguments.colLabel#</td>
				<cfloop query="arguments.qryAllMembers">
					<td id="cell_#arguments.domLabel#_#arguments.qryAllMembers.CurrentRow#_" class="text-grey">
						 #dateformat(arguments.qryAllMembers.createddate,"mmm d yyyy")#
					</td>
				</cfloop>
			</tr>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.rowContent>
	</cffunction>

</cfcomponent>