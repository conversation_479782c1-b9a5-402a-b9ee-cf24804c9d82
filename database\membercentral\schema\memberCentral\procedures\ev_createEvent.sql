ALTER PROCEDURE dbo.ev_createEvent
@siteID int,
@calendarID int,
@eventTypeID int,
@enteredByMemberID int,
@eventSubTitle varchar(200),
@lockTimeZoneID int,
@isAllDayEvent bit,
@altRegistrationURL varchar(300),
@status char(1),
@reportCode varchar(15),
@hiddenFromCalendar bit,
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit,
@parentEventID int = NULL,
@eventID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @appCreatedContentResourceTypeID int, @defaultLanguageID int, @eventSiteResourceID int, @eventResourceTypeID int, 
		@eventcontentID int, @eventcontentSiteResourceID int, @locationContentID int, @travelcontentid int, @contactcontentid int,
		@cancellationPolicyContentID int, @informationContentID int, @defaultGLAccountID int, @calApplicationSiteResourceID int,
		@eventsResourceTypeID int, @eventfunctionid int, @inheritedRightsFunctionID int, @calApplicationInstanceID int,
		@applicationTypeID int, @reportCodeCreated bit = 0, @eventUID uniqueidentifier = NEWID(), @orgID int, @RTID int,
		@calendarName varchar(250), @msgjson varchar(max);

	set @eventID = null;
	select @eventResourceTypeID = dbo.fn_getResourceTypeId('Event');
	select @eventsResourceTypeID = dbo.fn_getResourceTypeId('Events');
	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');
	select @RTID = dbo.fn_getResourceTypeID('Community');
	select @defaultLanguageID = defaultLanguageID, @orgID = orgID from dbo.sites where siteID = @siteID;

	-- report code in the control panel ui is passed in as empty string. imports pass in the real value.
	-- if blank, create a code. 
	-- it will just be temporary since we'll immediately change it to be have the eventID
	IF NULLIF(@reportCode,'') IS NULL BEGIN
		EXEC dbo.getUniqueCode @uniqueCode=@reportCode OUTPUT;
		set @reportCodeCreated = 1;
	END

	IF EXISTS(SELECT 1 FROM dbo.ev_events where siteID = @siteID and reportCode = @reportCode and [status] in ('A','I'))
		RAISERROR('Event Code must be unique.', 16, 1);

	-- get GLAccountID from home calendar
	select @defaultGLAccountID = defaultGLAccountID
	from dbo.ev_calendars
	where siteID = @siteID
	and calendarID = @calendarID;
		IF @defaultGLAccountID is null RAISERROR('No default GL found.',16,1);

	SELECT @calendarName = ai.applicationInstanceName + CASE WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
	from dbo.ev_calendars as c
	inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID 
		and c.applicationInstanceID = ai.applicationInstanceID 
	inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
		and ai.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID
		and parentResource.siteResourceID = sr.parentSiteResourceID
		and parentResource.siteResourceStatusID = 1
	left outer join dbo.cms_siteResources as grandparentResource
		inner join dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID
			and communityInstances.siteResourceID = grandParentResource.siteResourceID
		on grandparentResource.siteID = @siteID
		and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
		and grandparentResource.siteResourceStatusID = 1
		and grandparentResource.resourceTypeID = @RTID
	WHERE c.siteID = @siteID
	AND c.calendarID = @calendarID;

	BEGIN TRAN
		exec dbo.cms_createSiteResource @resourceTypeID=@eventResourceTypeID, @siteResourceStatusID=1, 
			@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@eventSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@eventcontentID OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@eventcontentID OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID, 
			@contentID=@locationContentID OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@travelcontentid OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@contactcontentid OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@cancellationPolicyContentID OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
			@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=@enteredByMemberID,
			@contentID=@informationContentID OUTPUT, @siteResourceID=@eventcontentSiteResourceID OUTPUT;

		-- add event
		insert into dbo.ev_events (
			eventTypeID, siteID, siteResourceID, eventContentID, locationContentID, travelContentId, 
			contactContentID, cancellationPolicyContentID, enteredByMemberID, lockTimeZoneID, isAllDayEvent, 
			altRegistrationURL, GLAccountID, [status], reportCode, informationContentID, emailContactContent, 
			emailLocationContent, emailCancelContent, emailTravelContent, hiddenFromCalendar, eventSubTitle, [uid])
		values (@eventTypeID, @siteID, @eventSiteResourceID, @eventContentID, @locationContentID, @travelContentId, 
			@contactContentID, @cancellationPolicyContentID, @enteredByMemberID, @lockTimeZoneID, @isAllDayEvent, 
			@altRegistrationURL, @defaultGLAccountID, @status, @reportCode, @informationContentID, @emailContactContent, 
			@emailLocationContent, @emailCancelContent, @emailTravelContent, @hiddenFromCalendar, nullif(@eventSubTitle,''),
			@eventUID);
		select @eventID = SCOPE_IDENTITY();

		-- update report code if we just created a random one
		IF @reportCodeCreated = 1
			UPDATE dbo.ev_events
			SET reportCode = 'EVC' + cast(@eventID as varchar(10))
			where eventID = @eventID;

		-- add parent - children relationship
		if @parentEventID is not null and @parentEventID > 0
			insert into ev_subEvents (parentEventID, eventID)
			values(@parentEventID, @eventID);

		-- add permissions for event management
		select @calApplicationSiteResourceID = ai.siteResourceID, @calApplicationInstanceID = c.applicationInstanceID
		FROM dbo.ev_calendars AS c 
		INNER JOIN dbo.cms_applicationInstances AS ai ON c.applicationInstanceID = ai.applicationInstanceID
		WHERE c.siteID = @siteID
		AND c.calendarID = @calendarID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditEvent',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditEventByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('DeleteEvent',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('DeleteEventByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ViewRegistrants',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ViewRegistrantsByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('EditRegistrants',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('EditRegistrantsByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ViewRealTimeRoster',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ViewRealTimeRosterByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManageFreeRates',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManageFreeRatesByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		SELECT @eventfunctionid = dbo.fn_getResourceFunctionID('ManagePaidRates',@eventResourceTypeID);
		SELECT @inheritedRightsFunctionID = dbo.fn_getResourceFunctionID('ManagePaidRatesByDefault',@eventsResourceTypeID);
		EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@eventSiteResourceID, @functionIDList=@eventfunctionid, @inheritedRightsResourceID=@calApplicationSiteResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;

		-- add to its main calendar
		EXEC dbo.ev_createCalendarEvent @calendarID=@calendarID, @sourceCalendarID=@calendarID, @sourceCategoryID=null, @sourceEventID=@eventID, @ovCategoryID=null;

		-- audit log for event creation
		DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('New Event [' + CASE WHEN @reportCodeCreated = 1 THEN 'EVC' + cast(@eventID as varchar(10)) ELSE @reportCode END + '] has been created under the calendar [' + @calendarName + '].', 'json');
		EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@enteredByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
