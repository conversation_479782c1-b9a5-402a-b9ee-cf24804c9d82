use platformMail
GO

ALTER PROC dbo.email_checkEmailSendingQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @fifteenMinutesInTheFuture datetime = dateadd(minute,15,getdate()), @insertingStatusID int, @cancelledStatusID int, 
		@queuedStatusID int, @requeuedStatusID int, @pendingStatusID int, @releasedStatusID int, @processingStatusID int, 
		@scheduledStatusID int, @erroredStatusID int, @scheduledJobsFound bit=0, @futureDatedQueuedMessagesFound bit = 0, 
		@issueCount int, @tier varchar(12), @defaultMarketingSubUserID int, @defaultTransactionalSubUserID int, @environmentID int,
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @errorContent varchar(max), @checkDate datetime, 
		@actionNeeded bit = 0, @numFound int = 0, @anyCancelled bit = 0, @statusID int, @now datetime,  @errmsg varchar(max);
	declare @recipientsToChange TABLE (recipientID int PRIMARY KEY, messageID int);
	declare @recipientsToConsider TABLE (recipientID int PRIMARY KEY, messageID int);

	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	select @insertingStatusID = statusID from dbo.email_statuses where statusCode = 'I';
	select @cancelledStatusID = statusID from dbo.email_statuses where statusCode = 'C';
	select @processingStatusID = statusID from dbo.email_statuses where statusCode = 'G';
	select @pendingStatusID = statusID from dbo.email_statuses where statusCode = 'P';
	select @queuedStatusID = statusID from dbo.email_statuses where statusCode = 'Q';
	SELECT @requeuedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'R';
	SELECT @erroredStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'E';
	select @scheduledStatusID = statusID from dbo.email_statuses where statusCode = 'scheduled';
	SELECT @releasedStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'released';

	-- check for items marked as Queued where message send date is more than 10 minutes in the future
	-- hardcoded @queuedStatusID as 2 to use filtered index
	select top 1 @futureDatedQueuedMessagesFound = m.messageID
	from dbo.email_messageRecipientHistory as mrh
	inner join dbo.email_messages as m 
		on mrh.emailStatusID = 2
		and mrh.siteID = m.siteID
		and m.status = 'A'
		and m.sendOnDate > @fifteenMinutesInTheFuture
		and m.messageID = mrh.messageID;

	-- check for scheduled jobs that are now due within the next 10 minutes, mark then as Queued if found
	-- hardcoded @scheduledStatusID as 18 to use filtered index
	select top 1 @scheduledJobsFound = mrh.recipientID
	from dbo.email_messageRecipientHistory as mrh
	inner join dbo.email_messages as m 
		on mrh.emailStatusID = 18
		and mrh.siteID = m.siteID
		and mrh.batchID is null
		and m.status = 'A'
		and m.messageID = mrh.messageID
		and m.sendOnDate < @fifteenMinutesInTheFuture;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF @futureDatedQueuedMessagesFound = 1
		-- hardcoded @queueStatusID as 2 to use filtered index
		update mrh 
		set mrh.emailStatusID = @scheduledStatusID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 2
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate > @fifteenMinutesInTheFuture
			and m.messageID = mrh.messageID;

	if @scheduledJobsFound = 1
		-- hardcoded @scheduledStatusID as 18 to use filtered index
		update mrh 
		set mrh.emailStatusID = @queuedStatusID
		from dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 18
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.sendOnDate < @fifteenMinutesInTheFuture
			and m.messageID = mrh.messageID;
	

	-- get all recipients not in the emailExtMergeCode queue or the eventCertificate queue
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
		declare @recipientsToCancel TABLE (recipientID int PRIMARY KEY);
		DECLARE @EMCqueueTypeID int, @EMCstatusReady int, @EMCstatusGrabbed int, @EMCrecipientIDDataColumnID int;
		EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@EMCqueueTypeID OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@EMCqueueTypeID, @queueStatus='readyToProcess', @queueStatusID=@EMCstatusReady OUTPUT;
		EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@EMCqueueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@EMCstatusGrabbed OUTPUT;
		select @EMCrecipientIDDataColumnID = columnID from platformQueue.dbo.tblQueueTypeDataColumns where queueTypeID = @EMCqueueTypeID and columnName = 'MCRecipientID';
		SET @checkDate = dateadd(minute,-30,getdate());
		SET @numFound = 0;
		SET @anyCancelled = 0;

		-- hardcoded @insertingStatusID as 1 for inserting to use filtered index 
		INSERT INTO @recipientsToConsider (recipientID, messageID)  
		SELECT recipientID, messageID  
		FROM dbo.email_messageRecipientHistory
		WHERE emailStatusID = 1  
		AND dateLastUpdated < @checkDate;

		insert into @recipientsToCancel (recipientID)
		SELECT recipientID
		FROM @recipientsToConsider
			except
		SELECT recipientID
		FROM (
			SELECT qid.columnValueInteger as recipientID
			FROM platformQueue.dbo.tblQueueItems as qi2
			inner join platformQueue.dbo.tblQueueItemData as qid on qi2.itemUID = qid.itemUID 
				AND qid.columnID = @EMCrecipientIDDataColumnID
			WHERE qi2.queueStatusID in (@EMCstatusReady, @EMCstatusGrabbed)
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_eventCertificate
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_swodCertificate
				UNION
			SELECT recipientID
			FROM platformQueue.dbo.queue_swlCertificate
		) as tmp;

		SET @numFound = @@ROWCOUNT;
		IF @numFound > 0
			SET @anyCancelled = 1;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @anyCancelled = 1 BEGIN
			update mrh WITH (UPDLOCK, HOLDLOCK) 
			SET mrh.emailStatusID = @cancelledStatusID
			FROM @recipientsToCancel rc 
			inner join dbo.email_messageRecipientHistory mrh on mrh.recipientID = rc.recipientID;

			IF @tier = 'Production' BEGIN
				SET @errorSubject = 'Investigation Needed: Email Sending queue had potential problem Inserting Recipients. The recipients have been cancelled.';
				SET @errmsg = 'There were recipients marked as Inserting that hadn''t been updated for 30 minutes. They have been cancelled.';
				EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items marked as Queued WHERE datelastupdated older than 4 hours 
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		SET @checkDate = dateadd(hour,-4,getdate());

		-- hardcoded @queuedStatusID as 2 to use filtered index
		SELECT @numFound=count(*)
		FROM dbo.email_messageRecipientHistory as mrh
		inner join dbo.email_messages as m 
			on mrh.emailStatusID = 2
			and mrh.siteID = m.siteID
			and m.status = 'A'
			AND m.sendOnDate < @checkDate
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate
		group by mrh.siteID, mrh.messageID;
		
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 and @tier = 'Production' BEGIN
			SET @errorSubject = 'Investigation Needed: Email Sending queue has old recipients marked as queued.';
			SET @errmsg = 'There are recipients marked as Queued that haven''t been updated in at least 4 hours. Developer needs to make sure scheduled task is running AND not failing.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items marked as Requeue with datelastupdated more than 5 mins ago
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-5,@now);

		DECLARE @recipientsToUpdate TABLE (recipientID int PRIMARY KEY)

		-- hardcoded @requeuedStatusID as 7 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 7
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 7
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	--  items marked as Errored WHERE datelastupdated older than 15 minutes
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SET @checkDate = dateadd(minute,-15,getdate());

			-- hardcoded @erroredStatusID as 5 to use filtered index
			SELECT @numFound=count(*)
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 5 
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			IF @numFound > 0 BEGIN
				SET @errorSubject = 'Investigation Needed: Email Sending queue has errors sending to recipients.';
				SET @errmsg = 'There are recipients marked as Errored that haven''t been updated in at least 15 minutes. These must be resolved by a developer.';
				EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
			END
			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
		END CATCH
	END


	--  items marked as Processing WHERE datelastupdated older than 15 minutes. Auto cancel.
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @recipientsToUpdate1 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-15,getdate());
		SET @anyCancelled = 0;
		
		-- hardcoded @pendingStatusID as 3 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 3 
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			and mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate1 (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 3
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @cancelledStatusID
			FROM @recipientsToUpdate1 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

			IF @@ROWCOUNT > 0
				SET @anyCancelled = 1;
		END

		IF @anyCancelled > 0 AND @tier = 'Production' BEGIN
			SET @errorSubject = 'Investigation Needed: Email Sending queue had old recipients marked as processing. The recipients have been cancelled.';
			SET @errmsg = 'There were recipients marked as Processing that hadn''t been updated for 15 minutes. They have been cancelled.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH



	-- cancel recipients marked as Queued WHERE message has been deleted
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @deletedMessageRecipientsToCancel TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @anyCancelled = 0;

		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m on mrh.siteID = m.siteID
			and m.status = 'D'
			and m.messageID = mrh.messageID
		WHERE mrh.emailStatusID in (@insertingStatusID,	@queuedStatusID,@requeuedStatusID,@erroredStatusID,@scheduledStatusID);

		IF @numFound > 0
			insert into @deletedMessageRecipientsToCancel (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m on mrh.siteID = m.siteID
				and m.status = 'D'
				and m.messageID = mrh.messageID
			WHERE mrh.emailStatusID in (@insertingStatusID,	@queuedStatusID,@requeuedStatusID,@erroredStatusID,@scheduledStatusID);

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0 BEGIN
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @cancelledStatusID
			FROM @deletedMessageRecipientsToCancel temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

			IF @@ROWCOUNT > 0
				SET @anyCancelled = 1;
		END

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	-- items grabbedForProcessing with datelastupdated more than 10 mins ago
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
		DECLARE @recipientsToUpdate2 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();
		SET @checkDate = dateadd(minute,-10,@now);
		-- hardcoded @processingStatusID as 6 to use filtered index
		SELECT @numFound = count(mrh.recipientID)
		FROM dbo.email_messageRecipientHistory mrh
		inner join dbo.email_messages m 
			on mrh.emailStatusID = 6
			and mrh.siteID = m.siteID
			and m.status = 'A'
			and m.messageID = mrh.messageID
			AND mrh.dateLastUpdated < @checkDate;

		IF @numFound > 0
			insert into @recipientsToUpdate2 (recipientID)
			SELECT mrh.recipientID
			FROM dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messages m 
				on mrh.emailStatusID = 6
				and mrh.siteID = m.siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				AND mrh.dateLastUpdated < @checkDate;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate2 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHandler @raise=0, @email=1;
	END CATCH


	--  items sent in the last 5 minutes before a container was killed 
	BEGIN TRY
		SET XACT_ABORT OFF;
		
		DECLARE @lastHeartbeatDateStart datetime, @lastHeartbeatDateEnd datetime;
		DECLARE @recentBatchesofKilledTaskWorkers TABLE (batchIdentifier varchar(50) PRIMARY KEY, dockerContainerID varchar(50), dateLastCheckin datetime, recipientCutoffDate datetime);
		DECLARE @recipientsToUpdate3 TABLE (recipientID int PRIMARY KEY);

		SET @now = getdate();
		SET @lastHeartbeatDateStart = dateadd(minute,-45,@now);
		SET @lastHeartbeatDateEnd = dateadd(minute,-10,@now);

		update sth 
		SET sth.batchIdentifier = NULL
		FROM platformStatsMC.dbo.scheduledTaskRunnerHeartbeats strh
		inner join platformStatsMC.dbo.scheduledTaskHistory sth on sth.dockerContainerID = strh.dockerContainerID
			AND sth.batchIdentifier is not null 
			AND sth.batchIdentifier = ''
		WHERE strh.dateLastCheckin between @lastHeartbeatDateStart AND @lastHeartbeatDateEnd;			

		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		insert into @recentBatchesofKilledTaskWorkers (batchIdentifier,dockerContainerID, dateLastCheckin,recipientCutoffDate)
		SELECT sth.batchIdentifier, strh.dockerContainerID, strh.dateLastCheckin, dateadd(minute,-5,strh.dateLastCheckin)
		FROM platformStatsMC.dbo.scheduledTaskRunnerHeartbeats strh
		inner join platformStatsMC.dbo.scheduledTaskHistory sth on sth.dockerContainerID = strh.dockerContainerID
			AND sth.batchIdentifier is not null
		where strh.dateLastCheckin between @lastHeartbeatDateStart AND @lastHeartbeatDateEnd;
			
		if exists(SELECT * FROM @recentBatchesofKilledTaskWorkers) BEGIN
			SELECT @numFound = count(mrh.recipientID)
			FROM @recentBatchesofKilledTaskWorkers kw
			inner join dbo.email_messageRecipientHistory mrh on kw.batchIdentifier = mrh.batchID
				AND mrh.emailStatusID = @releasedStatusID
				AND mrh.dateLastUpdated > kw.recipientCutoffDate;

			IF @numFound > 0
				insert into @recipientsToUpdate3 (recipientID)
				SELECT mrh.recipientID
				FROM @recentBatchesofKilledTaskWorkers kw
				inner join dbo.email_messageRecipientHistory mrh on kw.batchIdentifier = mrh.batchID
					AND mrh.emailStatusID = @releasedStatusID
					AND mrh.dateLastUpdated > kw.recipientCutoffDate;
		END

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @queuedStatusID
			FROM @recipientsToUpdate3 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
	END CATCH


	-- Detect Emails in Unconfirmed Status in the last 2 hours and Requeue them
	BEGIN TRY
		SET XACT_ABORT OFF;
		SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

		DECLARE @oldestbatchstartdateWithAwaitingValidation datetime;
		DECLARE @2hoursago datetime = dateadd(hour,-2,getdate());
		DECLARE @recipientsToUpdate4 TABLE (recipientID int PRIMARY KEY);
		SET @now = getdate();

		select @oldestbatchstartdateWithAwaitingValidation=min(batchstartdate)
		from dbo.email_messageRecipientHistory
		where emailStatusID = 16 
		and batchstartdate < @2hoursago;

		-- push it 15 mins earlier
		set @oldestbatchstartdateWithAwaitingValidation = dateadd(minute,-15,@oldestbatchstartdateWithAwaitingValidation);

		IF OBJECT_ID('tempdb..#tmpallrecipients') is not null
			drop table #tmpallrecipients;

		select recipientID, messageID, siteID, batchStartDate, sendingWindow=DATEDIFF(minute,'1/1/2023',batchStartDate)/15, 
			confirmed = case when emailStatusID = 16 then 0 when emailStatusID in (9,10,11,12,13,14,15,17,19) then 1 end
		into #tmpallrecipients
		from dbo.email_messageRecipientHistory
		where batchStartDate between @oldestbatchstartdateWithAwaitingValidation and @2hoursago
		and emailstatusID in (16, 9,10,11,12,13,14,15,17,19);

		-- items to requeue (set emailStatus = requeued, batchStartDate = null, batchID = null, dateLastUpdated = getdate())
		SELECT @numFound = count(r.recipientID)
		from #tmpallrecipients r
		inner join (
			select sendingWindow
			from #tmpallrecipients
			group by sendingWindow
			having cast(sum(confirmed) as decimal(7,2)) /count(*) > .95
		) reQ on reQ.sendingWindow = r.sendingWindow and r.confirmed=0;

		IF @numFound > 0
			insert into @recipientsToUpdate4 (recipientID)
			SELECT r.recipientID
			from #tmpallrecipients r
			inner join (
				select sendingWindow
				from #tmpallrecipients
				group by sendingWindow
				having cast(sum(confirmed) as decimal(7,2)) /count(*) > .95
			) reQ on reQ.sendingWindow = r.sendingWindow and r.confirmed=0;

		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		IF @numFound > 0
			update erh WITH (UPDLOCK, HOLDLOCK)
			SET batchID = null,
				batchStartDate = null,
				dateLastUpdated = @now,
				emailStatusID = @requeuedStatusID
			FROM @recipientsToUpdate4 temp
			inner join dbo.email_messageRecipientHistory erh on erh.recipientID = temp.recipientID;

		SET XACT_ABORT ON;
	END TRY
	BEGIN CATCH
		SET XACT_ABORT ON;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
	END CATCH


	-- Trigger prioritizeQueue if Unprioritied Emails  are found or if queue is more than 30 mins behind
	IF @tier = 'Production' BEGIN
		BEGIN TRY
			SET XACT_ABORT OFF;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @problemMessageTypes varchar(100);
			set @actionNeeded = 0;
			set @checkDate = dateadd(hour,-30,getdate());

			IF EXISTS (select 1 from dbo.email_messageRecipientHistory where emailStatusID in (1,2,18) and queuePriority is null) BEGIN
				set @actionNeeded = 1;

				select @problemMessageTypes = STRING_AGG(messageTypeCode,', ') WITHIN GROUP (ORDER BY messageTypeCode ASC)
				from (
					select distinct mt.messageTypeCode
					from dbo.email_messageRecipientHistory mrh 
					inner join dbo.email_messages m on mrh.siteID = m.siteID
						and m.messageID = mrh.messageID
					inner join dbo.email_messageTypes mt on mt.messageTypeID = m.messageTypeID
					where mrh.emailStatusID in (1,2,18) 
					and mrh.queuePriority is null
				) as tmp;

				SET @errorTitle = ' Email Sending Queue - Unprioritied Queued or Scheduled Emails (Codes: ' + @problemMessageTypes + ')';
				SET @errorSubject = 'Investigation Needed: Email Sending queue had Unprioritied Queued or Scheduled Emails (Codes: ' + @problemMessageTypes + ')';
				SET @errorContent = 'Email Sending queue had Unprioritied Queued or Scheduled Emails . The recipients have now been prioritized. Please track down the code problem -- all inserts to this table need to set the priority using fn_getInitialRecipientQueuePriority(). The corrected messages had type code(s): ' + @problemMessageTypes 
				EXEC platformQueue.dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
			END

			-- hardcoded queueStatusID to use filtered index
			-- Loading Queue StatusID       : 1
			-- Processing (queued) StatusID : 2
			IF EXISTS (select 1 from dbo.email_messageRecipientHistory where emailStatusID in (1,2) and dateLastUpdated < @checkDate)
				set @actionNeeded = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			IF @actionNeeded=1
				exec dbo.email_prioritizeQueue;

			SET XACT_ABORT ON;
		END TRY
		BEGIN CATCH
			SET XACT_ABORT ON;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			EXEC membercentral.dbo.up_MCErrorHANDler @raise=0, @email=1;
		END CATCH
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
