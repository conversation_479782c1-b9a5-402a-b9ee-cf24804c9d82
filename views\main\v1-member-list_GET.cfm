<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/list</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li><i>search</i> is a structure of subkeys used to limit the lists returned. Eligible subkeys are:<br/>
			<i>email</i> - e-mail address of the list membership<br/>
			<i>listname</i> - list name<br/>
			<i>name</i> - member's name on the list<br/>
			<i>lockname</i> - 1 or 0, if the Lock Name flag is set for this list membership<br/>
			<i>lockaddress</i> - 1 or 0, if the Lock Address flag is set for this list membership<br/>
			<i>keepactive</i> - 1 or 0, if the Keep Active flag is set for this list membership<br/>
			<i>membertype</i> - member type of the list membership. Valid values are: normal, confirm, private, expired, held, unsub, referred, needs-confirm, needs-hello, needs-goodbye<br/>
			<i>subtype</i> - subscription type of the list membership. Valid values are: mail, digest, mimedigest, index, nomail<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each list membership included in the response will have the same data structure as the GET /v1/member/{membernumber}/list/{listname} response.
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/list HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/list HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 157

{
    "count": 5,
    "start": 10,
    "search": {
        "membertype": "normal",
        "subtype": "mail"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":5,
        "list": [
            {
                "listname": "samplelist",
                "membertype": "normal",
                "subtype": "mail",
                "name": "Jane Doe, Esq",
                "email": "<EMAIL>",
                "lockname": 1,
                "lockaddress": 0,
                "keepactive": 0,
                "api_id": 0000000,
                "x-api-uri": "/v1/member/SAMPLE123456/list/samplelist/0000000"
            }
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>