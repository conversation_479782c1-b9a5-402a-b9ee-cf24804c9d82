<cfinclude template="dsp_manageSubs_commonJS.cfm">

<cfoutput>
<div class="manageSubsApp p-2">
	<!--- Member Card --->
	<div class="mb-3">
		<div>
			<h5 class="m-0">#local.strMember.mc_combinedName# (#local.strMember.memberNumber#)</h5>
			<cfif len(local.strMember.company)><div class="text-dim"><small>#local.strMember.company#</small></div></cfif>
			<cfif len(local.strMember.mc_combinedAddresses) or len(local.strMember.mc_extraInfo) or len(local.strMember.mc_recordType) or len(local.strMember.mc_memberType) or len(local.strMember.mc_lastlogin)>
				<div class="mt-1 p-1">
					<cfif len(local.strMember.mc_combinedAddresses)>#local.strMember.mc_combinedAddresses#</cfif>
					<cfif len(local.strMember.mc_extraInfo)>#local.strMember.mc_extraInfo#</cfif>
					<cfif len(local.strMember.mc_recordType)><div>#local.strMember.mc_recordType#</div></cfif>
					<cfif len(local.strMember.mc_memberType)><div>#local.strMember.mc_memberType#</div></cfif>
					<cfif len(local.strMember.mc_lastlogin)><div>#local.strMember.mc_lastlogin#</div></cfif>
				</div>
			</cfif>
		</div>

		<div id="subRegCartStepOneTotal" class="form-row mt-3">
			<div class="col">
				<div id="subRegCartTotal" class="font-weight-bold mb-2">
					Total: <span class="grandTotal">$0.00</span>
				</div>
				<div class="subCouponAppliedContainer bg-light p-3 d-none">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<div class="font-size-lg font-weight-bold align-self-baseline">Total:</div>
						<div class="text-right">
							<div class="font-size-lg font-weight-bold grandTotal"></div>
							<small class="text-dim"><del class="actualSubTotal"></del></small><br>
							<small class="text-success"><span class="totalSubDiscount"></span> discount applied</small>
						</div>
					</div>
					<div class="d-flex justify-content-between align-items-center mt-2">
						<div class="alert alert-success mb-0 py-1 px-3 subCouponRedeemDetail"></div>
						<button type="button" name="btnRemoveCoupon" class="btn btn-xs btn-warning py-2 btnRemoveCoupon" onclick="removeAppliedCoupon();">Remove Promo Code</button>
					</div>
				</div>
			</div>
			<div class="subCouponContainer col-auto ml-auto text-right align-self-end d-none">
				<div class="couponCodeResponse alert alert-danger p-1 mb-1 font-size-sm text-center" style="display:none;"></div>
				<div class="input-group input-group-sm">
					<input type="text" name="couponCode" value="" class="form-control form-control-sm couponCode" maxlength="15" placeholder="Promo Code">
					<div class="input-group-append">
						<button type="button" name="btnApplyCouponCode" class="btn btn-sm input-group-text btnApplyCouponCode" onclick="validateCouponCode();">Apply</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<form name="frmManageSubs" id="frmManageSubs" action="#local.formlink#" method="post" autocomplete="off" onsubmit="return onSubmitFinalForm();">
		<input type="hidden" name="mid" value="#local.memberID#">
		<input type="hidden" name="rootSubscriptionID" value="#local.rootSubscriptionID#">
		<input type="hidden" id="formStep" name="formStep" value="1">
		<input type="hidden" id="payScheduleRowIDsList" name="payScheduleRowIDsList" value="">
		<input type="hidden" id="currAction" name="currAction" value="S">
		<input type="hidden" id="skipEmailTemplateNotifications" name="skipEmailTemplateNotifications" value="0">
		<input type="hidden" id="chkBilled" name="chkBilled" value="0">
		<input type="hidden" id="fOfferExpDate" name="fOfferExpDate" value="#dateformat(local.offerExpirationDate,'m/d/yyyy')#">

		<div id="manageSubsContainer">
			<!--- Root Sub Card --->
			<div id="rootSubWrapper" class="card card-box mb-1 mainSubCard" 
				data-displaypriceelement="rootSubCardTotalPrice"
				data-linkedsummarycontainer="rootSubSummary"
				data-linkedformcontainer="rootSubForm"
				data-linkedtitlecontainer="rootSubTitleContainer"
				data-linkedtitleerrorcontainer="rootSubTitleAlert"
				data-errfields="">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">
						#local.strRootSub.subscription[1].subscriptionName# <span class="text-danger">*</span>
						<span class="rootSubCardTotalPrice"></span>
					</div>
				</div>
				<div class="card-body py-3">
					<div id="rootSubForm" class="d-none">
						<div class="subsContainer">
							<cfif local.strRootSub.subscription[1].qryRates.recordCount gt 0>
								#manageSubscription_renderSubscriptionsForm(arrSubs=local.strRootSub.subscription, strAddOn={}, parentSubscriptionID=0, strParentFreq=local.strRootSub.strParentFreq, 
									freeRateDisplay=local.freeRateDisplay, strEditSubs=local.strRootSub.strEditSub, recursionLevel=0, listSubscribed="")#
							<cfelse>
								<div class="alert alert-warning mb-1">There are no rates available for this member.<br>Select a different subscription.</div>
							</cfif>
						</div>
						<button type="button" id="rootSubConfirmBtn" class="btn btn-sm btn-primary mt-3 d-none" onclick="confirmRootSubChanges();">Confirm</button>
					</div>
					<div id="rootSubSummary" class="subCardSummary">
						<div id="rootSubRateInfo"></div>
						<cfif local.hasRootSubRatesCount gt 1>
							<button type="button" id="rootSubEditBtn" data-editmode="editrootsub" class="btn btn-sm btn-secondary subCardEditBtn mt-3 d-none">Cancel</button>
						</cfif>
					</div>
				</div>
			</div>

			<!--- Sub Term Dates Card --->
			<div id="rootSubTermDates" class="card card-box mb-1 mainSubCard d-none" 
				data-displaypriceelement=""
				data-linkedsummarycontainer="subTermDatesSummary"
				data-linkedformcontainer="subTermDatesForm"
				data-linkedtitlecontainer=""
				data-linkedtitleerrorcontainer=""
				data-errfields="">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">Term Dates</div>
				</div>
				<div class="card-body py-3">
					<div id="subTermDatesForm" class="d-none">
						<div class="termDatesContainer mb-3">
							<cfif len(local.prevTermDateString) gt 0>
								<div class="text-grey mb-2">
									Previous Subscription: #local.prevTermDateString#
								</div>
							</cfif>
							<table id="tblCurrentSubTermDates" class="table table-sm table-borderless">
								<thead>
									<tr>
										<th></th>
										<th>Start Date</th>
										<th>End Date</th>
										<th>Grace End Date</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td style="width:20%;">Subscription Term:</td>
										<td>
											<div class="input-group input-group-sm">
												<input type="text" name="fTermFrom" id="fTermFrom" value="" class="form-control form-control-sm dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermFrom"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</td>
										<td>
											<div class="input-group input-group-sm">
												<input type="text" name="fTermTo" id="fTermTo" value="" class="form-control form-control-sm dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermTo"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</td>
										<td>
											<div class="d-flex">
												<div class="col px-1">
													<div class="input-group input-group-sm">
														<input type="text" name="fTermGrace" id="fTermGrace" value="" class="form-control form-control-sm dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fTermGrace"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="flex-shrink-1 d-flex align-items-center">
													<a href="javascript:mca_clearDateRangeField('fTermGrace');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Clear Grace End Date"><i class="fa-regular fa-calendar-minus text-danger font-size-lg"></i></a>
												</div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
							<cfif local.useAccrualAcct>
								<table id="tblRecogDates" class="table table-sm table-borderless mt-2">
									<thead>
										<tr>
											<th style="width:45%;"></th>
											<th style="width:26%;">Recognition Start Date</th>
											<th style="width:26%;">Recognition End Date</th>
											<th style="width:3%;"></th>
										</tr>
									</thead>
									<tbody>
									</tbody>
								</table>
							</cfif>
						</div>
						<div class="d-flex no-gutters align-items-center termDatesFooterContainer mt-3 border-lightgray border-top pt-2">
							<div class="col-auto">
								<button type="button" class="btn btn-sm btn-primary subCardConfirmBtn" onclick="confirmTermDatesChanges();return false;">
									Confirm
								</button>
							</div>
							<div class="col-auto pl-2">
								<button type="button" name="btnCancelTermDatesChanges" class="btn btn-sm btn-secondary subCardCancelBtn" onclick="cancelTermDatesForm();">Cancel</button>
							</div>
						</div>
					</div>
					<div id="subTermDatesSummary" class="subCardSummary">
						<div>Subscription Term: <span id="termStart"></span> - <span id="termEnd"></span></div>
						<cfif len(local.prevTermDateString) gt 0>
							<div class="text-grey my-2">
								Previous Subscription: #local.prevTermDateString#
							</div>
						</cfif>
						<div class="mt-3">
							<button type="button" id="termDatesEditBtn" data-editmode="edittermdates" class="btn btn-sm btn-secondary subCardEditBtn d-none"></button>
						</div>
					</div>
				</div>
			</div>

			<!--- Sub AddOns --->
			<div id="sub#local.rootSubscriptionID#_addons"></div>

			<!--- Sample Card used by JS for loading --->
			<div id="subLoadingCard" class="d-none">
				<div class="mx-3 my-2 d-flex align-items-center">
					<i class="spinner-border spinner-border-sm"></i><span class="font-weight-bold ml-2">Please Wait...</span>
				</div>
			</div>
		</div>
		<div id="subsSummaryContainer" class="d-none">
			<div class="subCouponContainer mb-2 d-none">
				<div class="d-flex">
					<div class="col-auto ml-auto text-right align-self-end">
						<div class="couponCodeResponse alert alert-danger p-1 mb-1 font-size-sm text-center" style="display:none;"></div>
						<div class="input-group input-group-sm">
							<input type="text" name="couponCode" value="" class="form-control form-control-sm couponCode" maxlength="15" placeholder="Promo Code">
							<div class="input-group-append">
								<button type="button" name="btnApplyCouponCode" class="btn btn-sm input-group-text btnApplyCouponCode" onclick="validateCouponCode();">Apply</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="subCouponAppliedContainer mb-2 text-right">
				<button type="button" name="btnRemoveCoupon" class="btn btn-xs btn-warning py-2 btnRemoveCoupon" onclick="removeAppliedCoupon();">Remove Promo Code</button>
			</div>
			<div id="subSummaryCard" class="card card-box mb-2">
				<div class="card-body py-3">
					<div id="multipleInvProfilesAlert" class="alert alert-info mb-2 d-none">
						This subscription includes multiple Invoice Profiles and will create multiple invoices when finalized.
					</div>
					<div id="subSummaryTable" class="subSummarySection d-none">
						<div class="mb-2"><span class="font-weight-bold">#local.strRootSub.subscription[1].subscriptionName#</span> <span id="termDatesDisp"></span></div>
						<div id="rootSubRateSelection" class="pl-3">
						</div>
						<div id="addOnSubsSelection" class="pl-3">
						</div>
						<div class="mb-2 row no-gutters mt-3">
							<div class="col-7 font-weight-bold">Total Due:</div>
							<div id="totalDueDisplay" class="col text-right font-weight-bold px-3"></div>
							<div class="freqDisplayElm"></div>
						</div>
						<div id="subCouponSummary" class="subCouponAppliedContainer d-none">
							<div class="row no-gutters">
								<div class="col text-right font-size-sm px-3"><del class="actualSubTotal"></del></div>
								<div class="freqDisplayElm"></div>
							</div>
							<div class="row no-gutters">
								<div class="col text-right font-size-sm px-3"><span class="totalSubDiscount"></span> discount applied</div>
								<div class="freqDisplayElm"></div>
							</div>
							<div class="alert alert-success font-size-sm mt-2 mb-0 px-3 py-1 subCouponRedeemDetail"></div>
							<div class="alert alert-warning font-size-sm mt-2 mb-0 px-3 py-1">This coupon will <b>not</b> be applied if you choose to <b>Save and Keep as Billed</b>. In order to apply the coupon one of the <b>Finalize and Invoice</b> options must be selected.</div>
						</div>
						<div class="mb-2 row no-gutters mt-2">
							<div class="col-7 font-weight-bold">Amount Due Today:</div>
							<div id="amtDueTodayDisplay" class="col text-right font-weight-bold px-3"></div>
							<div class="freqDisplayElm"></div>
						</div>
						<div id="changePriceFormActions" class="d-none mt-4">
							<button type="button" class="btn btn-sm btn-primary" onclick="confirmPriceChange();">Confirm Price Change</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelPriceChange();">Cancel</button>
						</div>
					</div>
					<div id="paymentScheduleForm" class="subSummarySection d-none">
						<div id="paymentScheduleListing">
							Payment Schedule
						</div>
						<div id="err_schedform" class="alert alert-danger mb-2 d-none"></div>
						<div id="paymentScheduleFormActions" class="d-none mt-4">
							<button type="button" class="btn btn-sm btn-primary" onclick="confirmPaymentSchedule();">Confirm Payment Schedule</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelPaymentSchedule();">Cancel</button>
						</div>
					</div>
					<div id="summaryStepActions" class="mt-4">
						<button type="button" class="btn btn-sm btn-secondary" onclick="changePrice();">Change Price</button>
						<button type="button" class="btn btn-sm btn-secondary" onclick="setPaymentSchedule();">Set Payment Schedule</button>
						<button type="button" class="btn btn-sm btn-secondary" onclick="editStepOne();">Go Back &amp; Edit</button>
						<span id="payScheduleResetMsg" style="display:none;"></span>
					</div>
				</div>
			</div>

			<cfset local.stateIDForTax = val(local.qryTaxStateZIP.stateIDForTax)>
			<cfset local.zipForTax = local.qryTaxStateZIP.zipForTax>
			<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
				<cfset local.zipForTax = "">
			</cfif>

			<!--- prompt for missing tax information --->
			<cfif local.stateIDForTax eq 0 OR len(local.zipForTax) eq 0>
				<div class="row no-gutters my-2">
					<div class="col-md-12">
						<div class="card card-box mb-1">
							<div class="card-header bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Additional Billing Information Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="form-group row no-gutters">
									<label for="stateIDforTax" class="col-sm-3 col-form-label-sm font-size-md">Billing State/Province: *</label>
									<div class="col-sm-9">
										<cfset local.qryStates = application.objCommon.getStates()>
										<select id="stateIDforTax" name="stateIDforTax" class="form-control form-control-sm">
											<option value=""></option>
											<cfset local.currentCountryID = 0>
											<cfloop query="local.qryStates">
												<cfif local.qryStates.countryID neq local.currentCountryID>
													<cfset local.currentCountryID = local.qryStates.countryID>
													<cfoutput><optgroup label="#local.qryStates.country#"></cfoutput>
												</cfif>
												<cfoutput><option value="#local.qryStates.stateID#"<cfif local.stateIDForTax eq local.qryStates.stateID> selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option></cfoutput>
												<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
													</optgroup>
												</cfif>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row no-gutters">
									<label for="zipForTax" class="col-sm-3 col-form-label-sm font-size-md">Billing Postal Code: *</label>
									<div class="col-sm-9">
										<cfoutput><input type="text" id="zipForTax" name="zipForTax" class="form-control form-control-sm" maxlength="25" value="#local.zipForTax#"></cfoutput>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			<cfelse>
				<cfoutput>
				<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.qryTaxStateZIP.stateIDforTax#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#local.zipForTax#">
				</cfoutput>
			</cfif>
			<div id="subSaveCard" class="card card-box mb-1 bg-light">
				<div class="card-body py-3">
					<h6 class="font-weight-bold">Save Subscription</h6>
					<div id="err_finalsubform" class="alert alert-danger mb-2 d-none"></div>
					<div id="finalButtonsContainer">
						<div class="d-flex flex-column">
							<cfif local.rootSubscriberID eq 0 OR local.topStatus eq "O">
								<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="finalizeWithEmail"
									data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title=""
									data-original-title="Only applies if this subscription or selected addons have been assigned an Email Template to send upon becoming active. Email(s) will not be sent until the subscription activation requirement is met."
									>Finalize and Invoice - Allow Automated Emails *</button>
								<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="finalizeWithoutEmail">Finalize and Invoice - Skip Automated Emails</button>
							</cfif>
							<cfif local.rootSubscriberID gt 0>
								<cfif local.topStatus eq "O">
									<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="keepAsBilled">Save and Keep as Billed</button>
								<cfelseif local.topStatus eq "R">
									<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="keepAsRenewalNotSent">Save and Keep as Renewal Not Sent</button>
									<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="saveAsBilled">Save Subscription as Billed</button>
								</cfif>
							<cfelse>
								<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="createAsBilled">Create Subscription as Billed</button>
							</cfif>
						</div>
					</div>
					<!--- form to be shown when grace end date is left blank, or when saving as billed subscription to prompt for offer expiration date --->
					<div id="extraDatesPromptForm" class="d-none">
						<div class="alert alert-info mb-2" id="extraDatesFormAlerts"></div>
						<table class="table table-sm table-borderless">
							<tbody>
								<tr>
									<td width="30%">Parent Subscription:</td>
									<td class="px-2">
										<div class="font-weight-bold field-header-label text-grey">START DATE</div>
										<div class="input-group input-group-sm">
											<input type="text" name="fTermFromRO" id="fTermFromRO" value="" class="form-control form-control-sm" readonly disabled>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermFromRO"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</td>
									<td class="px-2">
										<div class="font-weight-bold field-header-label text-grey">END DATE</div>
										<div class="input-group input-group-sm">
											<input type="text" name="fTermToRO" id="fTermToRO" value="" class="form-control form-control-sm" readonly disabled>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermToRO"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</td>
								</tr>
								<tr><td colspan="3"></td></tr>
								<tr>
									<td></td>
									<td class="px-2">
										<div id="graceEndPrompFieldLabel" class="font-weight-bold field-header-label">GRACE END DATE</div>
										<div class="input-group input-group-sm">
											<input type="text" name="fTermGracePrompt" id="fTermGracePrompt" value="" class="form-control form-control-sm dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermGracePrompt"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</td>
									<td class="px-2">
										<div id="offerExpDateWrapper" class="d-none">
											<div class="font-weight-bold field-header-label">OFFER EXPIRATION DATE</div>
											<div class="input-group input-group-sm">
												<input type="text" name="fOfferExpDatePrompt" id="fOfferExpDatePrompt" value="" class="form-control form-control-sm dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fOfferExpDatePrompt"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<div class="mt-4">
							<input type="hidden" name="extraDatesPromptConfirm" id="extraDatesPromptConfirm" value="0">
							<button type="button" id="extraDatesPromptProceedBtn" class="btn btn-sm btn-primary font-weight-bold" onclick="saveExtraDatesPrompt();">Create Subscription as Billed</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelExtraDatesPrompt();">Cancel</button>
						</div>
					</div>
					<div id="finalSaveLoadingCard" class="mt-3 d-none">
						<div class="mx-3 my-2 d-flex align-items-center">
							<i class="spinner-border spinner-border-sm"></i><span class="font-weight-bold ml-2">Saving Subscription...</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>
<cfif local.useAccrualAcct>
	<script id="mc_recogDateRowHTML" type="text/x-handlebars-template">
		<tr id="recogDateRow_{{inputkey}}" class="recogDateRow" data-inputkey="{{inputkey}}">
			<td>{{subname}}</td>
			<td>
				<div class="input-group input-group-sm">
					<input type="text" name="fRecogFrom_{{inputkey}}" id="fRecogFrom_{{inputkey}}" class="form-control form-control-sm dateControl" value="{{frecogfromdate}}">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="fRecogFrom_{{inputkey}}"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</td>
			<td>
				<div class="input-group input-group-sm">
					<input type="text" name="fRecogTo_{{inputkey}}" id="fRecogTo_{{inputkey}}" class="form-control form-control-sm dateControl" value="{{frecogtodate}}" size="12">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="fRecogTo_{{inputkey}}"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
			</td>
			<td>
				{{##if isroot}}
					<div id="rootRecogDatesCopyEl" class="flex-shrink-1 align-items-center d-none">
						<a href="javascript:copyDateToAllRecogDates('{{inputkey}}');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Copy to all Recognition Dates"><i class="fa-solid fa-copy font-size-lg"></i></a>
					</div>
				{{/if}}
			</td>
		</tr>
	</script>
</cfif>
<script id="mc_subSelectionSummaryHTML" type="text/x-handlebars-template">
	{{##each arraddons}}
		{{##each arrsubs}}
			{{##if @first}}
				{{##compare ../setname '==' name}}
					{{> mc_subRateRowHTML idkey=idkey name=name rate=rate namefwbold=1 subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
					<div class="pl-3">
				{{/compare}}
				{{##compare ../setname '!=' name}}
					<div class="addOnSetName font-weight-bold mb-2">{{../setname}}</div>
					<div class="pl-3">
						{{> mc_subRateRowHTML idkey=idkey name=name rate=rate subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
				{{/compare}}
			{{else}}
				{{> mc_subRateRowHTML idkey=idkey name=name rate=rate subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
			{{/if}}
			{{##if childaddons}}
				{{> mc_subSelectionSummaryHTML arraddons=childaddons showfreqname=showfreqname }}
			{{/if}}
			{{##if @last}}
				</div>
			{{/if}}
		{{/each}}
	{{/each}}
</script>
<script id="mc_subRateRowHTML" type="text/x-handlebars-template">
	<div id="{{idkey}}_rateRow" class="rateRow mb-2 row no-gutters align-items-center">
		<div id="{{idkey}}_nameDisplay" class="col-7 pr-2{{##if namefwbold}} font-weight-bold{{/if}}">{{name}}{{##if isfreeaddonsub}}<span class="badge badge-success ml-2 freeaddonbadge">Free</span>{{/if}}</div>
		<div id="{{idkey}}_rateDisplay" class="col subRateDisplayElm text-right px-3">{{ratedisplay}}</div>
		<div id="{{idkey}}_rateEditable" class="col subRateEditableElm px-3 d-none">
			<div class="d-flex">
				<div class="input-group input-group-sm ml-auto" style="max-width:200px;">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input type="text" name="modifiedRateTotal_{{subid}}_{{rfid}}" id="modifiedRateTotal_{{subid}}_{{rfid}}" class="form-control form-control-sm modifiedRatePriceField" value="{{termratetouse}}" data-originalvalue="{{termratetouse}}" data-linkeddisplayfieldid="{{idkey}}_rateDisplay" onblur="onBlurEditableRateAmt(this);">
				</div>
			</div>
		</div>
		{{##if showfreqname}}
			<div id="{{idkey}}_rateFreq" class="freqDisplayElm col-auto" style="white-space:nowrap;max-width:250px;">{{freqname}}</div>
		{{/if}}
	</div>
</script>
</cfoutput>