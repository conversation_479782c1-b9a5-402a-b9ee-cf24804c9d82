<!--- default params --->
<cfparam name="request.contributorFirstName" default="">
<cfparam name="request.contributorLastName" default="">
<cfparam name="request.depoMemberDataID" default="">
<cfparam name="request.expertFirstName" default="">
<cfparam name="request.expertLastName" default="">
<cfparam name="request.expertNameContains" default="">
<cfparam name="request.documentID" default="">
<cfparam name="request.depositionDateFrom" default="">
<cfparam name="request.depositionDateTo" default="">
<cfparam name="request.dateEnteredFrom" default="">
<cfparam name="request.dateEnteredTo" default="">
<cfparam name="request.statusSetFrom" default="">
<cfparam name="request.statusSetTo" default="">
<cfparam name="request.approvedDate" default="">
<cfparam name="request.documentStatusID" default="0">
<cfparam name="request.style" default="">
<cfparam name="request.orgCode" default="ALL">
<cfparam name="request.hasAmazonCredits" default="0">
<cfparam name="request.docHasAttachments" default="">
<cfparam name="request.documentExtension" default="">
<cfparam name="request.xodpreapprove" default="">
<cfparam name="request.xodapprove" default="">
<cfparam name="request.excludeDisabledDocs" default="">
<cfparam name="request.uploadSourceID" default="">
<cfparam name="request.maxRows" default="100">
<cfparam name="request.startRow" default="0">
<cfparam name="request.orderBy" default="5">
<cfparam name="request.orderDir" default="asc">
<cfparam name="request.resultsMode" default="list">

<cfset local.returnStruct = { "success":false, "totalcount":0, "html":"" }>

<cftry>
	<cfset local.objDocuments = CreateObject("component","models.tsadmin.act_documents")>
	<cfset local.objDocuments.saveDocumentsQueueFilter(contributorFirstName=request.contributorFirstName, contributorLastName=request.contributorLastName, 
		depoMemberDataID=request.depoMemberDataID, expertFirstName=request.expertFirstName, expertLastName=request.expertLastName, 
		expertNameContains=request.expertNameContains, documentID=request.documentID, style=request.style, 
		orgCode=request.orgCode, depositionDateFrom=request.depositionDateFrom, depositionDateTo=request.depositionDateTo, 
		dateEnteredFrom=request.dateEnteredFrom, dateEnteredTo=request.dateEnteredTo, statusSetFrom=request.statusSetFrom, 
		statusSetTo=request.statusSetTo, documentStatusID=request.documentStatusID, hasAmazonCredits=val(request.hasAmazonCredits),
		docHasAttachments=request.docHasAttachments, documentExtension=request.documentExtension, xodpreapprove=request.xodpreapprove,
		xodapprove=request.xodapprove, excludeDisabledDocs=request.excludeDisabledDocs, uploadSourceID=request.uploadSourceID, 
		orderBy=request.orderBy, orderDir=request.orderDir)>

	<cfset qryDocuments = local.objDocuments.getFilteredDocuments(contributorFirstName=request.contributorFirstName, 
		contributorLastName=request.contributorLastName, depoMemberDataID=request.depoMemberDataID, expertFirstName=request.expertFirstName, 
		expertLastName=request.expertLastName, expertNameContains=request.expertNameContains, documentID=request.documentID, style=request.style, 
		orgCode=request.orgCode, depositionDateFrom=request.depositionDateFrom, depositionDateTo=request.depositionDateTo, 
		dateEnteredFrom=request.dateEnteredFrom, dateEnteredTo=request.dateEnteredTo, statusSetFrom=request.statusSetFrom, statusSetTo=request.statusSetTo, 
		documentStatusID=request.documentStatusID, docHasAttachments=request.docHasAttachments, documentExtension=request.documentExtension, 
		xodpreapprove=request.xodpreapprove, xodapprove=request.xodapprove, hasAmazonCredits=val(request.hasAmazonCredits), excludeDisabledDocs=request.excludeDisabledDocs,
		uploadSourceID=request.uploadSourceID, orderBy=request.orderBy, orderDir=request.orderDir, posStart=request.startRow, count=request.maxRows,
		resultsMode=request.resultsMode)>

	<cfif request.resultsMode EQ 'list'>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif qryDocuments.RecordCount>
				<b>#qryDocuments.totalCount# document<cfif qryDocuments.totalCount gt 1>s</cfif> found.</b>
				<cfif request.StartRow gt 1 or request.StartRow + request.MaxRows lte qryDocuments.totalCount>
					<cfset local.pageNum = (request.StartRow / request.MaxRows) + 1>
					<cfset local.totalPages = Ceiling(qryDocuments.totalCount / request.MaxRows)>
					<span style="float:right;margin:0px 10px;">
						<span style="margin-right:10px;color:##87837e;">#request.MaxRows# documents per page</span>
						<span style="margin-right:8px;">(Page #local.pageNum# of #local.totalPages#)</span>
						<cfif request.StartRow gt 1>
							[<a href="##" onclick="doLoadDocuments(#Evaluate(request.StartRow - request.MaxRows)#);return false;"><b>&lt; Previous</b></a>]
						<cfelse>
							[Previous]
						</cfif>
						<cfif request.StartRow + request.MaxRows lte qryDocuments.totalCount> 
							[<a href="##" onclick="doLoadDocuments(#Evaluate(request.StartRow + request.MaxRows)#);return false;"><b>Next &gt;</b></a>]
						<cfelse>
							[Next]
						</cfif>
					</span>
				</cfif>
				<table class="tblSlim" width="100%">
				<tr>
					<th style="padding: 8px 5px;">
						<button type="button" id="btnDelete" style="font-size:9pt;" onclick="deleteDocuments()">Delete</button>
						<button type="button" id="btnShowChangeCreditType" style="font-size:9pt;" onclick="changeCreditType()">Change Type of Credit</button>
					</th>
				</tr>
				</table>

				<cfset local.tableColumns = [
					{ title: "Document ID", width:"120", class="" },
					{ title: "Document Status", width:"200", class="" },
					{ title: "Contributor Name", width:"", class="" },
					{ title: "Expert Name", width:"", class="" },
					{ title: "Deposition Date", width:"120", class="" },
					{ title: "Date of Upload", width:"120", class="" },
					{ title: "Approval Date", width:"120", class="" },
					{ title: "Purchase Credit Value", width:"160", class="r" },
					{ title: "Amazon Credit Value", width:"160", class="r" }
				]>

				<table id="tblDocuments" class="tblSlim" width="100%">
					<tr>
						<th class="c"><input type="checkbox" name="masterCheckBox" value="1" onclick="toggleCheckAllDocuments(this.checked);"></th>
						<cfloop array="#local.tableColumns#" item="local.thisColumn" index="local.index">
							<th<cfif len(local.thisColumn.width)> width="#local.thisColumn.width#"</cfif> class="valueCol<cfif len(local.thisColumn.class)> #local.thisColumn.class#</cfif>" onclick="doSortDocumentList(#local.index#);">
								#local.thisColumn.title#
								<cfif local.index eq request.orderBy>
									<cfif request.orderDir eq "asc">
										<i class="fa-solid fa-sort-up fa-lg" style="vertical-align:sub;margin-left:3px;"></i>
									<cfelse>
										<i class="fa-solid fa-sort-down fa-lg" style="vertical-align:baseline;margin-left:3px;"></i>
									</cfif>
								</cfif>
							</th>
						</cfloop>
					</tr>
					<cfloop query="qryDocuments">
						<tr>
							<td class="c" width="40"><input type="checkbox" name="selDocID" value="#qryDocuments.DocumentID#"></td>
							<td>
								<a href="DocumentEdit.cfm?depomemberdataID=#qryDocuments.depomemberdataID#&DocumentID=#qryDocuments.DocumentID#">#qryDocuments.DocumentID#</a>
								<cfif qryDocuments.origHasAttachments>&nbsp;&nbsp;<i class="fa-solid fa-paperclip"></i></cfif>
								<div class="dim mt-1">#qryDocuments.originalExt#</div>
							</td>
							<td>
								#qryDocuments.DocumentStatus#
								<cfif (qryDocuments.DocumentStatus eq "Flagged for Review" or qryDocuments.DocumentStatus eq "Pending Approval") and len(qryDocuments.XODPreApprove)>&nbsp;&nbsp;<i class="fa-regular fa-eye fa-lg" title="Preview Available"></i>
								<cfelseif qryDocuments.DocumentStatus eq "Approved" and len(qryDocuments.XODApprove)>&nbsp;&nbsp;<i class="fa-regular fa-eye fa-lg" title="Document Available"></i>
								</cfif>		
							</td>
							<td>
								<a href="MemberEdit.cfm?depoMemberDataID=#qryDocuments.DepomemberdataID#" target="_blank">#qryDocuments.ContributorName#</a> (#qryDocuments.DepomemberdataID#)
								<div style="margin-top:4px;">#qryDocuments.BillingFirm#</div>
							</td>
							<td>
								#qryDocuments.expertName#
								<div class="dim mt-1">#qryDocuments.expertFirstName# #qryDocuments.expertMiddleName# #qryDocuments.expertLastName#</div>
							</td>
							<td>#DateFormat(qryDocuments.DocumentDate,"mm/dd/yyyy")#</td>
							<td>#DateFormat(qryDocuments.DateEntered,"mm/dd/yyyy")#</td>
							<td>#DateFormat(qryDocuments.ApprovalDate,"mm/dd/yyyy")#</td>
							<td class="r" style="padding-right:10px;">#len(qryDocuments.PurchaseCreditAmount) ? dollarFormat(qryDocuments.PurchaseCreditAmount) : ''#</td>
							<td class="r" style="padding-right:10px;">#len(qryDocuments.DepoAmazonBucksCredit) ? dollarFormat(qryDocuments.DepoAmazonBucksCredit) : ''#</td>
						</tr>
					</cfloop>
				</table>
			<cfelse>
				<div style="padding:.75rem 1.25rem;border:1px solid transparent;border-radius:.25rem;color:##000;background-color:##d1ecf1;border-color:##bee5eb;">
					No matching documents found based on the filter criteria.
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.returnStruct = { "success":true, "totalcount":qryDocuments.totalCount, "html":trim(local.data) }>
	</cfif>
<cfcatch type="Any">
	<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
	<cfset local.returnStruct = { "success":false }>
</cfcatch>
</cftry>

<cfif request.resultsMode EQ 'list'>
	<!--- output serialized JSON --->
	<cfoutput>#serializeJSON(local.returnStruct)#</cfoutput>
<cfelseif request.resultsMode EQ 'export'>
	<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=qryDocuments.sourceFilePath, displayName="Documents.csv", deleteSourceFile=false, forceDownload=true)>
	<cfabort>
</cfif>