<cfcomponent>
	<cffunction name="init" access="public" returntype="void">
		<cfset variables.remoteLoginAssociations = "PA">
		<cfset variables.objectTimeStamp = getTickCount()>
	</cffunction>
	<cffunction name="isTrialLawyerAssociation" access="public" output="no" returntype="boolean">
		<cfargument name="sitecode" required="Yes" type="string">
		<cfset var nonTrialLawyerAssociations = "TW,TWF,CAGL">
		<cfreturn not listfindnocase(nonTrialLawyerAssociations,arguments.sitecode)>
	</cffunction>

	<cffunction name="isRemoteLoginAssociation" access="public" output="no" returntype="boolean">
		<cfargument name="sitecode" required="Yes" type="string">
		<cfreturn listfindnocase(variables.remoteLoginAssociations,arguments.sitecode)>
	</cffunction>
	<cffunction name="getRemoteLoginAssociations" access="public" output="no" returntype="string">
		<cfreturn variables.remoteLoginAssociations>
	</cffunction>

	<cffunction name="getStates" returntype="query" access="public" output="No">
		<cfset var qryStates = "">
	
		<cfquery name="qryStates" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			select c.countryID, c.country, s.stateid, s.code as StateCode, s.name as StateName
			from ams_states as s
			inner join ams_countries as c on c.countryID = s.countryID
			order by 
			<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID is 2>
				case when c.countryID = 2 then 0 else 1 end, 
			</cfif>
			c.orderPref, c.country, s.orderPref, s.name
		</cfquery>
		
		<cfreturn qryStates>
	</cffunction>

	<cffunction name="getCountries" returntype="query" access="public" output="No">
		<cfset var qryCountries = "">
		
		<cfquery name="qryCountries" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(1,0,0,0)#">
			Select countryID, countryCode, country
			from ams_countries
			order by 
			<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID is 2>
				case when countryID = 2 then 0 else 1 end, 
			</cfif>
			orderPref, country
		</cfquery>
		
		<cfreturn qryCountries>
	</cffunction>
	
	<cffunction name="getActiveMemberID" access="public" returntype="numeric" output="no">
		<cfargument name="memberID" type="numeric" required="Yes">

		<cfset var qryActiveMemberID = "">
		
		<cfquery name="qryActiveMemberID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">) as activeMemberID
		</cfquery>
		
		<cfreturn val(qryActiveMemberID.activeMemberID)>
	</cffunction>

	<cffunction name="redirect" access="public" displayname="Redirects user to a new page" output="no">
		<cfargument name="urlToGoTo" type="string" required="Yes">
		<cflocation url="#arguments.urlToGoTo#" addtoken="No">
	</cffunction>

	<cffunction name="redirectTo404" access="public" displayname="Redirects user to the 404 page" output="no">
		<cfset redirect(urlToGoTo='/?pg=404')/>
	</cffunction>

	<cffunction name="checkLyrisArchive" access="public" returntype="void" output="no" hint="Used to check lyrisArchive database">
		<cfset var local = StructNew()>
		
		<cftry>
			<cfquery name="local.qryTest" datasource="#application.dsn.lyrisarchive.dsn#" timeout="5">
				set nocount on;

				declare @listID int

				select top 1 @listID = listID
				from messagelists

				select top 1 messageid_
				from messagelists ml
				inner join messages_ m
					on m.listID = ml.listID
					and ml.listID = @listID

				set nocount off;
			</cfquery>
			<cfset local.isOK = true>
		<cfcatch type="database">
			<cfset local.isOK = false>
		</cfcatch>
		</cftry>
		
		<cfset application.dsn.lyrisarchive.isAvailable = local.isOK>
	</cffunction>

	<cffunction name="checkTrialsLyris1" access="public" returntype="void" output="no" hint="Used to check trialslyris1 database">
		<cfset var local = StructNew()>
		
		<cftry>
			<cfquery name="local.qryTest" datasource="#application.dsn.trialslyris1.dsn#" timeout="5">
				select top 1 memberid_
				from dbo.members_
			</cfquery>
			<cfset local.isOK = true>
		<cfcatch type="database">
			<cfset local.isOK = false>
		</cfcatch>
		</cftry>
		
		<cfset application.dsn.trialslyris1.isAvailable = local.isOK>
	</cffunction>

	<cffunction name="checkMailArchive" access="public" returntype="void" output="no" hint="Used to check mailarchive database">
		<cfset var local = StructNew()>
		
		<cftry>
			<cfquery name="local.qryTest" datasource="#application.dsn.mailarchive.dsn#" timeout="5">
				select top 1 emailid
				from dbo.orders
			</cfquery>
			<cfset local.isOK = true>
		<cfcatch type="database">
			<cfset local.isOK = false>
		</cfcatch>
		</cftry>
		
		<cfset application.dsn.mailarchive.isAvailable = local.isOK>
	</cffunction>


	<!--- string manipulation --->
	<cffunction name="fullLeft" access="public" returntype="string" output="no">
		<cfargument name="str" type="string" required="Yes">
		<cfargument name="count" type="numeric" required="Yes">
		
		<cfscript>
		if (not refind("[[:space:]]", arguments.str) or (arguments.count gte len(arguments.str)))
			return Left(arguments.str,arguments.count);
		else if (reFind("[[:space:]]",mid(arguments.str,arguments.count+1,1))) {
			return left(arguments.str,arguments.count);
		} else { 
			if (arguments.count-refind("[[:space:]]", reverse(mid(arguments.str,1,arguments.count)))) return Left(arguments.str, (arguments.count-refind("[[:space:]]", reverse(mid(arguments.str,1,arguments.count))))); 
			else return(left(arguments.str,1));
		}
		</cfscript>
	</cffunction>

	<cffunction name="minText" access="public" returntype="string" output="no">
		<cfargument name="str" type="string" required="Yes">
		<cfreturn str.replaceall("[\t\r\n]","")>
	</cffunction>

	<cffunction name="normalizeString" access="public" returntype="string" output="no" hint="swap accented characters with no accented characters">
		<cfargument name="str" type="string" required="Yes">
		<cfscript>
			//based on the approach found here: http://stackoverflow.com/a/1215117/894061
			var Normalizer = createObject("java","java.text.Normalizer");
			var NormalizerForm = createObject("java","java.text.Normalizer$Form");
			var normalizedString = Normalizer.normalize(arguments.str, NormalizerForm.NFD);
			var pattern = createObject("java","java.util.regex.Pattern").compile("\p{InCombiningDiacriticalMarks}+");
			return pattern.matcher(normalizedString).replaceAll("");
		</cfscript>
	</cffunction>

	<cffunction name="slugify" access="public" returntype="string" output="no">
		<cfargument name="str" type="string" required="Yes">
		<cfscript>
			var output = normalizeString(arguments.str).trim();
			output = output.lcase().rereplace('[^a-z0-9\s-]','','all');
			output = output.rereplace('\s+','-','all');
			return output;
		</cfscript>
	</cffunction>

	<cffunction name="diffDateLong" access="public" returntype="string" output="no">
		<cfargument name="date1" type="date" required="yes">
		<cfargument name="date2" type="date" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.diff_min = dateDiff("n",arguments.date1,arguments.date2)>
		
		<cfset local.numdays = local.diff_min \ 1440>
		<cfset local.numhours = (local.diff_min mod 1440) \ 60>
		<cfset local.numminutes = (local.diff_min mod 1440) mod 60>
		
		<cfset local.returnString = "">
		<cfif local.numdays is 1>
			<cfset local.returnString = local.returnString & "#local.numdays# day">
		<cfelseif local.numdays gt 1>
			<cfset local.returnString = local.returnString & "#local.numdays# days">
		</cfif>
		<cfif local.numhours is 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numhours# hour">
		<cfelseif local.numhours is 1>
			<cfset local.returnString = local.returnString & "#local.numhours# hour">
		<cfelseif local.numhours gt 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numhours# hours">
		<cfelseif local.numhours gt 1>
			<cfset local.returnString = local.returnString & "#local.numhours# hours">
		</cfif>
		<cfif local.numminutes lt 1 and len(local.returnString) is 0>
			<cfset local.returnString = local.returnString & "less than a minute">
		<cfelseif local.numminutes is 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numminutes# minute">
		<cfelseif local.numminutes is 1>
			<cfset local.returnString = local.returnString & "#local.numminutes# minute">
		<cfelseif local.numminutes gt 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & ", #local.numminutes# minutes">
		<cfelseif local.numminutes gt 1>
			<cfset local.returnString = local.returnString & "#local.numminutes# minutes">
		</cfif>
		
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="diffDateAbbr" access="public" returntype="string" output="no">
		<cfargument name="date1" type="date" required="yes">
		<cfargument name="date2" type="date" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.diff_min = dateDiff("n",arguments.date1,arguments.date2)>
		
		<cfset local.numdays = local.diff_min \ 1440>
		<cfset local.numhours = (local.diff_min mod 1440) \ 60>
		<cfset local.numminutes = (local.diff_min mod 1440) mod 60>
		
		<cfset local.returnString = "">
		<cfif local.numdays gte 1>
			<cfset local.returnString = local.returnString & "#local.numdays#d">
		</cfif>
		<cfif local.numhours gte 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & " #local.numhours#h">
		<cfelseif local.numhours gte 1>
			<cfset local.returnString = local.returnString & "#local.numhours#h">
		</cfif>
		<cfif local.numminutes lt 1 and len(local.returnString) is 0>
			<cfset local.returnString = local.returnString & "0m">
		<cfelseif local.numminutes gte 1 and len(local.returnString) gt 0>
			<cfset local.returnString = local.returnString & " #local.numminutes#m">
		<cfelseif local.numminutes gte 1>
			<cfset local.returnString = local.returnString & "#local.numminutes#m">
		</cfif>
		
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="getFilteredURLParams" access="public" output="false" returntype="string">
		<cfargument name="exclusionList" type="string" required="true">
		<cfscript>
			var local = {};
			return url.keyArray().reduce(
				function(keyValuePair, element) {
					if (not listfindnocase(exclusionList,element))
						keyValuePair = keyValuePair.listappend("#element#=#url[element]#","&");
					return keyValuePair;
				}, "");
		</cfscript>
	</cffunction>


	<!--- xml manipulation --->
	<cffunction name="combineXML" access="public" returntype="xml" output="no">
		<cfargument name="rootXML" type="xml" required="Yes">
		<cfargument name="secondaryXML" type="xml" required="Yes">

		<cfset var local = StructNew()>

		<cfscript>
		if (isXMLDoc(arguments.rootXML))
			arguments.rootXML = ToString(arguments.rootXML);
		arguments.rootXML = ReplaceNoCase(arguments.rootXML,"<?xml version=""1.0"" encoding=""UTF-8""?>","");

		if (isXMLDoc(arguments.secondaryXML))
			arguments.secondaryXML = ToString(arguments.secondaryXML);
		arguments.secondaryXML = ReplaceNoCase(arguments.secondaryXML,"<?xml version=""1.0"" encoding=""UTF-8""?>","");
		
		local.pos = refind("[^/]\>",arguments.rootXML);
		if (local.pos gt 0)
			local.finalXML = trim(insert(arguments.secondaryXML,arguments.rootXML,local.pos+1));
		else 
			local.finalXML = trim(arguments.rootXML);

		return XMLParse(local.finalXML);
		</cfscript>
	</cffunction>
	<cffunction name="XmlDeleteNodes" access="public" returntype="void" output="false">
		<cfargument name="XmlDocument" type="any" required="true">
		<cfargument name="Nodes" type="any" required="false" hint="node or array of nodes">
	 
		<cfset var local = StructNew()>
	 
		<cfif NOT IsArray( ARGUMENTS.Nodes )>
			<cfset LOCAL.Node = ARGUMENTS.Nodes />
			<cfset ARGUMENTS.Nodes = [ LOCAL.Node ] />
		</cfif>
	 
		<cfloop index="LOCAL.NodeIndex" from="#ArrayLen( ARGUMENTS.Nodes )#" to="1" step="-1">
			<cfset LOCAL.Node = ARGUMENTS.Nodes[ LOCAL.NodeIndex ] />
			<cfif StructKeyExists( LOCAL.Node, "XmlChildren" )>
				<cfset LOCAL.Node.XmlAttributes[ "delete-me-flag" ] = "true" />
			<cfelse>
				<cfset ArrayDeleteAt(ARGUMENTS.Nodes,LOCAL.NodeIndex) />
			</cfif>
		</cfloop>
	 
		<cfloop index="LOCAL.Node" array="#ARGUMENTS.Nodes#">
			<cfset LOCAL.ParentNodes = XmlSearch( LOCAL.Node, "../" ) />
			<cfif (ArrayLen( LOCAL.ParentNodes ) AND StructKeyExists( LOCAL.ParentNodes[ 1 ], "XmlChildren" ))>
				<cfset LOCAL.ParentNode = LOCAL.ParentNodes[ 1 ] />
				<cfloop index="LOCAL.NodeIndex" from="#ArrayLen( LOCAL.ParentNode.XmlChildren )#" to="1" step="-1">
					<cfset LOCAL.Node = LOCAL.ParentNode.XmlChildren[ LOCAL.NodeIndex ] />
					<cfif StructKeyExists( LOCAL.Node.XmlAttributes, "delete-me-flag" )>  
						<cfset ArrayDeleteAt(LOCAL.ParentNode.XmlChildren,LOCAL.NodeIndex) />
						<cfset StructDelete(LOCAL.Node.XmlAttributes,"delete-me-flag") />
					</cfif>
				</cfloop>
			</cfif>
		</cfloop>

		<cfreturn>
	</cffunction>	
	<cffunction	name="XmlAppend" access="public" returntype="any" output="false" hint="Copies the children of one node to the node of another document. By Ben Nadel">
		<cfargument name="NodeA" type="any" required="true" hint="The node whose children will be added to." />
		<cfargument name="NodeB" type="any" required="true" hint="The node whose children will be copied to another document." 	/>
	 
		<!--- Set up local scope. --->
		<cfset var LOCAL = StructNew() />
		 
		<!---
		Get the child nodes of the originating XML node.
		This will return both tag nodes and text nodes.
		We only want the tag nodes.
		--->
		<cfset LOCAL.ChildNodes = ARGUMENTS.NodeB.GetChildNodes() />
	 
	 
		<!--- Loop over child nodes. --->
		<cfloop index="LOCAL.ChildIndex" from="1" to="#LOCAL.ChildNodes.GetLength()#" step="1">
	 
		 
			<!---
			Get a short hand to the current node. Remember
			that the child nodes NodeList starts with
			index zero. Therefore, we must subtract one
			from out child node index.
			--->
			<cfset LOCAL.ChildNode = LOCAL.ChildNodes.Item(JavaCast("int",(LOCAL.ChildIndex - 1))) />
			 
			<!---
			Import this noded into the target XML doc. If we
			do not do this first, then COldFusion will throw
			an error about us using nodes that are owned by
			another document. Importing will return a reference
			to the newly created xml node. The TRUE argument
			defines this import as DEEP copy.
			--->
			<cfset LOCAL.ChildNode = ARGUMENTS.NodeA.GetOwnerDocument().ImportNode(LOCAL.ChildNode,	JavaCast( "boolean", true )) />
			 
			<!---
			Append the imported xml node to the child nodes
			of the target node.
			--->
			<cfset ARGUMENTS.NodeA.AppendChild(LOCAL.ChildNode) />
		 
		</cfloop>
		 
		 
		<!--- Return the target node. --->
		<cfreturn ARGUMENTS.NodeA />
	</cffunction>
	<cffunction name="XmlDeleteNodesJava" access="public" returntype="void" output="false" hint="I remove a node or an array of nodes from the given XML document.">
	 
		<!--- Define arugments. --->
		<cfargument name="XmlDocument" type="any" required="true" hint="I am a ColdFusion XML document object."	/>
		<cfargument	name="Nodes" type="any"	required="false" hint="I am the node or an array of nodes being removed from the given document."/>
	 
		<!--- Define the local scope. --->
		<cfset var LOCAL = StructNew() />
	 
		<!---
			Check to see if we have a node or array of nodes. If we
			only have one node passed in, let's create an array of
			it so we can assume an array going forward.
		--->
		<cfif NOT IsArray( ARGUMENTS.Nodes )>
	 
			<!--- Get a reference to the single node. --->
			<cfset LOCAL.Node = ARGUMENTS.Nodes />
	 
			<!--- Convert single node to array. --->
			<cfset ARGUMENTS.Nodes = [ LOCAL.Node ] />
	 
		</cfif>
	 
	 
		<!--- Loop over the nodes. --->
		<cfloop
			index="LOCAL.Node"
			array="#ARGUMENTS.Nodes#">
	 
			<!--- Get the parent node. --->
			<cfset LOCAL.ParentNode = LOCAL.Node.GetParentNode() />
	 
			<!---
				Check to see if the parent was found. If not, then
				we are not dealing with an Element node.
			--->
			<cfif StructKeyExists( LOCAL, "ParentNode" )>
	 
				<!---
					Get the previous sibling of the node in the
					question. If there is no previous sibling, this
					will return NULL and it will tell us that
					the target node is the first node in the child
					nodes array.
				--->
				<cfset LOCAL.PrevNode = LOCAL.Node.GetPreviousSibling() />
	 
				<!---
					Check to see if the previous node was found
					or if is null (which will have removed the
					struct key).
				--->
				<cfif StructKeyExists( LOCAL, "PrevNode" )>
	 
					<!---
						We have the prev node. Use that to get the
						Java version of our ChildNode and delete from
						the parent.
					--->
					<cfset LOCAL.ParentNode.RemoveChild(
						LOCAL.PrevNode.GetNextSibling()
						) />
	 
				<cfelse>
	 
					<!---
						The previous node didn't exist which means
						that our target node is the first node in the
						child array.
					--->
					<cfset LOCAL.ParentNode.RemoveChild(
						LOCAL.ParentNode.GetFirstChild()
						) />
	 
				</cfif>
	 
			</cfif>
	 
		</cfloop>
	 
		<!--- Return out. --->
		<cfreturn />
	</cffunction>
	<cffunction name="xmlToStruct" returntype="any" hint="convert xml to more simplified struct">
		<cfargument name="x" type="xml" required="true">
		    <cfscript>
			    var s = {};
			    var temp = "";
			    var item = "";
			    var i = "";
			    if(xmlGetNodeType(x) == "DOCUMENT_NODE") {
			        s[structKeyList(x)] = xmlToStruct(x[structKeyList(x)]);
			    } else {
				    if(structKeyExists(x, "xmlAttributes") && !structIsEmpty(x.xmlAttributes)) {
				        for(item in x.xmlAttributes) {
				            s[item] = x.xmlAttributes[item];
				        }
				    }
				    if (structKeyExists(x, "xmlText") && len(trim(x.xmlText))){
				        return replace(replace(x.xmlText, Chr(10), "", "all"), chr(09), "", "all");
				    }
				    if(structKeyExists(x, "xmlChildren") && arrayLen(x.xmlChildren)) {
				        for(i=1; i<=arrayLen(x.xmlChildren); i++) {
				            if(structKeyExists(s, x.xmlchildren[i].xmlname)) {
				                if(!isArray(s[x.xmlChildren[i].xmlname])) {
				                    temp = s[x.xmlchildren[i].xmlname];
				                    s[x.xmlchildren[i].xmlname] = [temp];
				                }
								arrayAppend(s[x.xmlchildren[i].xmlname], xmlToStruct(x.xmlChildren[i]));
				             } else {
				                 //before we parse it, see if simple
				                 if(structKeyExists(x.xmlChildren[i], "xmlChildren") && arrayLen(x.xmlChildren[i].xmlChildren)) {
				                        s[x.xmlChildren[i].xmlName] = xmlToStruct(x.xmlChildren[i]);
				                 } else if(structKeyExists(x.xmlChildren[i],"xmlAttributes") && !structIsEmpty(x.xmlChildren[i].xmlAttributes)) {
				                    s[x.xmlChildren[i].xmlName] = xmlToStruct(x.xmlChildren[i]);
				                } else {
				                    s[x.xmlChildren[i].xmlName] = replace(replace(x.xmlChildren[i].xmlText, Chr(10), "", "all"), chr(09), "", "all");
				                }
				             }
				        }
				    }
				}
		    </cfscript>
	    <cfreturn s>
	</cffunction>
	<cffunction name="queryToArrayOfStructures" output="false" access="public" returntype="array" hint="Converts a query to an array of structures">    
		<cfargument name="theQuery" type="query" required="true" hint="The query to convert" />
		<cfscript>   
			var theArray = arraynew(1);
			var cols = listToArray( arguments.theQuery.columnlist );
			var row = 1;
			var thisRow = "";
			var col = 1;
			
			for(row = 1; row LTE arguments.theQuery.recordcount; row = row + 1){
				thisRow = {};
				for(col = 1; col LTE arraylen( cols ); col = col + 1){
					thisRow[ cols[ col ] ] = arguments.theQuery[ cols[ col ] ][ row ];
				}
				arrayAppend(theArray, thisRow);
			}
			
			return theArray;
		</cfscript>    
	</cffunction>

	<cffunction name="listRemoveDuplicates" output="false" access="public" returntype="string" >    
		<cfargument name="thelist" type="string" required="true"/>
		<cfargument name="delimiter" type="string" required="false" default=","/>

		<cfscript>   
			var listStruct = {};
			var i = 1;

			for(i=1;i<=listlen(thelist, delimiter);i++)
			{
				listStruct[listgetat(thelist,i)] = listgetat(thelist,i);
			}

			return structkeylist(listStruct,delimiter);
		</cfscript>    
	</cffunction>

	<cffunction name="initLyris" access="public" returntype="boolean" output="no">
		<cfset var local = StructNew()>
	
		<cftry>
			<cfquery name="local.getemails" datasource="#application.dsn.membercentral.dsn#">
				SELECT distinct email, replace(email,'''', '''''') AS email_escape
				FROM dbo.ams_networkProfileEmails
				WHERE profileID = <cfqueryparam value="#session.cfcuser.memberData.profileid#" cfsqltype="CF_SQL_INTEGER">
				and isCertified = 1
			</cfquery>
			<cfif local.getemails.recordcount>
				<cfset session.cfcuser.lyris.emaillist = valuelist(local.getemails.email)>
				<cfset session.cfcuser.lyris.sqlemaillist = quotedvaluelist(local.getemails.email_escape)>
			<cfelse>
				<cfset session.cfcuser.lyris.emaillist = "">
				<cfset session.cfcuser.lyris.sqlemaillist = "''">
			</cfif>

			<cfif application.dsn.trialslyris1.isAvailable>
				<cfset checkTrialsLyris1()>
			</cfif>
			<cfif application.dsn.trialslyris1.isAvailable>
				<cfquery name="this.qLists" datasource="#application.dsn.trialslyris1.dsn#" timeout="5">
					SELECT list_, memberid_, emailaddr_, IsListAdm_, fullname_
					FROM members_
					WHERE emailaddr_ in (#PreserveSingleQuotes(session.cfcuser.lyris.sqlemaillist)#) 
					and membertype_ in ('normal','held') 
					and list_ in (select name from lists_format where hidden = 0)
				</cfquery>
				<cfset session.cfcuser.lyrisinitialized = 1>
			<cfelse>
				<cfset session.cfcuser.lyrisinitialized = 0>
			</cfif>
		<cfcatch type="Any">
			<cfset session.cfcuser.lyrisinitialized = 0>
		</cfcatch>
		</cftry>

		<cfreturn true>
	</cffunction>	

	<cffunction name="sortArrayOfObjects" access="public" output="false" returntype="Array" hint="Returns an array of object sorted on one or more specified keys. Sort order can be specified seperately per key. Objects to be sorted can be either arrays or instantiated cfc's">
		<cfargument name="arrayToSort" type="Array" required="true" hint="An array that contains the objects to be sorted">
		<cfargument name="sortKeys" type="Array" required="true" hint="An array of structures with sorting specifications. Each struct in the array represents a key to sort the objects on. Each struct must have the following keys: 1)'keyname' - string - the name of the property to sort the objects by 2)'sortOrder' - string -the order in which to sort. Must be set to either 'ascending' or 'descending'"> 
		<cfargument name="doDuplicate" default="false" type="Boolean" required="false" hint="By default, the objects in the returned array point to the same memory location as the objects in the argument 'arrayToSort'. After executing this function changing an object in the returned array thus also changes the corresponding object in the argument array, and vice versa! If this kind of behavior is unwanted, specify this argument as true.">
						
		<cfset var local = structNew()>
		<cfset local.arrayToReturn = arrayNew(1)>
		<cfset local.nElements = arrayLen(arguments.arrayToSort)>
		<cfset local.nSortKeys = arrayLen(arguments.sortKeys)>
		<cfloop from="1" to="#local.nElements#" index="local.i">
			<cfset local.elementData = arguments.arrayToSort[local.i]>			
			<cfset local.insertPosition = 1>
			<cfset local.nPreviousElements = local.i - 1>
			<cfloop from="1" to="#local.nPreviousElements#" index="local.j">
				<cfset local.previousElementData = local.arrayToReturn[local.j]>
				<cfset local.doBreak = false>
				<cfloop from="1" to="#local.nSortKeys#" index="local.k">
					<cfset local.currentKey = arguments.sortKeys[local.k]>
					<cfset local.currentValue = local.elementData[local.currentKey.keyName]>
					<cfset local.previousValue = local.previousElementData[local.currentKey.keyName]>
					<cfset local.currentGreater = local.currentValue gt local.previousValue>
					<cfset local.previousGreater = local.previousValue gt local.currentValue>					
					<cfset local.currentFirst = (local.currentGreater AND (local.currentKey.sortOrder eq "descending")) OR (local.previousGreater AND (local.currentKey.sortOrder eq "ascending"))>
					<cfset local.previousFirst = (local.previousGreater AND (local.currentKey.sortOrder eq "descending")) OR (local.currentGreater AND (local.currentKey.sortOrder eq "ascending"))>
					<cfif local.previousFirst>
						<cfset local.insertPosition = local.insertPosition + 1>
						<cfbreak>			
					</cfif>
					<cfif local.currentFirst>
						<cfset local.doBreak = true>
						<cfbreak>
					</cfif>
				</cfloop>
				<cfif local.doBreak>
					<cfbreak>
				</cfif>
			</cfloop>
			<cfif arguments.doDuplicate>
				<cfset local.insertData = duplicate(local.elementData)>
			<cfelse>
				<cfset local.insertData = local.elementData>
			</cfif>
			<cfif local.insertPosition lt local.i>
				<cfset arrayInsertAt(local.arrayToReturn, local.insertPosition, local.insertData)>
			<cfelse>
				<cfset arrayAppend(local.arrayToReturn, local.insertData)>	
			</cfif>
		</cfloop>
		<cfreturn local.arrayToReturn>
	</cffunction>

	<cffunction name="mergePDFs" access="public" returntype="void" output="no">
		<cfargument name="pdfPath" type="string" required="yes">
		<cfargument name="listofPDFs" type="string" required="yes">
		<cfargument name="finalOutPutFile" type="string" required="yes">
		
		<cfset var local = StructNew()>
	
		<cfscript> 
		if (right(arguments.pdfPath,1) neq "/") arguments.pdfPath = arguments.pdfPath & "/";
		
		local.pdfCopy = createObject("java", "com.lowagie.text.pdf.PdfCopy"); 
		local.pdfReader = createObject("java","com.lowagie.text.pdf.PdfReader"); 
		local.pageSize = createObject("java", "com.lowagie.text.PageSize").init(); 
		local.bookMark = createObject("java","com.lowagie.text.pdf.SimpleBookmark"); 
		local.pdfDocument = createObject("java","com.lowagie.text.Document"); 
		local.newPDF = createObject("java","java.io.FileOutputStream").init(arguments.pdfPath & arguments.finalOutPutFile);
		local.pageOffset = 0; 
		local.PDFs = listToArray(arguments.listOfPDFs);
		local.master = arrayNew(1);
		
		for (local.i=1; local.i LTE arrayLen(local.PDFs); local.i=local.i+1) { 
			local.reader = ""; // clobber reader 
			local.pdfFile = arguments.pdfPath & local.PDFs[local.i]; 
			local.reader = local.pdfReader.init(local.pdfFile); 
			local.reader.removeUnusedObjects();
			local.reader.consolidateNamedDestinations(); 
			local.pages = local.reader.getNumberOfPages();
			local.pageOffset = local.pageOffset + local.pages; 
			if (local.i is 1) { 
				local.pdfDocument.init(local.reader.getPageSizeWithRotation(1)); 
				local.pdfCopy.init(local.pdfDocument, local.newPDF); 
				local.pdfDocument.open();             
			}
			// now add pages to new PDF 
			for (local.p=1; local.p LTE local.pages; local.p=local.p+1){ 
				local.page=local.pdfCopy.getImportedPage(local.reader,javacast("int",local.p)); 
				local.pdfCopy.addPage(local.page); 
			}
			// special case: does this thing have any forms? 
			local.acroForm=local.reader.getAcroForm(); 
			if (isDefined("local.acroForm")) local.pdfCopy.copyAcroForm(local.reader); 
		}
		if (arraylen(local.master) GT 0) local.pdfCopy.setOutlines(local.master); 
		local.pdfDocument.close();
		</cfscript> 
	</cffunction>
	
	<cffunction name="encryptPDF" access="public" returntype="void" output="no">
		<cfargument name="origPDF" type="string" required="yes">
		<cfargument name="newPDF" type="string" required="yes">
		<cfargument name="userPassword" type="string" required="yes">
		<cfargument name="ownerPassword" type="string" required="yes">
	
		<cfscript> 
		var local = StructNew();
		local.pdfReader = createObject("java", "com.lowagie.text.pdf.PdfReader").init(arguments.origPDF);
		local.streamOut = createObject("java", "java.io.FileOutputStream").init(arguments.newPDF);
		local.pdfStamper = createObject("java", "com.lowagie.text.pdf.PdfStamper").init(local.pdfReader, local.streamOut);
		local.pdfWriter = createObject("java", "com.lowagie.text.pdf.PdfWriter");
		local.pdfStamper.setEncryption(local.pdfWriter.STRENGTH128BITS,arguments.userPassword,arguments.ownerPassword,bitOr(local.pdfWriter.AllowPrinting,local.pdfWriter.AllowCopy));
		local.pdfStamper.close();
		</cfscript> 
	</cffunction>

	<cffunction name="directoryCopy" access="public" returntype="void" output="no">
	    <cfargument name="source" required="true" type="string">
	    <cfargument name="destination" required="true" type="string">
		<cfargument name="overwrite" required="false" type="Boolean" default="false" hint="Files in the directory should be overwritten.">
	
	    <cfset var contents = "" />
	    
	    <cfif not(directoryExists(arguments.destination))>
	        <cfdirectory action="create" directory="#arguments.destination#">
	    </cfif>
	    
	    <cfdirectory action="list" directory="#arguments.source#" name="contents">

	    <cfloop query="contents">
	        <cfif contents.type eq "file">
	            <cfif not (fileExists("#arguments.destination#/#contents.name#"))>
					<cffile action="copy" source="#arguments.source#/#contents.name#" destination="#arguments.destination#/#contents.name#">
				<cfelseif arguments.overwrite NEQ False>
					<cffile action="copy" source="#arguments.source#/#contents.name#" destination="#arguments.destination#/#contents.name#" nameconflict="overwrite">
	            </cfif>
	        <cfelseif contents.type eq "dir">
	            <cfset directoryCopy(arguments.source & "/" & contents.name, arguments.destination & "/" & contents.name,arguments.overwrite)>
	        </cfif>
	    </cfloop>
	</cffunction>

	<cffunction name="getFeedFromPath" returntype="struct" output="false" hint="returns a structure with the query and xml variables from a cffeed call.">
		<cfargument name="path" type="string" required="yes">
		<cfargument name="timeout" type="numeric" required="no" default="60">
		<cfargument name="userAgent" type="string" required="no" default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36">

		<cfset var local=structNew()>

		<cfset local.fileName=CreateUUID() & ".xml">
		<cfset local.filePath="">
		<cfset local.httpResult=structNew()>
		<cfset local.providedPath=arguments.path>

		<cfset local.result=structNew()>
		<cfset local.result.query=QueryNew("")>
		<cfset local.result.xmlVar="">
		<cfset local.result.feedSuccess=false>
		<cfset local.result.statusCode = 0>

		<cfset local.fileReady=false>

		<cftry>
			<cfif isValid("regex",arguments.path,application.regEx.url) OR isValid("regex","http://#arguments.path#",application.regEx.url)>

				<cfif arguments.timeout and arguments.timeout gt 0>
					<cfset local.requestTimeout = arguments.timeout>
				<cfelse>
					<cfset local.requestTimeout = 60>
				</cfif>

				<cfhttp url="#arguments.path#" method="get" throwonerror="yes" result="local.APIResult" timeout="#local.requestTimeout#" charset="utf-8" getasbinary="yes"></cfhttp>

				<cfset local.apiFileContent = ToString(local.APIResult.filecontent,"UTF-8")>
				<cfset local.result.statusCode = local.APIResult.responseheader.status_Code>

				<cfif val(local.result.statusCode) EQ 200>
					<cfset local.filePath="#application.paths.localTempNoWeb.path##local.fileName#">

					<!--- removed the <xml> tag, specifically the encoding. It breaks in Lucee. --->
					<cfset local.rawResponse = replaceNoCase(toString(local.apiFileContent),'<?xml version="1.0" encoding="UTF-8"?>','')>
					<cffile action="write" file="#local.filePath#" nameconflict="overwrite" output="#local.rawResponse#" charset="utf-8">
					<cfset local.fileReady=true>
				<cfelseif val(local.result.statusCode) eq 404>
					<cfset local.result.feedError = local.APIResult.errordetail>
					<cfset local.result.statusCode=404>
					<cfthrow type="FeedNotFound" message="Received 404 on Feed Retrieval." detail="System received 404 making the request to the remote feed. Exception passed along in local.APIResult.errordetail">
				<cfelseif val(local.result.statusCode) eq 408>
					<cfset local.result.responseheader=local.APIResult.responseheader>
					<cfthrow type="FeedTimeout" message="Remote Server reported Timeout on Feed Retrieval." detail="Remote server responsed with 408 Timeout while making the request to the remote feed. Response headers passed along in local.APIResult.responseheader">
				<cfelse>
					<cfset local.result.feedError = local.APIResult.errordetail>
					<cfthrow type="FeedError" message="Issue with Feed Retrieval." detail="Could not process feed from the provided URL. Not a timeout or 404. Exception passed along in local.APIResult.errordetail">
				</cfif>

			<cfelseif application.objDocDownload.isAllowedDownloadableLocation(arguments.path) AND FileExists(arguments.path)>
				<cfset local.filePath=arguments.path>
				<cfset local.fileReady=true>
			</cfif>

			<cfif local.fileReady>					
				<cffeed source="#local.filePath#" xmlVar="local.result.xmlVar" query="local.result.query" userAgent="#arguments.userAgent#">
				<cfset local.result.feedSuccess=true>
			<cfelse>
				<cfthrow type="FeedFileNotReady" message="Issue with Feed Retrieval. File not ready" detail="Could not process feed from the provided URL.">
			</cfif>

		<cfcatch type="any">
			<!--- Dont email about feed timeouts. These are just noise to exceptions --->
			<cfif listFindNoCase("FeedTimeout",cfcatch.type)>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage='Feed: #arguments.path#')>
			</cfif>
			<cfset local.result.feedSuccess=false>
			<cfif not structKeyExists(local.result, "feedError")>
				<cfset local.result.feedError=cfcatch>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="rssToJson" returntype="struct" output="false" hint="returns a structure with the results from a cffeed call.">
		<cfargument name="url" type="string" required="yes">
		<cfargument name="timeout" type="numeric" required="no" default="60">
		<cfargument name="cacheSeconds" type="numeric" required="no" default="600">

		<cfset var local=structNew()>

		<cfset local.httpResult=structNew()>

		<cfset local.result=structNew()>
		<cfset local.result.feed=[]>
		<cfset local.result.feedSuccess=false>
		<cfset local.result.statusCode = 0>

		<cfset local.fileReady=false>

		<cftry>
			<cfif isValid("regex",arguments.url,application.regEx.url) OR isValid("regex","http://#arguments.url#",application.regEx.url)>

				<cfset local.strHTTP = structNew()>
				<cfset local.strHTTP['url'] = arguments.url>
				<cfset local.strHTTP['method'] = "get">
				<cfset local.strHTTP['charset'] = "utf-8">
				<cfif arguments.timeout gt 0>
					<cfset local.strHTTP['timeout'] = arguments.timeout>
				<cfelse>
					<cfset local.strHTTP['timeout'] = 60>
				</cfif>
				<cfif arguments.cacheSeconds gt 0>
					<cfset local.strHTTP['cacheSeconds'] = arguments.cacheSeconds>
				</cfif>

				<cfset local.stHTTPJSON = serializeJSON(local.strHTTP)>
				<cfset local.stEnc = encrypt(local.stHTTPJSON,"20.!$_CenTR@l", "CFMX_COMPAT", "Hex")>
				<cfhttp url="#application.paths.backendplatform.internalURL#?event=internalUtils.fetchRSSFeed&returnFormat=json" method="post" throwonerror="Yes" result="local.httpResult" charset="utf-8" getasbinary="yes">
					<cfhttpparam type="FORMFIELD" name="params" value="#local.stEnc#">
				</cfhttp>
				<cfset local.decodedResults = ToString(local.httpResult.filecontent,"UTF-8")>
				<cfset local.httpResult = deserializeJSON(trim(local.decodedResults))>
				<cfset local.result.stEnc = local.stEnc>

				<cfif isdefined("local.httpResult.responseheader.Status_Code")>
					<cfset local.result.statusCode=local.httpResult.responseheader.Status_Code>
				</cfif>

				<cfif local.httpResult.success>
					<cfset local.result.feed = local.httpResult.filecontent>
					<cfset local.result.feedSuccess=true>
				<cfelseif isdefined("local.httpResult.error.statusCode") and local.httpResult.error.statusCode eq 404>
					<cfset local.result.feedError=local.httpResult.error>
					<cfset local.result.statusCode=404>
					<cfset local.result.message="BER received 404 making the request to the remote feed. BER Exception passed along in local.httpResult.error">
				<cfelseif isdefined("local.httpResult.error.statusCode") and local.httpResult.error.statusCode eq 408>
					<cfset local.result.feedError=local.httpResult.error>
					<cfset local.result.statusCode=408>
					<cfset local.result.message="BER timedout making the request to the remote feed. BER Exception passed along in local.httpResult.error">
				<cfelseif local.result.statusCode eq 408>
					<cfset local.result.responseheader=local.httpResult.responseheader>
					<cfset local.result.message="Remote server responsed with 408 Timeout while making the request to the remote feed. Response headers passed along in local.httpResult.responseheader">
				<cfelse>
					<cfset local.result.feedError=local.httpResult.error>
					<cfthrow type="FeedError" message="Issue with rssToJson from BER." detail="Could not process feed from the provided URL. Not a timeout or 404. BER Exception passed along in local.httpResult.error">
				</cfif>
			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage='RSS URL: #arguments.url#')>
			<cfset local.result.feedSuccess=false>
			<cfif not structKeyExists(local.result, "feedError")>
				<cfset local.result.feedError=cfcatch>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.result>
	</cffunction>


	<cffunction name="getCurrentRequestTimeout" output="false" access="public" returntype="numeric">
		<cfset var rcMonitor = createObject("java", "coldfusion.runtime.RequestMonitor")>
		<cfreturn rcMonitor.getRequestTimeout()>
	</cffunction>

	<cffunction name="offloadDirectoryListing" output="false" access="public" returntype="struct">
		<cfargument name="directory" required="true" type="String">
		<cfargument name="sort" required="false" type="String" default="name">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.directory = ARGUMENTS.directory>
		
		<cftry>
			<cfdirectory action="list" directory="#arguments.directory#" name="local.returnStruct.qryResults" sort="#arguments.sort#">
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="thumborImageInfo" output="false" access="public" returntype="struct">
		<cfargument name="filePath" required="true" type="String">
		<cfargument name="command" required="false" type="String" default="">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.forwardSlashedUserassets = replace(application.paths.RAIDUserAssetRoot.path,"\","/","all")>
		<cfset local.forwardSlashedSharedTempNoWeb = replace(application.paths.SharedTempNoWeb.path,"\","/","all")>
		<cfset local.deleteSourceFile = false>

		<cftry>
				<cfset local.filePath = replace(arguments.filePath,"\","/","all")>
				<cfset local.filename = listLast(local.filePath,"/")>

				<!--- EncodeForURL encodes spaces as '+'. If '%20' is desired, then replace '+' with '%20' after encoding the string --->
				<cfset local.isURLSafe = (local.filename eq replace(encodeForURL(local.filename),'+','%20','ALL'))>

				<cfset local.assetsURL = replaceNoCase(application.paths.internalPlatform.url,"*SITECODE*","mc")>

				<cfif findnocase(local.forwardSlashedUserassets,local.filePath) and local.isURLSafe>
					<cfset local.relativeFilePath = replaceNoCase(local.filePath,local.forwardSlashedUserassets,"")>
					<cfset local.inputFileURL = "#local.assetsURL#userassets/#local.relativeFilePath#">
				<cfelseif findnocase(local.forwardSlashedSharedTempNoWeb,local.filePath) and local.isURLSafe>
					<cfset local.relativeFilePath = replaceNoCase(local.filePath,local.forwardSlashedSharedTempNoWeb,"")>
					<cfset local.inputFileURL = "#local.assetsURL##application.paths.SharedTempNoWeb.path##local.relativeFilePath#">
				<cfelse>
					<cfset local.deleteSourceFile = true>
					<cfset local.processingFolder = replace(application.paths.SharedTempNoWeb.path & "thumbortemp/",'\','/','all')>
					<cfif not directoryExists(local.processingFolder)>
						<cftry>
							<cfdirectory action="CREATE" directory="#local.processingFolder#" mode="777">
							<cfcatch type="any"></cfcatch>
						</cftry>
					</cfif>
					<cfset local.inputFileName = createUUID()>
					<!--- Add Source Extension --->
					<cfif listLen(arguments.filePath,".") gt 1>
						<cfset local.inputFileName = listAppend(local.inputFileName, listLast(arguments.filePath,"."),".")>
					</cfif>
					<cfset local.inputFilePath = local.processingFolder & local.inputFileName>
					<cffile action="copy" source="#ARGUMENTS.filePath#" destination="#local.inputFilePath#">
					<cfset local.inputFileURL = "#local.assetsURL##local.inputFilePath#">
				</cfif>
				<cfset local.inputFileURL = replace(encodeForURL(replacenocase(local.inputFileURL,"http://","")),'+','%20','ALL')>
				<cfif len(arguments.command)>
					<cfset local.thumborRequestURL = "#application.paths.thumbor.internalUrl#unsafe/meta/#arguments.command#/#local.inputFileURL#">
				<cfelse>
					<cfset local.thumborRequestURL = "#application.paths.thumbor.internalUrl#unsafe/meta/#local.inputFileURL#">
				</cfif>
				<cfhttp throwonerror="true" url="#local.thumborRequestURL#" method="get" result="local.cfhttp" encodeurl="false"></cfhttp>
				<cfset local.returnStruct.httpResponse = deserializeJSON(local.cfhttp.filecontent)/>
				<cfif isStruct(local.returnStruct.httpResponse.thumbor)>
					<cfif local.deleteSourceFile>
						<cffile action="delete" file="#local.inputFilePath#">
					</cfif>
					<cfset local.returnStruct.success = true>
					<cfset local.returnStruct.imageInfo = local.returnStruct.httpResponse.thumbor>
				<cfelse>
					<cfset local.returnStruct.success = false>
				</cfif>
			<cfcatch type="any">
				<cfset local.returnStruct.success = false>
				<!--- No alert for 400 error codes, might represent invalid image --->
				<cfif not isdefined("local.cfhttp.status_Code") or not left(local.cfhttp.status_Code,1) eq "4">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfif>
			</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="thumborImageTranform" output="false" access="public" returntype="struct">
		<cfargument name="filePath" required="true" type="String">
		<cfargument name="outputFilePath" required="true" type="String">
		<cfargument name="command" required="true" type="String">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {success = false}>
		<cfset local.forwardSlashedUserassets = replace(application.paths.RAIDUserAssetRoot.path,"\","/","all")>
		<cfset local.forwardSlashedSharedTempNoWeb = replace(application.paths.SharedTempNoWeb.path,"\","/","all")>
		<cfset local.deleteSourceFile = false>

		<cftry>
			<cfset local.filePath = replace(arguments.filePath,"\","/","all")>
			<cfset local.filename = listLast(local.filePath,"/")>
			
			<!--- EncodeForURL encodes spaces as '+'. If '%20' is desired, then replace '+' with '%20' after encoding the string --->
			<cfset local.isURLSafe = (local.filename eq replace(encodeForURL(local.filename),'+','%20','ALL'))>

			<cfset local.assetsURL = replaceNoCase(application.paths.internalPlatform.url,"*SITECODE*","mc")>

			<cfif findnocase(local.forwardSlashedUserassets,local.filePath) and local.isURLSafe>
				<cfset local.relativeFilePath = replaceNoCase(local.filePath,local.forwardSlashedUserassets,"")>
				<cfset local.inputFileURL = "#local.assetsURL#userassets/#local.relativeFilePath#">
			<cfelseif findnocase(local.forwardSlashedSharedTempNoWeb,local.filePath) and local.isURLSafe>
				<cfset local.relativeFilePath = replaceNoCase(local.filePath,local.forwardSlashedSharedTempNoWeb,"")>
				<cfset local.inputFileURL = "#local.assetsURL##application.paths.SharedTempNoWeb.path##local.relativeFilePath#">
			<cfelse>
				<cfset local.deleteSourceFile = true>
				<cfset local.processingFolder = replace(application.paths.SharedTempNoWeb.path & "thumbortemp/",'\','/','all')>
				<cfif not directoryExists(local.processingFolder)>
					<cftry>
						<cfdirectory action="CREATE" directory="#local.processingFolder#" mode="777">
						<cfcatch type="any"></cfcatch>
					</cftry>
				</cfif>
				<cfset local.inputFileName = createUUID()>
				<!--- Add Source Extension --->
				<cfif listLen(arguments.filePath,".") gt 1>
					<cfset local.inputFileName = listAppend(local.inputFileName, listLast(arguments.filePath,"."),".")>
				</cfif>
				<cfset local.inputFilePath = local.processingFolder & local.inputFileName>
				<cffile action="copy" source="#ARGUMENTS.filePath#" destination="#local.inputFilePath#">
				<cfset local.inputFileURL = "#local.assetsURL##local.inputFilePath#">
			</cfif>
			<cfset local.inputFileURL = replace(encodeForURL(replacenocase(local.inputFileURL,"http://","")),'+','%20','ALL')>

			<cfif len(arguments.command)>
				<cfset local.thumborRequestURL = "#application.paths.thumbor.internalUrl#unsafe/#arguments.command#/#local.inputFileURL#">
			<cfelse>
				<cfset local.thumborRequestURL = "#application.paths.thumbor.internalUrl#unsafe/#local.inputFileURL#">
			</cfif>
			<cfhttp throwonerror="true" url="#local.thumborRequestURL#" method="get" result="local.cfhttp" encodeurl="false"></cfhttp>
			<cfif local.cfhttp.status_Code eq "200">
				<cffile action="write" file="#arguments.outputfilePath#" nameconflict="overwrite" output="#ToBinary(local.cfhttp.fileContent)#">
				<cfif local.deleteSourceFile>
					<cffile action="delete" file="#local.inputFilePath#">
				</cfif>
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<!--- No alert for 400 error codes, might represent invalid image --->
			<cfif not isdefined("local.cfhttp.status_Code") or not left(local.cfhttp.status_Code,1) eq "4">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getManualCropPointsForThumbor" output="false" access="public" returntype="string">
		<cfargument name="x" required="true" type="number">
		<cfargument name="y" required="true" type="number">
		<cfargument name="width" required="true" type="number">
		<cfargument name="height" required="true" type="number">

		<cfset local.x2 = arguments.x + arguments.width>
		<cfset local.y2 = arguments.y + arguments.height>

		<cfset local.command = "#arguments.x#x#arguments.y#:#local.x2#x#local.y2#">
		
		<cfreturn local.command>
	</cffunction>	

	<cffunction name="runBirtReport" output="false" access="public" returntype="struct">
		<cfargument name="reportPath" required="true" type="String">
		<cfargument name="outputFilePath" required="true" type="String">
		<cfargument name="reportParams" required="true" type="array" hint="array of structs with each element containing name, value, datatype, and needsEvaluation keys">
		<cfargument name="renderFormat" required="true" type="string" default="pdf" hint="PDF, DOCX, HTML, XLSX -- depending on what report supports">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cftry>
			<cfset local.processingFolder = replace(application.paths.SharedTempNoWeb.path & "report/",'\','/','all')>

			<cfset local.forwardSlashedUserassets = replace(application.paths.RAIDUserAssetRoot.path,"\","/","all")>
			<cfset local.forwardSlashedSharedTempNoWeb = replace(application.paths.SharedTempNoWeb.path,"\","/","all")>


			<cfif arrayLen(arguments.reportParams)>
				<cfloop index="local.thisParam" array="#arguments.reportParams#">
					<cfif (not structKeyExists(local.thisParam,"name") or not structKeyExists(local.thisParam,"value") or not structKeyExists(local.thisParam,"datatype") or not structKeyExists(local.thisParam,"needsEvaluation"))>
						<cfset local.returnStruct.success = false>
						<cfset local.returnStruct.message = "Report Params Array not in expected format. Should be an array of structs with each array element containing the following keys: name, value, datatype, needsEvaluation">
						<cfbreak/>
					</cfif>
					<cfif local.thisParam.datatype eq "filePath">
						<cfset local.thisParam.value = replace(local.thisParam.value,"\","/","all")>
						<cfif findnocase(local.forwardSlashedUserassets,local.thisParam.value)>
							<cfset local.thisParam.value = replaceNoCase(local.thisParam.value,local.forwardSlashedUserassets,"")>
							<cfset local.thisParam.networkShare = "userAssets">
						<cfelseif findnocase(local.forwardSlashedSharedTempNoWeb,local.thisParam.value)>
							<cfset local.thisParam.value = replaceNoCase(local.thisParam.value,local.forwardSlashedSharedTempNoWeb,"")>
							<cfset local.thisParam.networkShare = "SharedTempNoWeb">
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
			<cfset local.urlStruct = {
				reportPath=arguments.reportPath,
				renderFormat=arguments.renderFormat,
				reportParams=arguments.reportParams
			}>
			<cfset local.JSONString = serializeJSON(local.urlStruct)>
			<cfset local.encryptString = encrypt(local.JSONString,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>

			<cfhttp url="#application.paths.backendPlatform.internalUrl#tasks/reportBatch/runReport/" method="post" result="local.cfhttp" charset="utf-8">
				<cfhttpparam type="FORMFIELD" name="id" value="#local.encryptString#">
			</cfhttp>
			<cfset local.returnStruct.httpResponse = deserializeJSON(local.cfhttp.filecontent)/>
			<cfif local.returnStruct.httpResponse.success>
				<cfset local.destinationPath = local.processingFolder & local.returnStruct.httpResponse.destinationFile>
				<cffile action="move" source="#local.destinationPath#" destination="#ARGUMENTS.outputFilePath#" nameconflict="overwrite">
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>

			<cfcatch type="any">
				<cfset local.returnStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSponsorAds" output="false" access="public" returntype="struct">
		<cfargument name="zoneType" type="string" required="true" hint="zone type for ads">
		<cfargument name="numberOfAds" type="numeric" required="false" default="5">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="uniqueCode" type="string" required="true">
		<cfargument name="advanceRotation" type="boolean" required="true">
		<cfargument name="baseurl" type="string" required="true">
		

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.arrAds = arrayNew(1)>

		<cfset local.adBaseURL = "#application.paths.backendPlatform.url#?event=ads.click">
		<cfset local.adImageBaseURL = arguments.baseurl>

		<cftry>
			<cfscript>
				local.objAdDelivery = createObject("component","model.system.platform.adDelivery");
				local.ads = local.objAdDelivery.getAds(zonetype=arguments.zonetype, sitecode=arguments.sitecode, numberOfAds=arguments.numberOfAds, uniqueCode=arguments.uniqueCode, advanceRotation=arguments.advanceRotation)
				for (local.row in local.ads) { 
					local.adLink = "";
					local.imageURL = "";

					if (local.row.adLink.len()) {
						local.adLink = local.objAdDelivery.getEncodedUrl(
							baseUrl=local.adBaseURL, AdID=local.row.adID, AdName=local.row.AdName, AdLink=local.row.adLink,
							imageURL=local.row.imageURL, el=local.row.adShortText, orgname=local.row.orgName, 
							UACode=local.row.googleUACode, zoneName=local.row.zoneName, zoneTypeName=local.row.zoneTypeName,cid=0)
					}
					if (local.row.imageURL.len()) {
						local.imageURL = replace(local.adImageBaseURL & local.row.imageURL,"/" & local.row.imageURL,local.row.imageURL,"all")
					}
					local.thisAd = {
						AdID=local.row.adID,
						imageurl=local.imageURL,
						width=local.row.imageWidth,
						height=local.row.imageHeight,
						adlink=local.adLink,
						adShortText=local.row.adShortText,
						googleUACode=local.row.googleUACode,
						orgname=local.row.orgname,
						adname=local.row.adname,
						zonename=local.row.zonename,
						zoneTypename=local.row.zoneTypename
					};
					local.returnStruct.arrAds.append(local.thisAd)
				}
				local.returnStruct.success = true;
			</cfscript> 
			<cfcatch type="any">
				<cfset local.returnStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getDocumentCount" access="remote" returntype="string" output="false">
		<cfquery name="getCount" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			Select doccount + cushion as doccount from databasesize where name = 'documents'
		</cfquery>
		<cfreturn numberformat(getCount.doccount,",") />
	</cffunction>
	
	<cffunction name="getMessageCount" access="remote" returntype="string" output="false">
		<cfquery name="getCount" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			Select doccount + cushion as doccount from databasesize where name = 'messages'
		</cfquery>
		<cfreturn numberformat(getCount.doccount,",") />
	</cffunction>

	<cffunction name="getSearchCount" access="remote" returntype="string" output="false">
		<cfquery name="getCount" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			Select doccount + cushion as doccount from databasesize where name = 'searches'
		</cfquery>
		<cfreturn numberformat(getCount.doccount,",") />
	</cffunction>

	<cffunction name="OnMissingMethod" access="public" returntype="any" output="false" hint="Handles missing method exceptions.">
		<cfargument name="MissingMethodName" type="string" required="true" hint="The name of the missing method." />
		<cfargument name="MissingMethodArguments" type="struct" required="true" hint="The arguments that were passed to the missing method. This might be a named argument set or a numerically indexed set."/>

		<cfset var freshObj = "">
		<cfset var freshFunction = "">
		<cflock name="tsCommonOnMissingMethod" timeout="3">
			<cfscript>
			//race condition: make sure another request didn't already inject function
			if (structKeyExists(this, arguments.MissingMethodName)) {
				freshFunction = this[arguments.MissingMethodName];
				return freshFunction(argumentCollection=arguments.MissingMethodArguments);
			} else {
				//Test if new function exists in cfc, but application scope hasn't been refreshed yet
				freshObj = new tsCommon();
				if (structKeyExists(freshObj, arguments.MissingMethodName)) {
					freshFunction = freshObj[arguments.MissingMethodName];
					structInsert(this,arguments.MissingMethodName,freshFunction);
					return freshFunction(argumentCollection=arguments.MissingMethodArguments);
				} else {
					throw(type="Missing Method", message="Missing method '#ARGUMENTS.missingMethodName#()'. Also checked disk to see if function exists in updated source file");	
				}
			}
			</cfscript>
		</cflock>
	</cffunction>

	<cffunction name="convertFileSeparator" access="public" returntype="string" output="no" hint="Convert filepath separator">
		<cfargument name="path" required="true" type="string">
		<cfargument name="separator" required="true" type="string" hint="new separator: / or \">
 		
 		<cfset var newpath = "">
 		
 		<cfif listfind("\,/",arguments.separator)>
			<cfset newpath = rereplace(arguments.path,'[/\\]',arguments.separator,"all")>
		<cfelse>
			<cfthrow type="" message="Invalid file separator" detail="convertFilePath only accepts a forward or back slash as valid separators">
		</cfif>

 		<cfreturn newpath>
 	</cffunction>

 	<cffunction name="getMCSystemMemberID" output="false" access="public" returntype="numeric">
		<cfset var qrySystemMember = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySystemMember" cachedwithin="#createTimeSpan(0,1,0,0)#">
			select dbo.fn_ams_getMCSystemMemberID() as memberID;
		</cfquery>

		<cfreturn local.qrySystemMember.memberID>
	</cffunction>

	<cffunction name="DateConvertISO8601" access="public" returntype="date" output="no">
		<cfargument name="ISO8601dateString" type="string" required="true">
		<cfargument name="targetZoneOffset" type="string" required="true">

		<cfset var rawDatetime = left(arguments.ISO8601dateString,10) & " " & mid(arguments.ISO8601dateString,12,8)>
	
		<!--- adjust offset based on offset given in date string --->
		<cfif uCase(mid(arguments.ISO8601dateString,20,1)) neq "Z">
			<cfset arguments.targetZoneOffset = arguments.targetZoneOffset - val(mid(arguments.ISO8601dateString,20,3))>
		</cfif>
	
		<cfreturn DateAdd("h", arguments.targetZoneOffset, CreateODBCDateTime(rawDatetime))>
	</cffunction>

	<cffunction name="thumbnailFromPDF" access="public" output="false" returntype="string">
		<cfargument name="pdfFile" type="string" required="true" hint="This is the physical file path to the PDF">
		<cfargument name="destination" type="string" required="false" default="" hint="Location to save the thumbbail, defaults to same location as original">
		<cfargument name="page" type="numeric" required="false" default="1" hint="page number to use for the thumbnail, defaults to page 1">
		<cfargument name="format" type="string" required="false" default="png" hint="thumbnail format, defaults to jpg">

		<cfscript>
			var BufferedImage = createObject("java","java.awt.image.BufferedImage");
			var ImageWriter = createObject("java","org.apache.pdfbox.util.PDFImageWriter");	

			if (!FileExists(arguments.pdfFile)) {
				cfthrow(message="No source file found.");
			}
			
			// load the pdf into PDFBox
			var document = createObject("java","org.apache.pdfbox.pdmodel.PDDocument").load("#arguments.pdfFile#");	

			arguments.pdfFile = convertFileSeparator(path=arguments.pdfFile, separator='/');

			// get the base paths file names needed
			var basefile = listlast(arguments.pdfFile,'/');
			var basefilename = replacenocase(basefile,".pdf","","ALL");
			var basepath = replacenocase(arguments.pdfFile,basefile,"");
			if (arguments.destination IS '') {
				arguments.destination = basepath;
			}
			if (right(arguments.destination,1) Neq '/') {
				arguments.destination = arguments.destination & '/';
			}
			var returnfileprefix = "#arguments.destination##basefilename#_"; //includes dest path
			var returnfilename = "#basefilename#_#arguments.page#.#arguments.format#";

			//check file extension
			if (right(arguments.pdfFile,4) Neq ".pdf") {
				cfthrow(message="File does not appear to be a PDF file.");
			}

			if (!['png','jpg'].contains(arguments.format)) {
				cfthrow(message="format must be png or jpg. Received #format#");
			};

			//write the image of page 1. This writes a file called 1.jpg. There is prefix argument and in this case we use the file name as the prefix.
			// arguments... writeImage(document,image type,start page, end page, prefix to <page number>.jpg, java color profile, resolution)
			imageWriter.writeImage(document, JavaCast("string",arguments.format), JavaCast("string",""), arguments.page, arguments.page, JavaCast("string",returnfileprefix), BufferedImage.TYPE_INT_RGB, 96);

			// close document object, no longer needed
			document.close();

			return returnfilename;
		</cfscript>
	</cffunction>

	<cffunction name="getCleanContentData" access="public" output="false" returntype="string">
		<cfargument name="HTMLContent" type="string" required="true">
		<cfscript>
			var local = {};

			local.cacheKeyPrefix = 'getCleanContentData-' & variables.objectTimeStamp;
			local.argumentsHash = hash(serializeJSON(arguments));
			local.cacheKey = local.cacheKeyPrefix & '-' & local.argumentsHash;
			local.cacheResult = false;
			local.previousCachedResultsFound = false;

			try {
				local.resultContent = application.mcCacheManager.clusterGetValue(keyname=local.cacheKey);
				local.cacheResult = false;
				local.previousCachedResultsFound = true;
			} catch(e) {
				local.previousCachedResultsFound = false;
				local.cacheResult = true;
				//  Comment out this error so we are notified everytime an object is not in cache.
				// application.objError.sendError(cfcatch=e, objectToDump=local, customMessage="Object not found in cache");
			}

			if (not local.previousCachedResultsFound) {
				// allowedAttributes: false  means that ALL attributes are allowed
				local.sanitizeOptions = {
					"allowedTags": [ 'a','abbr','address','article','aside','b','bdi','bdo','blockquote','br','caption','cite','code','col','colgroup','data','dd','dfn','div','dl','dt','em','figcaption','figure','footer','h1','h2','h3','h4','h5','h6','header','hgroup','hr','i','iframe','img','kbd','li','main','mark','nav','ol','p','pre','q','rb','rp','rt','rtc','ruby','s','samp','script','section','small','span','strike','strong','style','sub','sup','table','tbody','td','tfoot','th','thead','time','tr','u','ul','var','wbr','html','head','title','body','link','base','meta' ],
					"disallowedTagsMode": 'discard',
					"allowedAttributes": false,
					"transformTags": {
						"a": "function(tagName, attribs) {
									if (typeof attribs.href != 'undefined')
										attribs.href = attribs.href.replace(/\s/g,'%20');
									return {
										tagName: 'a',
										attribs: attribs
									}
								}",
						"img": "function(tagName, attribs) {
									if (typeof attribs.src != 'undefined')
										attribs.src = attribs.src.replace(/\s/g,'%20');
									return {
										tagName: 'img',
										attribs: attribs
									}
								}"
					}
				};
				local.resultContent = arguments.HTMLContent;
				local.sanitizeDetails = sanitizeHTML(dirtyHTML=local.resultContent, sanitizeOptions=local.sanitizeOptions);
				if (local.sanitizeDetails.success) {
					local.resultContent = local.sanitizeDetails.content;

					if (local.cacheResult) {
						application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.resultContent, timespan=createtimespan(7,0,0,0));
					}
				}
			}

			return local.resultContent;
		</cfscript>
	</cffunction>

	<cffunction name="sanitizeHTML" access="public" output="false" returntype="struct">
		<cfargument name="dirtyHTML" type="string" required="true">
		<cfargument name="sanitizeOptions" type="struct" required="false" default="#structNew()#">

		<cfscript>
			var local = {};
			local.returnStruct = {};
			local.returnStruct.success = false;
			try {
				local.cacheKeyPrefix = 'sanitizeHTML-' & variables.objectTimeStamp;
				local.argumentsHash = hash(serializeJSON(arguments));
				local.cacheKey = local.cacheKeyPrefix & '-' & local.argumentsHash;
				local.cacheResult = false;
				local.previousCachedResultsFound = false;

				try {
					local.returnStruct.content = application.mcCacheManager.clusterGetValue(keyname=local.cacheKey);
					local.cacheResult = false;
					local.previousCachedResultsFound = true;
				} catch(e) {
					local.previousCachedResultsFound = false;
					local.cacheResult = true;
					//  Comment out this error so we are notified everytime an object is not in cache.
					// application.objError.sendError(cfcatch=e, objectToDump=local, customMessage="Object not found in cache");
				}

				if (not local.previousCachedResultsFound) {
					local.args = { "dirty" = arguments.dirtyHTML };
					if (isDefined("arguments.sanitizeOptions") and isStruct(arguments.sanitizeOptions))
						local.args["sanitizeOptions"] = arguments.sanitizeOptions;
					local.jsonargs = serializeJSON(local.args);
					local.url = "#application.paths.templateProcessor.internalUrl#sanitize";
				
					cfhttp(method="post", url=local.url, result="local.CallResult", throwonerror="true", charset="utf-8") {
						cfhttpparam(type="HEADER", name="Content-Type", value="text/plain");
						cfhttpparam(type="body", value="#local.jsonargs#");
					}

					local.returnStruct.content = local.CallResult.fileContent.trim();
					if (local.cacheResult) {
						application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.returnStruct.content, timespan=createtimespan(7,0,0,0));
					}
				}
				local.returnStruct.success = true;
			} catch(local.cfcatch) {
				local.returnStruct.success = false;
				if (structKeyExists(local,"CallResult") and structKeyExists(local.CallResult,"status_code") and local.CallResult.status_code eq "422")
					local.returnStruct.content = "<p>Your HTML body couldn't be cleaned due to a syntax error</p><p>#local.CallResult.filecontent#</p>";
				else
					local.returnStruct.content = "<p>Your HTML body couldn't be cleaned due to a server error</p>";
				application.objError.sendError(cfcatch=local.cfcatch,objectToDump=local);
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="cleanLyrisMessage" access="public" output="false" returntype="struct">
		<cfargument name="dirtyHTML" type="string" required="true">		
		<cfscript>
			var local = {};
			local.returnStruct = {};
			local.returnStruct.success = false;
			try {
				local.cacheKeyPrefix = 'cleanLyrisMessage-' & variables.objectTimeStamp;
				local.argumentsHash = hash(serializeJSON(arguments));
				local.cacheKey = local.cacheKeyPrefix & '-' & local.argumentsHash;
				local.cacheResult = false;
				local.previousCachedResultsFound = false;

				try {
					local.returnStruct.content = application.mcCacheManager.clusterGetValue(keyname=local.cacheKey);
					local.cacheResult = false;
					local.previousCachedResultsFound = true;
				} catch(e) {
					local.previousCachedResultsFound = false;
					local.cacheResult = true;
					//  Comment out this error so we are notified everytime an object is not in cache.
					// application.objError.sendError(cfcatch=e, objectToDump=local, customMessage="Object not found in cache");
				}

				if (not local.previousCachedResultsFound) {
					local.args = { "dirty" = arguments.dirtyHTML };
					local.jsonargs = serializeJSON(local.args);
					local.url = "#application.paths.templateProcessor.internalUrl#cleanlyrismessage";

					cfhttp(method="post", url=local.url, result="local.CallResult", throwonerror="true", charset="utf-8") {
						cfhttpparam(type="HEADER", name="Content-Type", value="text/plain");
						cfhttpparam(type="body", value="#local.jsonargs#");
					}

					local.returnStruct.content = local.CallResult.fileContent.trim();
					if (local.cacheResult) {
						application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.returnStruct.content, timespan=createtimespan(7,0,0,0));
					}
				}
				local.returnStruct.success = true;
			} catch(local.cfcatch) {
				local.returnStruct.success = false;
				if (structKeyExists(local,"CallResult") and structKeyExists(local.CallResult,"status_code") and local.CallResult.status_code eq "422")
					local.returnStruct.content = "<p>Your HTML body couldn't be cleaned due to a syntax error</p><p>#local.CallResult.filecontent#</p>";
				else
					local.returnStruct.content = "<p>Your HTML body couldn't be cleaned due to a server error</p>";
				application.objError.sendError(cfcatch=local.cfcatch,objectToDump=local);
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getMessageTypeIDFromMessageTypeCode" access="public" output="false" returntype="numeric">
		<cfargument name="messageTypeCode" type="string" required="true">

		<cfset var qryMessageTypeID = "">

		<cfquery name="qryMessageTypeID" datasource="#application.dsn.platformMail.dsn#">
			select messageTypeID
			from dbo.email_messageTypes 
			where messageTypeCode =  <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.messageTypeCode#">
		</cfquery>

		<cfreturn val(qryMessageTypeID.messageTypeID)>
	</cffunction>

	<cffunction name="getGoogleCalendarLink" access="public" output="false" returntype="string">
		<cfargument name="text" type="string" required="true">
		<cfargument name="details" type="string" required="true">
		<cfargument name="location" type="string" required="true">
		<cfargument name="website" type="string" required="true">
		<cfargument name="startDate" type="string" required="true">
		<cfargument name="endDate" type="string" required="true">
		<cfargument name="isAllDayEvent" type="boolean" required="true">
		<cfargument name="timeZone" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>

		<cfset local.qryTZ = local.objTSTZ.getTimeZones()>
		<cfquery name="local.qryTZ_selected" dbtype="query">
			select timeZoneCode
			from [local].qryTZ
			where timeZone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.timeZone#">
		</cfquery>
		
		<cfset local.utcStart = local.objTSTZ.convertTimeZone(dateToConvert=arguments.startDate, fromTimeZone=local.qryTZ_selected.timeZoneCode, toTimeZone="UTC")>
		<cfset local.utcEnd = local.objTSTZ.convertTimeZone(dateToConvert=arguments.endDate, fromTimeZone=local.qryTZ_selected.timeZoneCode, toTimeZone="UTC")>
		
		<cfif arguments.isAllDayEvent>
			<!--- Google Calendar all-day events (end date is exclusive). For eg: "start":"2025-09-01", "end":"2025-09-05" exclusive -> shows Sep 1 to Sep 4 --->
			<cfset local.gcalDates = DateFormat(local.utcStart, "yyyymmdd") & "/" & DateFormat(DateAdd('D', 1, local.utcEnd), "yyyymmdd")>
		<cfelse>
			<cfset local.gcalDates = DateTimeFormat(local.utcStart, "yyyyMMdd'T'HHnnss'Z'","UTC") & "/" & DateTimeFormat(local.utcEnd, "yyyyMMdd'T'HHnnss'Z'","UTC")>
		</cfif>

		<cfset local.linebreak = "#chr(13)##chr(10)#">
		<cfset local.sanitizeOptions = {
			"allowedTags": [],
			"allowedAttributes": {}
		}>
		<cfset local.sanitizeDetails = sanitizeHTML(dirtyHTML=trim(arguments.details), sanitizeOptions=local.sanitizeOptions)>
		<cfif local.sanitizeDetails.success>
			<cfset arguments.details = trim(rereplace(rereplace(local.sanitizeDetails.content,"\t","","ALL"),"(\r\n)+",local.linebreak,"ALL"))>
		</cfif>

		<!--- hotfix-391079 Reducing the details to 2000 leaving room for extra strings from Google sign in redirects --->
		<cfif len(arguments.details) GT 2000>
			<cfset arguments.details = LEFT(arguments.details,1997) & "...">
		</cfif>
		<cfset arguments.details = urlEncodedFormat(arguments.details)>
	
		<cfset arguments.text = URLEncodedFormat(htmleditformat(arguments.text))>
		<cfset local.gCalLink = "http://www.google.com/calendar/event?action=TEMPLATE&text=#arguments.text#&dates=#local.gcalDates#">
		<cfif len(arguments.website)>
			<cfset local.gCalLink = "#local.gCalLink#&sprop=website:#arguments.website#">
		</cfif>
		<cfset local.gCalLink = "#local.gCalLink#&details=#arguments.details#">
		<cfif len(arguments.location)>
			<cfset local.gCalLink = "#local.gCalLink#&location=#URLEncodedFormat(htmleditformat(left(arguments.location,500)))#">
		</cfif>
		
		<cfreturn local.gCalLink>
	</cffunction>

	<cffunction name="getTSStates" access="public" returntype="query" output="false">
		<cfset var qryStates = "">
		
		<cfquery name="qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#createTimeSpan(0,0,5,0)#">
			SELECT code, name
			FROM dbo.states
			ORDER BY orderpref, name;
		</cfquery>
	
		<cfreturn qryStates>
	</cffunction>

	<cffunction name="validateUIDList" access="public" returntype="boolean" output="false" hint="validates pipe-delimited list of UIDs">
		<cfargument name="inputString" type="string" required="true">
		<cfargument name="allowBlank" type="boolean" required="false" default="true">
		
		<cfset var local = structNew()>
		<cfset local.result = false>

		<cfif len(arguments.inputString)>
			<cfset local.UIDPattern = "[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}">
			<cfset local.UIDPatternWithOptionalBrackets = "([{]#local.UIDPattern#[}])|(#local.UIDPattern#)">
			<cfset local.regexUIDList = "^(#local.UIDPatternWithOptionalBrackets#)(\|(#local.UIDPatternWithOptionalBrackets#))*$">

			<cfif isValid("regex",arguments.inputString,local.regexUIDList)>
				<cfset local.result = true>
			</cfif>
		<cfelseif arguments.allowBlank>
			<cfset local.result = true>
		</cfif>
	
		<cfreturn local.result>
	</cffunction>

	<cffunction name="getValidatedEmailAddresses" access="public" output="false" returntype="string">
		<cfargument name="emailAddressList" type="string" required="true">
		<cfargument name="delimiter" type="string" required="false" default=",">

		<cfscript>
			var local = structNew();
			local.validatedEmailAddrList = "";
			
			if (len(arguments.emailAddressList)) {
				local.arrEmails = listToArray(replace(replace(arguments.emailAddressList,',',';','ALL'),' ','','ALL'),';');
				for (var i=1; i lte arrayLen(local.arrEmails); i++) {
					if (len(local.arrEmails[i]) and not isValid("regex",local.arrEmails[i],application.regEx.email)) {
						arrayDeleteAt(local.arrEmails,i);
					}
				}
				local.validatedEmailAddrList = arrayToList(local.arrEmails,arguments.delimiter);
			}

			return local.validatedEmailAddrList;
		</cfscript>
	</cffunction>

	<cffunction name="mc_gcv2verify" access="public" output="false" returntype="struct">
		<cfargument name="a" type="any" required="false" default="">
		<cfargument name="t" type="any" required="false" default="">

		<cfset var local = structNew()>

		<cfif isSimpleValue(arguments.a) and isSimpleValue(arguments.t) and len(arguments.a) and len(arguments.t)>
			<cftry>
				<cfhttp method="post" url="https://www.google.com/recaptcha/api/siteverify" useragent="MemberCentral.com" result="local.APIResult" timeout="60" throwonerror="true">
					<cfhttpparam type="formfield" name="secret" value="#application.strPlatformAPIKeys.googlerecaptchav2.secret#" />
					<cfhttpparam type="formfield" name="response" value="#arguments.t#" />
				</cfhttp>
				<cfset local.statusCode = local.APIResult.status_code>
				<cfset local.jsonResult = toString(trim(local.APIResult.fileContent))>
				<cfset local.strResult = deserializeJSON(local.jsonResult)>

				<cfset local.verify = local.strResult.success>
				<cfif local.strResult.success and local.strResult.hostname neq application.objPlatform.getCurrentHostname()>
					<cfset local.verify = "false">
				</cfif>
			<cfcatch type="Any">
				<cfset local.statusCode = 500>
				<cfset local.jsonResult = "{}">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

				<cfset local.verify = "false">
			</cfcatch>
			</cftry>

			<cftry>
				<cfset local.strRequest = {
					"c":"GoogleReCaptchaV2",
					"d": {
						"tier":application.MCEnvironment,
						"siteid":application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID,
						"statssessionid":session.cfcuser.statsSessionID?:0,
						"area":arguments.a,
						"timestamp":now(),
						"verify":local.verify,
						"request": {
							"token":arguments.t
						},
						"response": {
							"statuscode":local.statusCode,
							"bodycontent":local.jsonResult
						}
					}
				}>
		
				<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.platformQueue.dsn#">
					INSERT INTO dbo.queue_mongo (msgjson) 
					VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
				</cfquery>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.verify = "false">
		</cfif>

		<cfreturn { "verify":local.verify }>
	</cffunction>

	<cffunction name="generateScreenshot" access="public" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="false" default="#session.mcstruct.siteCode#">
		<cfargument name="htmlContent" type="string" required="true" hint="html content to generate screenshot">
		<cfargument name="destination" type="string" required="false" default="" hint="location to save the screenshot">
		<cfargument name="viewportWidth" type="numeric" required="true">
		<cfargument name="viewportHeight" type="numeric" required="true">
		<cfargument name="viewportDeviceScaleFactor" type="numeric" required="true">
		<cfargument name="fullpage" type="boolean" required="true">
		<cfargument name="additionalOptions" type="struct" default="#structNew()#" required="false">

		<cfset var local = {}>
		<cfsavecontent variable="local.screenshotreadyJS">
			<cfoutput>
			<script type="text/javascript">
			const maximumAllowedWaitTimeMS = 5000;
			const maximumPageHeightPx = 5000;
			var screenshotReadyCalled = false;
		
			const screenshotReady = function () {
				if (!screenshotReadyCalled) {
					screenshotReadyCalled = true;

					// wrap a div around everything in the body that hides any content past the max height point 
					let maxPageHeightwrapper = document.createElement('div');
					maxPageHeightwrapper.setAttribute("style",`max-height:${maximumPageHeightPx}px;overflow:hidden;`);
					let existingItems = Array.from(document.getElementsByTagName('body')[0].children)
					document.getElementsByTagName('body')[0].prepend(maxPageHeightwrapper)
					existingItems.forEach((element) => {maxPageHeightwrapper.append(element)})

					// add the element that triggers the screenshot
					let readyDiv = document.createElement("div");
					readyDiv.setAttribute("id","MCscreenshotIsReady");
					document.querySelector("body").appendChild(readyDiv);
				}
			}
			// run function immediately when all content finishing loading
			window.onload = screenshotReady;
			// run function after our max waiting time, regardless if content is still loading
			setTimeout(screenshotReady,maximumAllowedWaitTimeMS);
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfscript>
			try {
				local.returnStruct = {success=false};
				if (NOT len(arguments.destination)) {
					local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.sitecode);
					arguments.destination = local.strFolder.folderPath;
				}
				if (right(arguments.destination,1) NEQ '/') {
					arguments.destination = arguments.destination & '/';
				}
				local.siteID = application.objSiteInfo.getSiteInfo(arguments.siteCode).siteID;
				local.imageURL = replaceNoCase(application.paths.internalPlatform.assetsurl,"*SITECODE*",arguments.siteCode);
				local.sourceHtml = application.objResourceRenderer.qualifyAllLinks(content=arguments.htmlContent, siteID=local.siteID, qualURL=local.imageURL);

				// inject screenshot ready javascript
				local.sourceHtml = local.sourceHtml.rereplacenocase("(</body\b[^>]*>)","#local.screenshotreadyJS#\1");
				local.screenshotImgFilePath = arguments.destination & 'screenshot.jpg';
				
				local.strScreenshotCommand = {
					"output": "screenshot",
					"html": local.sourceHtml,
					"ignoreHttpsErrors": true,
					"waitFor": "##MCscreenshotIsReady",
					"goto": {
						"waitUntil" : "domcontentloaded"
					},
					"screenshot": {
						"fullPage": arguments.fullpage ? true : false,
						"type": "jpeg"
					},
					"viewport": {
						"width": arguments.viewportWidth,
						"height": arguments.viewportHeight,
						"deviceScaleFactor": arguments.viewportDeviceScaleFactor
					}
				};

				if (arguments.additionalOptions.keyExists('screenshot')) {
					if (arguments.additionalOptions.screenshot.keyExists('fullpage') AND isValid("boolean",arguments.additionalOptions.screenshot.fullpage))
						local.strScreenshotCommand.screenshot['fullPage'] = arguments.additionalOptions.screenshot.fullpage ? true : false;

					if (arguments.additionalOptions.screenshot.keyExists('quality') AND val(arguments.additionalOptions.screenshot.quality))
						local.strScreenshotCommand.screenshot['quality'] = val(arguments.additionalOptions.screenshot.quality);

					if (arguments.additionalOptions.screenshot.keyExists('omitBackground') AND isValid("boolean",arguments.additionalOptions.screenshot.omitBackground))
						local.strScreenshotCommand.screenshot['omitBackground'] = arguments.additionalOptions.screenshot.omitBackground ? true : false;

					if (arguments.additionalOptions.screenshot.keyExists('clip')) {
						if (arguments.additionalOptions.screenshot.clip.keyExists('x') AND val(arguments.additionalOptions.screenshot.clip.x))
							local.strScreenshotCommand.screenshot.clip['x'] = val(arguments.additionalOptions.screenshot.clip.x);

						if (arguments.additionalOptions.screenshot.clip.keyExists('y') AND val(arguments.additionalOptions.screenshot.clip.y))
							local.strScreenshotCommand.screenshot.clip['y'] = val(arguments.additionalOptions.screenshot.clip.y);

						if (arguments.additionalOptions.screenshot.clip.keyExists('width') AND val(arguments.additionalOptions.screenshot.clip.width))
							local.strScreenshotCommand.screenshot.clip['width'] = val(arguments.additionalOptions.screenshot.clip.width);

						if (arguments.additionalOptions.screenshot.clip.keyExists('height') AND val(arguments.additionalOptions.screenshot.clip.height))
							local.strScreenshotCommand.screenshot.clip['height'] = val(arguments.additionalOptions.screenshot.clip.height);

						if (arguments.additionalOptions.screenshot.clip.keyExists('selector') AND len(arguments.additionalOptions.screenshot.clip.selector))
							local.strScreenshotCommand.screenshot.clip['selector'] = arguments.additionalOptions.screenshot.clip.selector;
					}
				}
				
				if (arguments.additionalOptions.keyExists('viewport')) {
					if (arguments.additionalOptions.viewport.keyExists('width') AND val(arguments.additionalOptions.viewport.width))
						local.strScreenshotCommand.viewport['width'] = val(arguments.additionalOptions.viewport.width);
				
					if (arguments.additionalOptions.viewport.keyExists('height') AND val(arguments.additionalOptions.viewport.height))
						local.strScreenshotCommand.viewport['height'] = val(arguments.additionalOptions.viewport.height);
				
					if (arguments.additionalOptions.viewport.keyExists('deviceScaleFactor') AND val(arguments.additionalOptions.viewport.deviceScaleFactor))
						local.strScreenshotCommand.viewport['deviceScaleFactor'] = val(arguments.additionalOptions.viewport.deviceScaleFactor);
				
					if (arguments.additionalOptions.viewport.keyExists('isMobile') AND isValid("boolean",arguments.additionalOptions.viewport.isMobile))
						local.strScreenshotCommand.viewport['isMobile'] = arguments.additionalOptions.viewport.isMobile ? true : false;

					if (arguments.additionalOptions.viewport.keyExists('hasTouch') AND isValid("boolean",arguments.additionalOptions.viewport.hasTouch))
						local.strScreenshotCommand.viewport['hasTouch'] = arguments.additionalOptions.viewport.hasTouch ? true : false;

					if (arguments.additionalOptions.viewport.keyExists('isLandscape') AND isValid("boolean",arguments.additionalOptions.viewport.isLandscape))
						local.strScreenshotCommand.viewport['isLandscape'] = arguments.additionalOptions.viewport.isLandscape ? true : false;
				}

				local.returnStruct.screenshotImgFilePath = local.screenshotImgFilePath;				

				local.screenshotResponse = {};
				local.jsonScreenshotCommand = serializeJson(local.strScreenshotCommand);

				
				filewrite(file="#arguments.destination#screenshot.html", data="#local.sourceHtml#");
				filewrite(file="#arguments.destination#screenshot.json", data="#local.jsonScreenshotCommand#");

				cfhttp(method="post", url="#application.paths.screenshots.internalUrl#/api/render", result="local.screenshotResponse", throwonerror="true") {
					cfhttpparam(type="header", name="Content-Type", value="application/json");
					cfhttpparam(type="body",  value=local.jsonScreenshotCommand);
				}

				// success
				if (local.screenshotResponse.status_Code eq "200") {
					filewrite(file="#local.screenshotImgFilePath#", data="#ToBinary(local.screenshotResponse.fileContent)#");
					local.returnStruct.success=true;
				}
			} catch(e) {
				application.objError.sendError(cfcatch=cfcatch, objectToDump={local=local, arguments=arguments});
			}

			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="checkBillingZIP" access="public" output="false" returntype="struct">
		<cfargument name="billingZip" type="string" required="true">
		<cfargument name="billingStateID" type="numeric" required="false" default="0">
		<cfargument name="billingState" type="string" required="false" default="">

		<cfset var qryCheckZIP = "">

		<cfif (arguments.billingStateID EQ 0 AND NOT len(arguments.billingState)) OR arguments.billingZip.len() EQ 0>
			<cfreturn { "isvalidzip":false }>
		</cfif>

		<cfquery name="qryCheckZIP" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @stateCode char(2), @countryID int, @isValidZip bit = 0,
				@zipCode varchar(5) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(arguments.billingZip),5)#">;

			<cfif arguments.billingStateID>
				SELECT @stateCode = [Code], @countryID = countryID
				FROM membercentral.dbo.ams_states
				WHERE stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.billingStateID#">;
			<cfelse>
				SET @stateCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">;
				
				SELECT TOP 1 @countryID = countryID 
				FROM membercentral.dbo.ams_states 
				WHERE code = @stateCode
				ORDER BY countryID;
			</cfif>

			-- US State
			IF @countryID = 1 AND EXISTS (SELECT 1 FROM dbo.avalaraTaxTable WHERE [state] = @stateCode AND zipCode = @zipCode)
				SET @isValidZip = 1;

			-- Other Countries
			IF @isValidZip = 0 AND @countryID > 1
				SET @isValidZip = 1;

			SELECT @isValidZip AS isValidZip, @countryID AS countryID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var validatedBillingZip = "">
		<cfif qryCheckZIP.isValidZip EQ 1>
			<cfset validatedBillingZip = qryCheckZIP.countryID EQ 1 ? left(trim(arguments.billingZip),5) : arguments.billingZip>
		</cfif>

		<cfreturn { "isvalidzip":qryCheckZIP.isValidZip EQ 1, "billingzip":validatedBillingZip }>
	</cffunction>

	<cffunction name="getToolFiltersData" access="public" output="false" returntype="struct">
		<cfargument name="keyname" type="string" required="true">
		<cfargument name="defaultFilterData" type="struct" required="true">

		<cfset var local = {}>

		<!--- defaults --->
		<cfset local.filterData = duplicate(arguments.defaultFilterData)>

		<!--- saved filters --->
		<cfset local.savedFilterData = application.mcCacheManager.sessionGetValue(keyname="toolfilter_#arguments.keyname#", defaultValue={})>

		<!--- Merge in saved filters, overwriting defaults --->
		<cfset structAppend(local.filterData, local.savedFilterData, true)>

		<cfreturn local.filterData>
	</cffunction>

	<cffunction name="saveToolFiltersData" access="public" output="false" returntype="struct">
		<cfargument name="mc_filterkeyname" type="string" required="true">

		<cfset var local = {}>

		<cfset local.data = duplicate(arguments)>
		<cfloop list="mcproxy_orgCode,mcproxy_siteID,mcproxy_siteCode,mcproxy_orgID,mcproxy_memberID,mc_filterkeyname" item="local.thisItem">
			<cfif local.data.keyExists(local.thisItem)>
				<cfset structDelete(local.data,local.thisItem)>
			</cfif>
		</cfloop>

		<cfif NOT isSimpleValue(arguments.mc_filterkeyname) OR NOT len(arguments.mc_filterkeyname) OR structIsEmpty(local.data)>
			<cfreturn { "success": false }>
		</cfif>

		<cfset application.mcCacheManager.sessionSetValue(keyname="toolfilter_#arguments.mc_filterkeyname#", value=local.data)>

		<cfreturn { "success": true }>
	</cffunction>

</cfcomponent>
