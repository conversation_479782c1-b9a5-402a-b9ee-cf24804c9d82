<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/address</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>type</i> - name of the address type, 20 char max (required)<br/>
			<i>description</i> - description of the address type (optional)<br/>
			<i>useattn</i> - enable the attn address field for this address type (optional, 1 or 0, defaults to 1)<br/>
			<i>useaddress2</i> - enable the address2 address field for this address type (optional, 1 or 0, defaults to 1)<br/>
			<i>useaddress3</i> - enable the address3 address field for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>usecounty</i> - enable the county address field for this address type (optional, 1 or 0, defaults to 1)<br/>
			<i>enabledistrictmatching</i> - enable district matching for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>standardizeaddress</i> - enable address standardization for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>standardizecity</i> - enable city standardization for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>standardizestate</i> - enable state standardization for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>standardizepostalcode</i> - enable postal code standardization for this address type (optional, 1 or 0, defaults to 0)<br/>
			<i>standardizecounty</i> - enable county standardization for this address type (optional, 1 or 0, defaults to 0)<br/>
			<br/>
			Note: Address standardization cannot be enabled if address3 is enabled for this address type.
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/address HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 78

{
    "type": "Office Address",
    "description": "Office Address",
    "useattn": 1
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">201 CREATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT CREATED</td><td>error creating address type</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "count":1,
        "result": "Address type created.",
        "address": {
            "type": "Office Address",
            "description": "Office Address",
            "useattn": 1,
            "useaddress2": 1,
            "useaddress3": 0,
            "usecounty": 1,
            "enabledistrictmatching": 0,
            "standardizeaddress": 0,
            "standardizecity": 0,
            "standardizestate": 0,
            "standardizepostalcode": 0,
            "standardizecounty": 0,
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
        }
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to create address type.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>