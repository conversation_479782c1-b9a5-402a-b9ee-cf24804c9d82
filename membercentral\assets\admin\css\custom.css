@media print {
	.app-main > .mc_customheader,
	.app-main > .app-header,
	.app-main > .app-content .app-breadcrumb,
	.app-main > .app-content > .app-footer,
	.app-sidebar {
		display: none !important;
	}
	.app-main > .app-content { 
		padding: 0px!important;
	}
}
.mc_customheader {
	background-color:#3b3e66;
	height:60px;
	z-index:11;
	position:fixed;
	padding:0 2rem;
}
.app-header {
	height:65px;
	top:54px;
	left:0;
}
.app-sidebar {
	top:120px;
	height: calc(100% - 120px);
}
.app-content {
	padding-top:102px;
}
.app-sidebar--footer {
	height:auto;
}
#mc_userBox .avatar-icon img {
	object-fit: cover;
}
.scrollbar-container, .scrollbar-sidebar {
    position: relative;
    height: auto;
}
.navbar { 
	padding-left:0;
	padding-bottom:0;
}

.nav-line-alt .nav-item+.nav-item {
    margin-left: 1.25rem;
}

.nav-pills-dotted > .nav-item > .nav-link {
	border: 1px dotted #d1d2db;
	margin-left: .10rem;
}

.form-control:disabled {background-color: #cdcdcd; opacity: 1;}
.form-group {margin-bottom: 0.25rem;}
.form-label-group + .form-text{margin-top:-0.75rem;}
.form-label-group .select2-selection--multiple .select2-selection__rendered {margin-top:1rem !important;}
.form-label-group .select2-selection--single .select2-selection__rendered {margin-top:.5rem !important;}
.form-label-group .dateFieldHolder .input-group-text {padding:.375rem .5rem;}
.form-label-group label {white-space:nowrap;}
.input-group > .form-label-group > .form-control {border-top-left-radius: 0; border-bottom-left-radius: 0;}
.form-label-group [data-toggle='tooltip'] {pointer-events: auto!important;}
.form-label-group input,.form-label-group textarea {padding-top:calc(var(--input-padding-y) + var(--input-padding-y) * (2 / 3));padding-bottom:calc(var(--input-padding-y)/ 3)}
.form-label-group input~label,.form-label-group select~label,.form-label-group textarea~label {font-size:1em;top:var(--top-position);transform:translateY(-50%) scale(.7);visibility: visible}
.form-label-group input::placeholder,.form-label-group textarea::placeholder {color:#d1d2db;opacity:1;}

#divMCMainContainer {min-width: 650px;}

.text-grey {color:grey;}
.text-green {color:green;}
.text-darkred {color:#8B0000;}
.text-dim { color:#808080; }
.text-goldenrod {color: goldenrod;}
.text-brightblue {color: #3464a9;}

.bg-grey {background-color: #dddddd !important;}
.bg-forestgreen {background-color: #228b22!important;}
.bg-lightgreen {background-color: #83f383!important;}
.bg-darkyellow { background-color:#cc9!important; }
.bg-palered { background-color:#edaeaf!important; }
.bg-paleyellow { background-color:#fcffa1!important; }

.border-lightgrey { border-color: #ccc!important; }

.cursor-pointer { cursor: pointer; }
.text-decoration-underline { text-decoration:underline; }
.table-layout-fixed { table-layout: fixed; }
.btn-neutral-dark {
 background:rgba(122,123,151,.15);
 color:#7a7b97;
 border-color:transparent
}
.btn-neutral-dark.active,
.btn-neutral-dark:focus,
.btn-neutral-dark:hover {
 background:#7a7b97;
 color:#fff
}
.btn-neutral-dark.active .btn-wrapper--icon svg path,
.btn-neutral-dark:focus .btn-wrapper--icon svg path,
.btn-neutral-dark:hover .btn-wrapper--icon svg path {
 fill:#fff
}
.btn.btn-transition-none:hover {
 -webkit-transform: none !important;
 transform: none !important;
}
.mc_development_watermark { 
 background:url(/assets/admin/images/development.gif) #fff;
}
.mc_beta_watermark { 
 background:url(/assets/admin/images/beta.gif) #fff;
}
.mc_tabcontent {
 border-top: 1px solid #d1d2db;
 border-left: 1px solid #d1d2db;
 border-right: 1px solid #d1d2db;
 border-bottom: 1px solid #d1d2db;
}
.mcg-cell-content { padding:5px 0px; line-height:1.3em; }
.mc_overlay { position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);z-index:2; }
.mc_dt_blur { filter: blur(2px); }
div.dataTables_wrapper div.dataTables_processing { position: absolute !important; top: 15% !important; }

.select2-container .select2-selection__rendered > *:first-child.select2-search--inline { width: 100% !important; }
.select2-container .select2-selection__rendered > *:first-child.select2-search--inline .select2-search__field { width: 100% !important; }
.select2-results__option[aria-disabled="true"], .select2-results__option[disabled] { color:#ccc; }

/* form-control-sm styling for select2 */
select.form-control-sm + .select2-container .select2-search--inline .select2-search__field { font-size: 13px; height: 28px; margin-top: 0!important; }
select.form-control-sm + .select2-container .select2-selection--multiple { min-height: 28px!important; }
select.form-control-sm + .select2-container .select2-selection--multiple .select2-selection__rendered { display: block; }
select.form-control-sm + .select2-container .select2-selection--multiple .select2-selection__choice { margin: 4px 0 4px 6px; font-size: 13px; }
select.form-control-sm + .select2-container .select2-selection--multiple .select2-selection__clear { top: auto; margin-top: .5em!important; }
/* form-control-sm styling for select2 - single */
select.form-control-sm + .select2-container .select2-selection--single { height: 28px!important; }
select.form-control-sm + .select2-container .select2-selection--single span.select2-selection__rendered { line-height: 1.5; margin: 3px 0 3px 0px; font-size: 13px; }
select.form-control-sm + .select2-container .select2-selection--single span.select2-selection__placeholder { line-height: 1.5; }
select.form-control-sm + .select2-container .select2-selection--single .select2-selection__clear { top: auto; margin-top: 0.2em!important; }
/* form-control-sm styling for file control */
div.custom-file.form-control-sm { height: 32px; }
div.custom-file.form-control-sm input.custom-file-input { height:32px; }
div.custom-file.form-control-sm .custom-file-label { height:30px; padding:5px 15px; margin-bottom:0px; overflow:hidden; }
div.custom-file.form-control-sm .custom-file-label:after { height:30px; padding:5px 15px; }

/* custom styling for jquery selectBox */
div.sb.selectbox.custom-select-box { width: 100%; }
div.sb.selectbox.custom-select-box > div.display { height: calc(1.5em + .5rem + 2px); padding: .25rem 1.1rem; font-size: .83125rem; border-radius: .29rem; color: #3b3e66; font-family: "Heebo",sans-serif; }
div.sb.selectbox.custom-select-box .display .arrow_btn { background: none; border: none; margin: 7px 5px 0 0;}
div.sb.selectbox.custom-select-box .display .arrow_btn .arrow { background: none; }
div.sb.selectbox.custom-select-box .arrow_btn:after { font: var(--fa-font-solid); content: "\f107"; font-size: 1.1875rem; }
div.sb.selectbox.custom-select-box .display.active .arrow_btn { box-shadow: none; -moz-box-shadow: none; -webkit-box-shadow: none; }
ul.selectbox.items.custom-select-box { border-radius: .29rem; }
ul.selectbox.items.custom-select-box li.selected { background-color: #eceef7!important; }

div.gridbox {box-sizing:content-box;}
.mc_border_bottom_1 { border-bottom:1px solid #333; }
.w-5 { width:5%; } .w-10 { width:10%; } .w-20 { width:20%; } .w-30 { width:30%; }
.mc_memthumb { max-width:80px; }
/*modal-dialog-slideout*/
.modal-dialog-slideout {min-height: 100%; margin: 0 0 0 auto;background: #fff;}
.modal.fade .modal-dialog.modal-dialog-slideout {-webkit-transform: translate(100%,0)scale(1);transform: translate(100%,0)scale(1);}
.modal.fade.show .modal-dialog.modal-dialog-slideout {-webkit-transform: translate(0,0);transform: translate(0,0);display: flex;align-items: stretch;-webkit-box-align: stretch;height: 100%;}
.modal.fade.show .modal-dialog.modal-dialog-slideout .modal-body{overflow-y: auto;overflow-x: hidden;}
.modal-dialog-slideout .modal-content{border: 0;}
.modal-dialog-slideout .modal-header, .modal-dialog-slideout .modal-footer {height: 69px; display: block;}
/*custom-range-bubble*/
.custom-range-wrap { position: relative; }
.custom-range-bubble { background: #3c44b1; color: white; padding: 0 5px; position: absolute; border-radius: 4px; left: 50%; transform: translateX(-50%); min-width:25px; text-align:center; }
.custom-range-bubble::after { content: ""; position: absolute; width: 2px; height: 5px; background: #3c44b1; top: -5px; left: 50%; }
/*datatables*/
table.dataTable td.details-control { background: url('/assets/common/images/closedfolder.gif') no-repeat center center; cursor: pointer; }
table.dataTable tr.shown td.details-control { background: url('/assets/common/images/openfolder.gif') no-repeat center center; }
/*bs4-tagsinput*/
.bootstrap-tagsinput .badge {padding:0 0.7em;margin:4px;text-transform:none;font-size:12px;}
.bootstrap-tagsinput .badge [data-role="remove"]::after {font-size:12px;}
/*floating label for input-group-prepend*/
.form-label-group .input-group-prepend .input-group-text {padding-top:0.8rem !important; padding-bottom:0.2rem !important;}
/*select2 within input-group*/
select.input-group-item-select2-prepend + span.select2.select2-container {width:auto!important;}
select.input-group-item-select2-prepend + span.select2.select2-container .select2-selection.select2-selection--single {border-top-right-radius:0;border-bottom-right-radius:0;}

/* custom colors btn-outline */
.btn-outline-green{color:green;border-color:green}
.btn-outline-green:hover{color:#fff;background-color:green;border-color:green}
.btn-outline-green.focus,.btn-outline-green:focus{box-shadow:0 0 0 0 transparent}
.btn-outline-green.disabled,.btn-outline-green:disabled{color:green;background-color:transparent}
.btn-outline-green:not(:disabled):not(.disabled).active,.btn-outline-green:not(:disabled):not(.disabled):active,.show>.btn-outline-green.dropdown-toggle{color:#fff;background-color:green;border-color:green}
.btn-outline-green:not(:disabled):not(.disabled).active:focus,.btn-outline-green:not(:disabled):not(.disabled):active:focus,.show>.btn-outline-green.dropdown-toggle:focus{box-shadow:0 0 0 0 transparent}

/* flyout vertical steps */
.mc_verticalform .vertical .steps {width:90px; border-top-left-radius: 0.65rem;}
.mc_verticalform .vertical .content, .vertical .actions {width:calc(100% - 90px)}
.mc_verticalform {height: 100vh!important;}
.mc_verticalform .vertical .content {height:calc(100% - 69px);overflow-y:auto;}

/* card box radio selectors */
.mc_card-input-element+.card { padding: 1rem; height: 2rem; color: var(--primary);-webkit-box-shadow: none;box-shadow: none;border: 2px solid transparent;border-radius: 4px; }
.mc_card-input-element+.card:hover {cursor: pointer;}
.mc_card-input-element:checked+.card { border: 2px solid var(--primary);-webkit-transition: border .3s;-o-transition: border .3s;transition: border .3s; }
.mc_card-input-element:checked+.card::after { content: '\e5ca';color: #AFB8EA;font-family: 'Material Icons';font-size: 24px;-webkit-animation-name: mc_fadeInCheckbox;
	animation-name: mc_fadeInCheckbox;-webkit-animation-duration: .5s;animation-duration: .5s;-webkit-animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
@-webkit-keyframes mc_fadeInCheckbox {
	from { opacity: 0;-webkit-transform: rotateZ(-20deg); }
	to { opacity: 1;-webkit-transform: rotateZ(0deg); }
}
@keyframes mc_fadeInCheckbox {
	from { opacity: 0;transform: rotateZ(-20deg); }
	to { opacity: 1;transform: rotateZ(0deg); }
}

/* skeleton loading effect */
.mc-skeleton {opacity:.7;animation:mc-skeleton-loading 3s linear infinite alternate;}
.mc-skeleton-text {width: 100%;height:1rem;margin-bottom:1rem;border-radius:.125rem;}
@keyframes mc-skeleton-loading {
	0% {background-color: hsl(200, 20%, 70%);}
	100% {background-color: hsl(200, 20%, 95%);}
}

#MCModalHeader button.multiline {position:absolute;top:10px;right:15px;}
.mc-hover-reveal-link:hover span {text-decoration: underline;}
.mc-hover-reveal-link:hover span::after {content: '↗';text-decoration:none;display:inline-block;padding-left:5px;}