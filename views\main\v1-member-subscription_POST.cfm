<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/subscription</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys.<br/>
		<div style="margin-left:30px;">
			<b>Required keys</b>:<br/>
			<i>status</i> - status of the subscription<br/>
			<i>subscription_api_id</i> - api_id of the subscription<br/><br/>

			<b>Optional keys</b>:<br/>
			<i>frequency_api_id</i> - api_id of the frequency. Defaults to Full if not provided.<br/>
			<i>rate_api_id</i> - api_id of the rate. If not provided, we will look up rate based on eligible rate and frequency.<br/>
			<i>rate_overrideamt</i> - overridden amount of the rate<br/>
			<i>startdate</i> - overridden start date of the subscription<br/>
			<i>enddate</i> - overridden end date of the subscription<br/>
			<i>graceenddate</i> - overridden grace end date of the subscription<br/>
			<i>skipemail</i> - true or false, defaults to false. If true, we will skip automated e-mails to Active or Accepted subscriptions.<br/><br/>

			<b>Optionally, an addons array can be provided:</b><br/>
			<i>addons</i> - an array of addons to parent subscription. Each addon can contain:<br/>
			- <i>subscription_api_id</i> - api_id of the subscription (required)<br/>
			- <i>rate_api_id</i> - api_id of the rate. If not provided, we will look up rate based on eligible rate and frequency.<br/>
			- <i>rate_overrideamt</i> - overridden amount of the rate
		</div>
	</div>

	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/subscription HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 350

{
    "status": "Active",
    "subscription_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
    "frequency_api_id": "WWWWWWWW-WWWW-WWWW-WWWW-WWWWWWWWWWWW",
    "rate_overrideamt": "195.00",
    "startdate": <cfoutput>"8/1/#year(now())+1#"</cfoutput>,
    "enddate": <cfoutput>"8/31/#year(now())+1#"</cfoutput>,
    "graceenddate": <cfoutput>"8/31/#year(now())+1#"</cfoutput>,
    "addons": [
        {
            "subscription_api_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY"
        }
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">201 CREATED</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT CREATED</td><td>error creating member subscription</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "result": "Member subscription creation has been scheduled successfully.",
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to create member subscription.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>