<cfset objDocuments = CreateObject("component","models.tsadmin.act_documents")>

<!--- upload the document --->
<cfif isDefined("request.doUploadDocs")>
	<cfset uploadDocResult = objDocuments.uploadDocument(formfields=request)>
	<!--- output serialized JSON --->
	<cfoutput>#serializeJSON(uploadDocResult)#</cfoutput>
	<cfabort>
<cfelseif isDefined("request.reuploadOriginalDoc")>
	<cfset formActionURL = "DocumentAdd.cfm?documentID=#request.documentID#&depoMemberDataID=#request.depoMemberDataID#&doReuploadOriginalDoc=1&nobanner=1">

	<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
	<cfsavecontent variable="pageJS">
		<cfoutput>
		<script type="text/javascript">
			function validateForm(theForm) {
				if (!$('##newDocFile').val().length) {
					$('##err_reupload').html('Select a New Document File.').show();
					return false;
				} else {
					$('##frmDocUploadButton').hide();
					$('##frmDocSaveLoading').show();
					$('##err_reupload').html('').hide();
				}
				return true;
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#pageJS#">

	<cfoutput>
	<div style="font-size: 1.5em;font-weight: bold;margin-bottom:20px;color: ##0E568D;">Re-Upload & Process Document</div>
	<div id="err_reupload" style="width:250px;padding:0.5rem 1rem;margin-bottom:10px;border:1px solid ##e78989;border-radius:0.25rem;color:##870808;background-color:##ebcaca;display:none;"></div>
	<form id="frmReuploadDocument" name="frmReuploadDocument" action="#formActionURL#" method="POST" enctype="multipart/form-data" onsubmit="return validateForm(this)">
		<div style="font-size: 1.1em;margin-bottom:15px;">Select the new document file:</div>
		<input type="file" id="newDocFile" name="newDocFile">
		<div style="font-size: 1.1em;margin:20px 0px 10px 0px;">This process will take several minutes to complete. You will need to reload this page to see the reprocessed document.</div>
		<div id="frmDocUploadButton" style="text-align:right;"><button type="submit" name="btnReuploadDoc" id="btnReuploadDoc">Continue</button></div>
		<div id="frmDocSaveLoading" style="text-align:right;display:none;margin-top:10px;"><i class="fa-light fa-circle-notch fa-spin"></i> <b>Please Wait...</b></div>
	</form>
	</cfoutput>
<cfelseif isDefined("request.doReuploadOriginalDoc") and request.doReuploadOriginalDoc eq 1>
	<cfset uploadResult = objDocuments.reuploadOriginalDocument(request)>
	
	<cfif uploadResult.success>
		<div style="width:360px;color:#0c6b7e; background-color:#cff3f8; border-color:#bceff5;position:relative; padding:.75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-radius: .65rem;">
			The document has been successfully uploaded.
		</div>
		<div style="margin-top:20px;"><i class="fa-light fa-circle-notch fa-spin"></i> <b>Please wait while we process the upload.</div>
	<cfelse>
		<div style="width:360px;color: #7e0c0c;background-color: #edbebc;border-color: #e5230f;position:relative; padding:.75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-radius: .65rem;">
			An error occured while uploading the document.
		</div>
	</cfif>
	<script type="text/javascript">
		setTimeout(function(){ 
			top.closeBox();
			top.location.href = <cfoutput>'DocumentEdit.cfm?depoMemberDataID=#request.depoMemberDataID#&documentID=#request.DocumentID#'</cfoutput>;
		},3000);
	</script>
<cfelse>
	<cfparam name="request.depoMemberDataID" default="0">

	<cfset qryMemberData = application.objCommon.getMemberData(request.depoMemberDataID)>
	<cfset qryDepoDocumentSettings = objDocuments.getDocumentSettings()>
	<cfset getAssociations = application.objCommon.getAssociationsWithMembers()>

	<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="account_memberInit">
		<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#request.depomemberdataID#">
		<cfprocresult name="memberLinkedSites" resultset="4">
	</cfstoredproc>

	<cfsavecontent variable="addDocumentJS">
		<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
		<link href="/javascripts/plupload/3.1.2/jquery.plupload.queue/css/jquery.plupload.queue.css" rel="stylesheet" type="text/css" />
		<script type="text/javascript" src="/javascripts/plupload/3.1.2/plupload.full.min.js"></script>
		<script type="text/javascript" src="/javascripts/plupload/3.1.2/jquery.plupload.queue/jquery.plupload.queue.min.js"></script>

		<script language="javascript">
		<cfoutput>
			var #ToScript("DocumentAdd.cfm?depoMemberDataID=#request.depoMemberDataID#","addDocumentLink")#
			var #ToScript(request.depoMemberDataID,"depoMemberDataID")#
		</cfoutput>
		let depoDocUploader, depoDocsUploadedCount = 0, newDocumentIDArray = [];

		function hideAlert() { $('#errDocForm').html('').hide(); };
		function showAlert(msg) { $('#errDocForm').html(msg).show(); };
		function onDepoDocFileAddedOrRemoved(up, files) {
			$.each(depoDocUploader.pluploadQueue().files,function(i, file) {
				$('#'+file.id).find('.plupload_file_name').html('<a href="#" onclick="removeDepoDocFileFromQueue(\''+file.id+'\');return false;" class="removeDepoDocFile" title="Remove File"><img src="/media/cancel.png" alt="Remove File" style="vertical-align:middle;"></a> ' + file.name);
			});
			var fileCount = depoDocUploader.pluploadQueue().files.length;
			$('#btnUploadDocuments').prop('disabled', fileCount ? false : true);
		}
		function removeDepoDocFileFromQueue(id) {
			depoDocUploader.pluploadQueue().removeFile(id);
		}
		function clearDepoDocUploadQueue() {
			$.each(depoDocUploader.pluploadQueue().files,function(i, file) {
				depoDocUploader.pluploadQueue().removeFile(file.id);
			});
			depoDocsUploadedCount = 0;
		}
		function initializeDocumentUploader(){
			depoDocUploader = $("#depoDocUploader").pluploadQueue({
				runtimes : 'html5',
				url : addDocumentLink + '&doUploadDocs=1&noheader=1',
				multipart : true,
				file_data_name : 'depoDoc',
				multiple_queues : true,
				init : {
					PostInit: function() {
						$('#depoDocUploader .plupload_header_text').html('Drag and drop files to the area below, or click "Add files" to browse your computer. Click the "Start Upload of Files" button after selecting your files.');
						$('#depoDocUploader_container').attr('title','Add files here');
					},
					FilesAdded: onDepoDocFileAddedOrRemoved,
					FilesRemoved: onDepoDocFileAddedOrRemoved,
					BeforeUpload: function(up, file) {
						up.settings.multipart_params = {
							'docState': $('#docState').val(),
							'creditType': $('input[name="credType"]:checked').val(),
							'depoDocAmazonBucksFullName': $('input[name="credType"]:checked').val() == 'amazon' ? $('#depoDocAmazonBucksFullName').val().trim() : '',
							'depoDocAmazonBucksEmail': $('input[name="credType"]:checked').val() == 'amazon' ? $('#depoDocAmazonBucksEmail').val().trim() : '',
							'isDepoConnectUpload': $("#isDepoConnectUpload").is(':checked') ? 1 : 0
						};
						/*Next line needed to force the params to be passed along in the POST*/
						up.setOption('params', up.settings.multipart_params);
						toggleUploadButton(false);
					},
					FileUploaded: function(up, file, ret) {
						let retRegex = /[\r\n]/ig;
						let retObj = JSON.parse(ret.response.replaceAll(retRegex,''));
						if (retObj.success) {
							depoDocsUploadedCount += retObj.uploadeddocscount;
							if (retObj.documentid)
								newDocumentIDArray.push(retObj.documentid);
						} else {
							if (retObj.errmsg) showAlert(retObj.errmsg);
							else showAlert('We were unable to upload the files.');
						}
					},
					UploadComplete: function(up, files) {
						if (depoDocsUploadedCount) {
							$('#docUpdConfirmContainer').html('Successfully Uploaded ' + depoDocsUploadedCount + ' document' + (depoDocsUploadedCount > 1 ? 's' : '') + '.').show().fadeOut(4000);
						}
						clearDepoDocUploadQueue();

						if(newDocumentIDArray.length){
							$('#depoDocUploader').hide();
							self.location.href='DocumentEdit.cfm?depoMemberDataID='+ depoMemberDataID +'&documentID='+newDocumentIDArray[0];
						} else {
							$('#depoDocUploader').show();
							toggleUploadButton(true);
						}
					},
					Error: function(up, error) {
						showAlert('We ran into an issue. ' + error.message);
						toggleUploadButton(true);
					}
				}
			});
		}
		function toggleUploadButton(f){
			$('#btnUploadDocuments').prop('disabled', !f).text(f ? 'Start Upload of Files' : 'Uploading Documents...');
		}
		function doUploadDocuments(){
			if(validateDocumentForm()){
				if (depoDocUploader.pluploadQueue().files.length){
					$('.removeDepoDocFile').remove();
					depoDocUploader.pluploadQueue().start();
				}
			}
		}
		function validateDocumentForm() {
			var theForm = document.forms["frmDocument"];
			let arrReq = [];
			hideAlert();
			var errMsg = '';

			if ($('#docState').val() == '') arrReq.push('Which trial lawyer group gets Credit for your depositions?');
			if (!$('input[name="credType"]').is(':checked')) arrReq.push('What type of Credit do you want for your depositions?');
			else if ($('input[name="credType"]:checked').val() == 'amazon') {
				if (!$('#depoDocAmazonBucksFullName').val().trim().length)
					arrReq.push('Enter the Full Name for Amazon Gift Card Recipient.');
				if (!$('#depoDocAmazonBucksEmail').val().trim().length || !_CF_checkEmail($('#depoDocAmazonBucksEmail').val()))
					arrReq.push('Enter a valid Email Address for Amazon Gift Card Recipient.');
			}
			if ($('#depoDocUploader').is(':visible') && !depoDocUploader.pluploadQueue().files.length)
				arrReq.push('Drag and drop files to the area below, or click "Add files" to browse your computer.');

			if (arrReq.length) {
				showAlert(arrReq.join('<br/>'));
				return false;
			}

			return true;
		}
		function toggleAmazonBucksOptions(f){
			if(f) $('.amazonBucksOptions').show();
			else $('.amazonBucksOptions').hide();
		}
		function fillAmazonBuckDetails(fn,ea) {
			$('#depoDocAmazonBucksFullName').val(fn);
			$('#depoDocAmazonBucksEmail').val(ea);
		}

		$(function() {
			initializeDocumentUploader();
		});
		</script>
		<style>
			#errDocForm { width:500px;padding:0.5rem 1rem;margin:10px 0px;border:1px solid #e78989;border-radius:0.25rem;color:#870808;background-color:#ebcaca; }
			#depoDocUploader .plupload_container {padding:0px;width:900px;}
			#depoDocUploader .plupload_header {background:#dfdfdf;}
			#depoDocUploader .plupload_header_content {color:#003366 !important;background:none;padding-left:10px !important;}
			#depoDocUploader .plupload_header_title {font-weight:600;}
			#depoDocUploader .plupload_header_text {font-weight:400;font-size: 13px;padding-bottom:5px;}
			#depoDocUploader .plupload_button.plupload_start {display:none;}
			#depoDocUploader .plupload_droptext {font-size:18px;color:#777;}
			.stepDiv {margin-bottom:20px;}
			.stepLabel {font-weight: 600;margin-top:10px;margin-bottom:7px;}
			.stepFields {padding-left:7px;}
			.doc-tag { display:inline-flex; margin:0 8px 8px 0; align-items:center; border-radius:0.5em; border:1px solid #dadce0; 
				background-color:#fff; height:2em; transition:all .2s cubic-bezier(0.4,0,0.2,1) 0s; padding:0.1em 0.45em; font-size:12px;
				text-decoration:none !important; }
			.doc-tag:hover { box-shadow:0 1px 5px 0 rgba(0,0,0,.1); color:#3c4043; border-color:#b2b3b5; text-decoration:none; }
			.badge { text-transform: uppercase; height: 20px; line-height: 21px; }
			.badge-info { color: #fff; background-color: #11c5db; }
			.badge { font: "Heebo",sans-serif; display: inline-block; padding: 0 .7em; font-size: 80%; font-weight: 700; text-align: center; white-space: nowrap; 
				vertical-align: baseline; border-radius: .2rem; transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
				float:right; margin-left:2px; margin-right:8px; }
			#depoConnectSelection input, #depoConnectSelection label { vertical-align:middle; }
		</style>
	</cfsavecontent>
	<cfhtmlhead text="#addDocumentJS#">

	<cfoutput>
	<div id="crumbs">
		You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
		<a href="MemberEdit.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">Member Account: #qryMemberData.FirstName# #qryMemberData.LastName#</a> \ 
		<a href="MemberDocuments.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">Document Contribution History</a> \
		Add Documents
	</div>
	<div id="pageTitle">Add Documents</div>
	<div id="formContainer">
		<div id="documentFieldsSection">
			<form id="frmDocument" name="frmDocument">
			<input type="hidden" name="DocumentID" value="0">
			<table cellpadding="2" style="min-width:600px;">
				<tr><td colspan="3"></td></tr>
				<tr>
					<td width="150"><b>Contributor:</b></td>
					<td></td>
					<td><strong><a href="MemberEdit.cfm?depomemberdataID=#qryMemberData.depomemberdataID#">#qryMemberData.FirstName# #qryMemberData.LastName#</a></strong></td>
				</tr>
			</table>
			<div id="errDocForm" style="display:none;"></div>
			<div class="stepDiv" style="margin-top:15px;">
				<div class="stepLabel">Which trial lawyer group gets credit for these depositions?</div>
				<div class="stepFields">
					<select name="docState" id="docState">
					<option value="">Select a Trial Lawyers Association/Litigation Group</option>
					<cfloop query="getAssociations">
						<option value="#TLAMemberState#" <cfif qryMemberData.tlamemberstate eq getAssociations.TLAMemberState>selected</cfif>>#TLAMemberState# - #description#</option>
					</cfloop>
					</select>
				</div>
			</div>
			<cfif val(qryDepoDocumentSettings.DepoAmazonBucks) AND val(qryDepoDocumentSettings.DepoAmazonBucksCredit)>
				<div class="stepDiv">
					<div class="stepLabel">What type of credit does the contributor want for these depositions?</div>
					<div class="stepFields">
						<div style="margin-bottom:3px;">
							<input type="radio" name="credType" id="credType_depo" value="depo" checked onclick="toggleAmazonBucksOptions(0)">
							<label for="credType_depo">Contributor prefers Deposition Purchase Credits which can be used at a later date without penalty, up to $15 per depo no limit (best value)</label>
						</div>
						<div>
							<input type="radio" name="credType" id="credType_amazon" value="amazon" onclick="toggleAmazonBucksOptions(1)">
							<label for="credType_amazon">Contributor prefers Amazon Bonus Cash of #DollarFormat(qryDepoDocumentSettings.DepoAmazonBucksCredit)# per deposition, which will be awarded in the form of an Amazon Gift Card.</label>
						</div>
						<table cellpadding="2" style="display:none;margin-top:10px;margin-left:20px;" class="amazonBucksOptions">
							<tr>
								<td>Full Name for Amazon Gift Card Recipient:</td>
								<td></td>
								<td><input type="text" id="depoDocAmazonBucksFullName" name="depoDocAmazonBucksFullName" value="" maxlength="550" size="40"></td>
							</tr>
							<tr>
								<td>Email for Amazon Gift Card Recipient:</td>
								<td></td>
								<td><input type="text" id="depoDocAmazonBucksEmail" name="depoDocAmazonBucksEmail" value="" maxlength="255" size="40"></td>
							</tr>
							<cfif memberLinkedSites.recordCount OR len(qryMemberData.Email)>
								<tr valign="top">
									<td colspan="3">
										Or choose from one of these:
										<div style="padding:8px 0;">
											<cfif len(qryMemberData.Email)>
												<a href="##" class="doc-tag" onclick="fillAmazonBuckDetails('#encodeForJavaScript("#qryMemberData.FirstName# #qryMemberData.LastName#")#','#encodeForJavaScript(qryMemberData.Email)#');return false;">
													<span class="badge badge-info">TSAdmin</span> 
													#qryMemberData.FirstName# #qryMemberData.LastName# - #qryMemberData.Email#
												</a>
											</cfif>
											<cfloop query="memberLinkedSites">
												<a href="##" class="doc-tag" onclick="fillAmazonBuckDetails('#encodeForJavaScript("#memberLinkedSites.firstname# #memberLinkedSites.lastname#")#','#encodeForJavaScript(memberLinkedSites.email)#');return false;">
													<span class="badge badge-info">#memberLinkedSites.SiteCode#</span> 
													#memberLinkedSites.firstname# #memberLinkedSites.lastname##len(memberLinkedSites.email) ? " - #memberLinkedSites.email#" : ""# 
												</a>
											</cfloop>
										</div>
									</td>
								</tr>
							</cfif>
						</table>
					</div>
				</div>
			</cfif>
			<div id="depoDocUploader" class="docUpdTool" style="margin-top:10px;">
				<p>Your browser does not have HTML5 support.</p>
			</div>
			<div>
				<button type="button" name="btnUploadDocuments" id="btnUploadDocuments" onclick="doUploadDocuments();" style="margin-top:10px;" disabled>Start Upload of Files</button>
				<span id="docUpdConfirmContainer" style="color:##116d2f;background-color:##d1f4d9;border-color:##bff0ca;position: relative;padding: .75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-top-color: transparent;border-right-color: transparent;border-bottom-color: transparent;border-left-color: transparent;border-radius: .65rem;margin-left:20px; display:none;"></span>
			</div>
			<div id="depoConnectSelection" style="margin-top:10px;">
				<input type="checkbox" id="isDepoConnectUpload" name="isDepoConnectUpload" value="1">
				<label for="isDepoConnectUpload">DepoConnect Upload. Skip confirmation email and credit awarded email.</label>
			</div>
			</form>
		</div>
	</div>
	</cfoutput>
</cfif>