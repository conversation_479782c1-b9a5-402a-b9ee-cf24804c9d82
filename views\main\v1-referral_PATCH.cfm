<div class="patch method-example">
	<div class="method-wrapper">
		<div class="method">PATCH</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following key.<br/>
		<div style="margin-left:30px;">
			<i>op</i> - the operation to perform, either "activate" or "reopen"<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request - Activate Referral</div>
	<div class="jsonblock">
<pre class="prettyprint">
PATCH /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 17

{
    "op":"activate"
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Request - Re-open Referral</div>
	<div class="jsonblock">
<pre class="prettyprint">
PATCH /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 15

{
    "op":"reopen"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 REFERRAL NOT FOUND</td><td>invalid referral api_id</td></tr>
		<tr><td class="rc">406 NOT ACCEPTABLE</td><td>invalid patch request</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error updating referral</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "referral": {
            "calldate": "1/1/2024 11:04 AM",
            "lastupdateddate": "3/1/2024 4:16 PM",
            "referraldate": "1/4/2024 11:08 AM",
            "caseopendate": "2/1/2024 2:00 PM",
            "casecloseddate": "3/1/2024 4:16 PM",
            "source":"Website",
            "language": "English",
            "legalissue": "Family Law: Divorce with Children and Custody Matters",
            "agreesurvey": 1,
            "agreenewsletter": 1,
            "isreferred": 0,
            "caseexists": 0,
            "referralstatus": "Pending - Referral NOT sent",
            ...
        }
        "result": "Referral activated."
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response - Invalid Input (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
406 NOT ACCEPTABLE

{
    "data": {},
    "error": true,
    "messages": [
        "Invalid patch request."
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response - Internal Error (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to apply patch to the referral."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>