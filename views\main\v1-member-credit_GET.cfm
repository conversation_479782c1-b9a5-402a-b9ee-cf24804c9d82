﻿<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/credit</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li>
			<i>search</i> is a structure of subkeys used to limit the credits returned. Eligible subkeys are:<br/>
				<i>programdatefrom</i> is a date to limit the credit awarded programs to on or after this date<br/>
				<i>programdateto</i> is a date to limit the credit awarded programs to on or before this date<br/>
				<i>authority</i> - credit authority<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each credit included in the response will have the following data.
		<ul>			
			<i>programdate</i> - for Events, this is the event start date; for SeminarWeb Live, this is the program start date; for SeminarWeb OnDemans, this is the completed date; for Store, this is the affirmation claimed date.<br/>
			<i>program</i> - event title for Events, seminar name for SeminarWeb, and product title for Store<br/>
			<i>programtype</i> - program type for which the credits were awarded. Valid values are: Event, SeminarWeb, Store<br/>
			<i>authorities</i> - array of authorities and credit types awarded across them (includes <i>type</i> and <i>amount</i>)<br/>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/credit HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/credit HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 157

{
    "count": 5,
    "start": 10,
    "search": {
        "programdatefrom": "1/1/<cfoutput>#year(now())#</cfoutput>",
        "programdateto": "1/31/<cfoutput>#year(now())#</cfoutput>",
        "authority": "Credit Authority Name"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":5,
        "credit": [
            {
                "programdate": "2019-01-15 08:00:00",
                "program": "Sample Event",
                "programtype": "Event",
                "authorities": [
                    {
                        "authority": "Authority Name",
                        "credittypes": [
                            {
                                "type": "Ethics",
                                "amount": 2
                            }
                        ]
                    }
                ]
            }
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>