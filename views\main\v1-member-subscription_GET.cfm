<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/subscription</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li>
			<i>search</i> is an object of subkeys used to limit the subscriptions returned. Eligible subkeys are:<br/>
				<i>type_api_id</i> is an array of the subscription type api_id<br/>
				<i>subscription_api_id</i> is an array of the subscription api_id<br/>
				<i>rate_api_id</i> is an array of the subscription rate api_id<br/>
				<i>frequency_api_id</i> is an array of the subscription frequency api_id<br/>
				<i>status</i> is an array of the subscription statuses<br/>
				<i>startdate</i> is the start date of the subscription<br/>
				<i>enddate</i> is the end date of the subscription<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each subscription listed in the response will have the following data:
		<ul>
			<li><i>x-api-uri</i> - the URI of the member's subscription</li>
			<li><i>subscriber_id</i> - unique identifer of the member's subscription</li>
			<li><i>parent_subscriber_id</i> - subscription's parent subscriber ID, if applicable</li>
			<li><i>x-parent-api-uri</i> - the URI of the parent subscriber ID, if applicable</li>
			<li><i>type</i> - name of the subscription type</li>
			<li><i>type_api_id</i> - api_id of the subscription type</li>
			<li><i>subscription</i> - name of the subscription</li>
			<li><i>subscription_api_id</i> - api_id of the subscription</li>
			<li><i>rate</i> - name of the subscription rate</li>
			<li><i>rate_api_id</i> - api_id of the subscription rate</li>
			<li><i>frequency</i> - name of the subscription frequency</li>
			<li><i>frequency_api_id</i> - api_id of the subscription frequency</li>
			<li><i>status</i> - status of the subscription</li>
			<li><i>activationstatus</i> - activation status of the subscription</li>
			<li><i>startdate</i> - start date of the subscription</li>
			<li><i>enddate</i> - end date of the subscription</li>
			<li><i>graceenddate</i> - grace period end date of the subscription, if applicable</li>
			<li><i>x-renew-uri</i> - when the status is Billed, the URI of the renewal</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/subscription HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/subscription HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 157

{
    "count": 5,
    "start": 10,
    "search": {
        "type_api_id": [
            "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
        ],
        "status": [
            "Active",
            "Accepted"
        ]
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":21,
        "subscription":[
            {
                "type": "Membership",
                "type_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "subscription": "Annual Membership",
                "subscription_api_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "rate": "Less than 5 years",
                "rate_api_id": "ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                "frequency": "Full",
                "frequency_api_id": "WWWWWWWW-WWWW-WWWW-WWWW-WWWWWWWWWWWW",
                "status": "Expired",
                "activationstatus": "Activation Requirement Met",
                "startdate": "08/22/2011",
                "enddate": "12/31/2011",
                "graceenddate": "03/31/2012",
                "subscriber_id": "123456",
                "parent_subscriber_id": "",
                "x-parent-api-uri": "",
                "x-api-uri": "/v1/member/SAMPLE123456/subscription/123456",
                "x-renew-uri": ""
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>