<cfset qryAssociations = application.objCommon.getAssociationsWithMembers()>

<cfset local.objDocuments = CreateObject("component","models.tsadmin.act_documents")>
<cfset numDocumentsPending = local.objDocuments.getDocumentsPending()>
<cfset numDocumentsAdminReview = local.objDocuments.getDocumentsForAdminReview()>
<cfset numDocumentsApprovedNoXOD = local.objDocuments.getDocumentsForApprovedNoXOD()>
<cfset qryDocumentStatuses = local.objDocuments.getDocumentStatuses()>
<cfset qryDocumentUploadSources = local.objDocuments.getDocumentUploadSources()>
<cfset local.strDocumentExtensions = local.objDocuments.getDocumentExtensions()>
<cfset local.approvedStatusID = QueryFilter(qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Approved'; }).statusID>
<cfset local.adminReviewStatusID = QueryFilter(qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Flagged for Review'; }).statusID>
<cfset local.pendingStatusID = QueryFilter(qryDocumentStatuses, function(thisRow) { return arguments.thisRow.statusName EQ 'Pending Approval'; }).statusID>
<cfset local.strDocsQueueFilter = local.objDocuments.getDocumentsQueueFilter()>

<cfsavecontent variable="headJS">
	<script type='text/javascript' src='/javascripts/membercentral/jquery-1.8.3.min.js'></script>
	<script type='text/javascript' src='/javascripts/membercentral/colorbox/jquery.colorbox-min-1.4.19.js'></script>
	<script type="text/javascript">
		var savedFilterParams = '', orderBy = 5, orderDir = 'asc';
		function hideAlert() { $('#errFilters').html('').hide(); };
		function showAlert(msg) { $('#errFilters').html(msg).show(); };
		function getParsedResponse(xhr){
			try { 
				var r = JSON.parse(xhr.responseText);
			} catch(e) { 
				var r = new Object();
				r.success = false;
				r.errmsg = "Some error occured while parsing response.";
			}
			return r;
		}
		function validateDateFilter(name){
			var theForm = $('#frmDocumentFind')[0];
			return _CF_hasValue(theForm[name], "TEXT", false) && !_CF_checkdate(theForm[name].value, true);
		}
		function filterDocuments() {
			hideAlert();
			var arrReq = new Array();
			if (validateDateFilter('depositionDateFrom')) arrReq[arrReq.length] = "Invalid Deposition Date From.";
			if (validateDateFilter('depositionDateTo')) arrReq[arrReq.length] = "Invalid Deposition Date To.";
			if (validateDateFilter('dateEnteredFrom')) arrReq[arrReq.length] = "Invalid Contribution Date From.";
			if (validateDateFilter('dateEnteredTo')) arrReq[arrReq.length] = "Invalid Contribution Date To.";
			if (validateDateFilter('statusSetFrom')) arrReq[arrReq.length] = "Invalid Status Date From.";
			if (validateDateFilter('statusSetTo')) arrReq[arrReq.length] = "Invalid Status Date To.";
			if (arrReq.length) {
				showAlert(arrReq.join('<br/>'));
				return false;
			}
			
			var fd = new Object();
			var arrFrmData = $('#frmDocumentFind').serializeArray();

			$.each(arrFrmData, function() {
				if (fd[this.name] !== undefined && typeof this.value !== undefined) {
					fd[this.name] = fd[this.name] + ',' + this.value || '';
				} else {
					fd[this.name] = this.value || '';
				}
			});
			var arrCheckboxFields = $.unique($('#frmDocumentFind').find('input[type="checkbox"]').map( function() { return $(this).attr('name'); }).get());
			$.each(arrCheckboxFields, function() {
				if (fd[this] === undefined) fd[this] = '';
			});

			savedFilterParams = fd;
			doLoadDocuments(0);
		}
		function doLoadDocuments(startRow){
			$('#btnSearchDocuments').prop('disabled',true);
			$('div#resultsGridContainer').html('<div class="c"><i class="fa-light fa-circle-notch fa-spin fa-3x"></i><br/><b>Please Wait...</b></div>').show();
			$.ajax({
				url: 'DocumentsQueueResults.cfm?noheader=1&startRow='+startRow+'&orderBy='+orderBy+'&orderDir='+orderDir,
				timeout: 40000,
				data: savedFilterParams,
				type: 'POST',
				dataType: 'json',
				complete: function(xhr,s) { 
					var r = getParsedResponse(xhr);
					if (r.success) {
						$('div#resultsGridContainer').html(r.html);
						if (r.totalcount) $('#btnExportDocuments').show();
						else $('#btnExportDocuments').hide();
					} else {
						alert(r.errmsg ? r.errmsg : 'We were unable to load the results.');
						$('div#resultsGridContainer').html('').hide();
					}
					$('#btnSearchDocuments').prop('disabled',false);
				}
			});
		}
		function doSortDocumentList(newOrderByCol){
			if(orderBy == newOrderByCol)
				orderDir = (orderDir == 'asc' ? 'desc' : 'asc');
			else orderDir = 'asc';
			orderBy = newOrderByCol;
			doLoadDocuments(0);
		}
		function clearDocQueueFilter() {
			/* since reset() won't clear fields with default values */
			$('#frmDocumentFind input[type="text"], #frmDocumentFind select').val('');
			$('#hasAmazonCredits').prop('checked',false);
		}
		function filterPendingDocs() {
			clearDocQueueFilter();
			$('#documentStatusID').val(<cfoutput>#local.pendingStatusID#</cfoutput>);
			filterDocuments();
		}
		function filterAdminReviewDocs() {
			clearDocQueueFilter();
			$('#documentStatusID').val(<cfoutput>#local.adminReviewStatusID#</cfoutput>);
			filterDocuments();
		}
		function filterApprovedNoXODDocs() {
			clearDocQueueFilter();
			$('#documentStatusID').val(<cfoutput>#local.approvedStatusID#</cfoutput>);
			$("#excludeDisabledDocs").prop('checked', true);
			$("#documentExtension").val("PDF").change();
			$("#xodapprove").val(0).change();
			$("#dateEnteredFrom").val(<cfoutput>'#dateFormat(dateAdd("yyyy", -1, now()), "mm/dd/yyyy")#'</cfoutput>);
			filterDocuments();
		}
		function exportDocuments() {
			self.location.href = 'DocumentsQueueResults.cfm?noheader=1&resultsMode=export&'+$('#frmDocumentFind').serialize();
		}
		function toggleCheckAllDocuments(chk){
			$("input[name='selDocID']").prop("checked", chk?true:false);
		}
		function getCheckedDocumentIDs(){
			return $("input[name='selDocID']:checked").map(function() {return this.value;}).get().join(',');
		}
		function deleteDocuments(){
			var checkedIDs = getCheckedDocumentIDs();
			if(checkedIDs == ''){
				alert('Select at least one document to perform this operation.');
				return false;
			}

			if (confirm('Are you sure you want to delete the selected documents?')) {
				var objParams = { "documentIDList" : checkedIDs };
				$('#btnDelete').attr("disabled", true);

				var requestResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') doLoadDocuments(0);
					else alert(r.errmsg ? r.errmsg : 'We were unable to delete the selected documents.');
					$('#btnDelete').attr("disabled", false);
				};

				TS_AJX('DOC','massDeleteDocuments',objParams,requestResult,requestResult,20000,requestResult);
			}
		}
		function changeCreditType(){
			var checkedIDs = getCheckedDocumentIDs();
			if(checkedIDs == ''){
				alert('Select at least one document to perform this operation.');
				return false;
			}

			top.$.colorbox( {innerWidth:510, innerHeight:200, html:$('#dialog_changeCreditType').html(), overlayClose:false} );
		}
		function onChangeCreditTypeDropdown(){
			var newCreditType = $('#newCreditType').val();
			$('tr.rowCashCredits, tr.rowAmazon').hide();
			if(newCreditType == 'Cash Credits')
				$('tr.rowCashCredits').show();
			else if(newCreditType == 'Amazon')
				$('tr.rowAmazon').show();
		}
		function doChangeCreditType(){
			var objParams = { creditType : $('#newCreditType').val() };
			if(objParams.creditType == ''){
				$('#newCreditType').focus();
				return false;
			}
			else if(objParams.creditType == 'Cash Credits'){
				objParams["purchaseCredit"] = $('#purchaseCredit').val();
				if (objParams.purchaseCredit == '') {
					$('#purchaseCredit').focus();
					return false;
				}
				else if (!_CF_checknumber(objParams.purchaseCredit, false)) {
					alert("Enter a valid Purchase Credit.");
					return false;
				}
			}
			else if(objParams.creditType == 'Amazon'){
				objParams["depoAmazonBucksFullName"] = $('#depoAmazonBucksFullName').val();
				objParams["depoAmazonBucksEmail"] = $('#depoAmazonBucksEmail').val();
				objParams["depoAmazonBucksCredit"] = $('#depoAmazonBucksCredit').val();

				var errMsg = '';
				if (objParams.depoAmazonBucksFullName == "")
					errMsg = "Enter the Full Name of Receiver for Amazon Bucks.";
				else if (objParams.depoAmazonBucksEmail == "")
					errMsg = "Enter the Receiver Email Address for Amazon Bucks.";
				else if(!_CF_checkEmail(objParams.depoAmazonBucksEmail))
					errMsg = "Enter a valid Receiver Email Address for Amazon Bucks.";
				else if (!_CF_checknumber(objParams.depoAmazonBucksCredit, true))
					errMsg = "Enter a valid Amazon Credit Value for Amazon Bucks.";
				else if(objParams.depoAmazonBucksCredit > 5)
					errMsg = "Amazon Credit Value cannot exceed $5.";

				if(errMsg.length){
					alert(errMsg);
					return false;
				}
			}

			if (confirm('Are you sure you want to change the credit type for selected documents?')) {
				var requestResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') doLoadDocuments(0);
					else alert(r.errmsg ? r.errmsg : 'We were unable to change the credit type for selected documents.');
					$('#btnChangeCreditType').attr("disabled", false);
					closeBox();
				};

				objParams["documentIDList"] = getCheckedDocumentIDs();
				$('#btnChangeCreditType').attr("disabled", true);
				TS_AJX('DOC','massChangeCreditType',objParams,requestResult,requestResult,20000,requestResult);
			}
		}
		function closeBox() { $.colorbox.close(); }

		$(function() {
			filterDocuments();
		});
	</script>
	<style>
		#errFilters { width:800px;padding:0.5rem 1rem;margin:10px 0px;border:1px solid #e78989;border-radius:0.25rem;color:#870808;background-color:#ebcaca; }
		#tblDocuments th.valueCol { cursor: pointer; }
	</style>
	<link type='text/css' rel='stylesheet' href='/javascripts/membercentral/colorbox/colorbox-1.4.19.css'>
</cfsavecontent>
<cfhtmlhead text="#headJS#">

<div id="crumbs">
	You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
	Find and Process Documents
</div>

<cfoutput>
<form id="frmDocumentFind" name="frmDocumentFind">
<table cellpadding="4" cellspacing="0">
<tr>
	<td colspan="4">
		<div id="pageTitle">Find and Process Documents</div>
		<div>Find and process documents using the filters below.</div>
	</td>
	<td colspan="7">
		<cfif numDocumentsPending gt 0 or numDocumentsAdminReview gt 0 or numDocumentsApprovedNoXOD gt 0>
			Quick Links: &nbsp; 
			<cfif numDocumentsPending gt 0>
				<a href="javascript:filterPendingDocs();" style="margin-left:10px;"><b>#numDocumentsPending#</b> Documents Pending Approval</a>
			</cfif>
			<cfif numDocumentsAdminReview gt 0>
				<a href="javascript:filterAdminReviewDocs();" style="margin-left:10px;"><b>#numDocumentsAdminReview#</b> Documents Flagged for Review</a>
			</cfif>
			<cfif numDocumentsApprovedNoXOD gt 0>
				<a href="javascript:filterApprovedNoXODDocs();" style="margin-left:10px;"><b>#numDocumentsApprovedNoXOD#</b> Documents Approved NO XOD</a>
			</cfif>
		</cfif>
	</td>
</tr>
<tr>
	<td colspan="11">
		<div id="errFilters" style="display:none;"></div>
	</td>
</tr>
<tr>
	<td nowrap>Contributor First Name:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="contributorFirstName" name="contributorFirstName" value="#local.strDocsQueueFilter.contributorFirstName#" size="20"></td>
	<td width=20>&nbsp;</td>
	<td>Expert First Name:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="expertFirstName" name="expertFirstName" value="#local.strDocsQueueFilter.expertFirstName#" size="20"></td>
	<td width=20>&nbsp;</td>
	<td>Document ID:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="documentID" name="documentID" value="#local.strDocsQueueFilter.documentID#" size="10" maxlength="10"></td>
</tr>
<tr>
	<td nowrap>Contributor Last Name:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="contributorLastName" name="contributorLastName" value="#local.strDocsQueueFilter.contributorLastName#" size="20"></td>
	<td width=20>&nbsp;</td>
	<td>Expert Last Name:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="expertLastName" name="expertLastName" value="#local.strDocsQueueFilter.expertLastName#" size="20"></td>
	<td width=20>&nbsp;</td>
	<td>Style of Case Contains:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="style" name="style" value="#local.strDocsQueueFilter.style#" size="25"></td>
</tr>
<tr>
	<td nowrap>Contributor DepoMemberDataID:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="depoMemberDataID" name="depoMemberDataID" value="#local.strDocsQueueFilter.depoMemberDataID#" size="10" maxlength="10"></td>
	<td width=20>&nbsp;</td>
	<td>Expert Name Contains:</td>
	<td width=10>&nbsp;</td>
	<td><input type="text" id="expertNameContains" name="expertNameContains" value="#local.strDocsQueueFilter.expertNameContains#" size="20"></td>
	<td width=20>&nbsp;</td>
	<td align="right">
		<input type="checkbox" id="hasAmazonCredits" name="hasAmazonCredits" value="1"<cfif local.strDocsQueueFilter.hasAmazonCredits EQ 1> checked</cfif>>
	</td>
	<td width=10>&nbsp;</td>
	<td>
		<label for="hasAmazonCredits">Limit to Documents With Amazon Credits</label>
	</td>
</tr>
<tr>
	<td>Contributing Association:</td>
	<td width=10>&nbsp;</td>
	<td colspan="5">
		<select id="orgCode" name="orgCode" style="font-family:monospace; font-size:10pt;">
		<option value="">Any Association</option>
		<cfloop query="qryAssociations">
			<option value="#qryAssociations.tlamemberstate#"<cfif local.strDocsQueueFilter.orgCode EQ qryAssociations.tlamemberstate> selected</cfif>>#qryAssociations.tlamemberstate# (<cfif len(qryAssociations.shortname)>#qryAssociations.shortname# - </cfif>#qryAssociations.description#)</option>
		</cfloop>
		</select>
	</td>
	<td width=20>&nbsp;</td>
	<td align="right">
		<input type="checkbox" id="excludeDisabledDocs" name="excludeDisabledDocs" value="1"<cfif local.strDocsQueueFilter.excludeDisabledDocs EQ 1> checked</cfif>>
	</td>
	<td width=10>&nbsp;</td>
	<td>
		<label for="excludeDisabledDocs">Exclude Disabled Documents</label>	
	</td>
</tr>
<tr>
	<td rowspan="3" valign="top">Document Status:</td>
	<td rowspan="3" width=10>&nbsp;</td>
	<td rowspan="3">
		<select name="documentStatusID" id="documentStatusID" size="4" style="width:100%;" multiple>
			<cfloop query="qryDocumentStatuses">
				<option value="#qryDocumentStatuses.statusID#"<cfif listLen(local.strDocsQueueFilter.documentStatusID) AND listFind(local.strDocsQueueFilter.documentStatusID,qryDocumentStatuses.statusID)> selected</cfif>>#qryDocumentStatuses.statusName#</option>
			</cfloop>
		</select>
	</td>
	<td rowspan="3" width=20>&nbsp;</td>
	<td>Deposition Date Between:</td>
	<td width=10>&nbsp;</td>
	<td>
		<input type="text" id="depositionDateFrom" name="depositionDateFrom" value="#DateFormat(local.strDocsQueueFilter.depositionDateFrom,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off"> to
		<input type="text" id="depositionDateTo" name="depositionDateTo" value="#DateFormat(local.strDocsQueueFilter.depositionDateTo,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off">
	</td>
	<td width=20>&nbsp;</td>
	<td>PDF Attachments:</td>
	<td width=10>&nbsp;</td>
	<td>
		<select name="docHasAttachments" id="docHasAttachments">
			<option value=""></option>
			<option value="1"<cfif local.strDocsQueueFilter.docHasAttachments EQ 1> selected</cfif>>Has Attachments</option>
			<option value="0"<cfif local.strDocsQueueFilter.docHasAttachments EQ 0> selected</cfif>>Does not have attachments</option>
		</select>
	</td>
</tr>
<tr>
	<td>Contribution Date Between:</td>
	<td>&nbsp;</td>
	<td>
		<input type="text" id="dateEnteredFrom" name="dateEnteredFrom" value="#DateFormat(local.strDocsQueueFilter.dateEnteredFrom,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off"> to 
		<input type="text" id="dateEnteredTo" name="dateEnteredTo" value="#DateFormat(local.strDocsQueueFilter.dateEnteredTo,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off">
	</td>
	<td width=20>&nbsp;</td>
	<td>Orig File Extension:</td>
	<td width=10>&nbsp;</td>
	<td>
		<select name="documentExtension" id="documentExtension" style="width:100%;">
			<option value=""></option>
			<optgroup label="Main Extensions">
			<cfloop query="local.strDocumentExtensions.qryDocumentExtensionsMain">
				<option value="#local.strDocumentExtensions.qryDocumentExtensionsMain.originalExt#" <cfif local.strDocsQueueFilter.documentExtension EQ local.strDocumentExtensions.qryDocumentExtensionsMain.originalExt> selected</cfif>>#local.strDocumentExtensions.qryDocumentExtensionsMain.originalExt#</option>
			</cfloop>
			</optgroup>
			<optgroup label="Other Extensions">
			<cfloop query="local.strDocumentExtensions.qryDocumentExtensionsOther">
				<option value="#local.strDocumentExtensions.qryDocumentExtensionsOther.originalExt#" <cfif local.strDocsQueueFilter.documentExtension EQ local.strDocumentExtensions.qryDocumentExtensionsOther.originalExt> selected</cfif>>#local.strDocumentExtensions.qryDocumentExtensionsOther.originalExt#</option>
			</cfloop>
			</optgroup>
		</select>
	</td>
</tr>
<tr>
	<td>Status Set Between:</td>
	<td width=10>&nbsp;</td>
	<td>
		<input type="text" id="statusSetFrom" name="statusSetFrom" value="#DateFormat(local.strDocsQueueFilter.statusSetFrom,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off"> to
		<input type="text" id="statusSetTo" name="statusSetTo" value="#DateFormat(local.strDocsQueueFilter.statusSetTo,'m/d/yyyy')#" size="7" maxlength="10" placeholder="mm/dd/yyyy" autocomplete="off">
	</td>
	<td width=20>&nbsp;</td>
	<td>XOD Pre-Approval:</td>
	<td width=10>&nbsp;</td>
	<td>
		<select name="xodpreapprove" id="xodpreapprove">
			<option value=""></option>
			<option value="1"<cfif local.strDocsQueueFilter.xodpreapprove EQ 1> selected</cfif>>Has a pre-approval document to view</option>
			<option value="0"<cfif local.strDocsQueueFilter.xodpreapprove EQ 0> selected</cfif>>Does not have a pre-approval document to view</option>
		</select>
	</td>
</tr>
<tr>
	<td>Upload Source:</td>
	<td width=10>&nbsp;</td>
	<td>
		<select name="uploadSourceID" id="uploadSourceID" size="4" style="width:100%;" multiple>
			<cfloop query="qryDocumentUploadSources">
				<option value="#qryDocumentUploadSources.uploadSourceID#"<cfif listLen(local.strDocsQueueFilter.uploadSourceID) AND listFind(local.strDocsQueueFilter.uploadSourceID,qryDocumentUploadSources.uploadSourceID)> selected</cfif>>#qryDocumentUploadSources.sourceName#</option>
			</cfloop>
		</select>
	</td>
	<td colspan="5"></td>
	<td valign="top">XOD Approval:</td>
	<td width=10>&nbsp;</td>
	<td valign="top">
		<select name="xodapprove" id="xodapprove">
			<option value=""></option>
			<option value="1"<cfif local.strDocsQueueFilter.xodapprove EQ 1> selected</cfif>>Has an approval document to view</option>
			<option value="0"<cfif local.strDocsQueueFilter.xodapprove EQ 0> selected</cfif>>Does not have an approval document to view</option>
		</select>
	</td>
</tr>
</table>
<div style="margin-left:10px;margin-top:20px;">
	<input type="button" name="btnSearchDocuments" id="btnSearchDocuments" value="Find Documents" onclick="filterDocuments();">&nbsp;
	<input type="button" name="btnExportDocuments" id="btnExportDocuments" value="Export Documents" onclick="exportDocuments();" style="display:none;">
	<span style="margin-left:10px;color:##0c6b7e; background-color:##cff3f8; border-color:##bceff5;position:relative; padding:.75rem 1.25rem;margin-bottom: 1rem;border: 1px solid transparent;border-radius: .65rem;">
		<span><strong>Tip:</strong> We will remember your filters the next time you come back to this tool in this session.</span>
	</span>
</div>
</form>
</cfoutput>

<div id="resultsGridContainer" style="margin-top:30px;"></div>

<cfoutput>
<script id="dialog_changeCreditType" type="text/html">
	<h3>Change Type of Credit</h3>
	<table>
		<tr>
			<td>Override Credit Type Designed:</td>
			<td>
				<select id="newCreditType" name="newCreditType" onchange="onChangeCreditTypeDropdown();">
					<option value="">--Select--</option>
					<option value="Cash Credits">Cash Credits</option>
					<option value="Amazon">Amazon</option>
				</select>
			</td>
		</tr>
		<tr class="rowCashCredits" style="display:none;">
			<td>Purchase Credit:</td>
			<td>
				<input type="text" id="purchaseCredit" name="purchaseCredit" value="" size="10">
			</td>
		</tr>
		<tr class="rowAmazon" style="display:none;">
			<td>Full Name of Receiver:</td>
			<td>
				<input type="text" id="depoAmazonBucksFullName" name="depoAmazonBucksFullName" value="" size="40">
			</td>
		</tr>
		<tr class="rowAmazon" style="display:none;">
			<td>Receiver Email Address:</td>
			<td>
				<input type="text" id="depoAmazonBucksEmail" name="depoAmazonBucksEmail" value="" size="40">
			</td>
		</tr>
		<tr class="rowAmazon" style="display:none;">
			<td>Default Value of Credit:</td>
			<td>
				<input type="text" id="depoAmazonBucksCredit" name="depoAmazonBucksCredit" value="0" size="10">
			</td>
		</tr>
	</table>
	<button type="button" id="btnChangeCreditType" style="margin-top:10px;" onclick="doChangeCreditType()">Change Type of Credit</button>
</script>
</cfoutput>