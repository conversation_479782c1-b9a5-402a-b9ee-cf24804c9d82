p {
	margin-left: 40px;
	font-size: 1.1em;
}
section#intro p { 
	margin-left: 0px;
}

.method-exampleCode {
	margin-top: 50px;
	margin-bottom: 20px;
}
.jsonblock-head {
	margin-left: 80px;
	font-weight: bold;
	margin-top: 20px;
}
.jsonblock-note {
	margin-left: 80px;
	font-style: italic;
}
.jsonblock-info {
	margin-left: 80px;
	width: 80%;
	min-width: 300px;
}
.jsonblock-table {
	margin-left: 80px;
}
.jsonblock-table td {
	padding-right:20px;
}
.jsonblock-table td.rc {
	font-family: Menlo,Monaco,Consolas,"Courier New",monospace;
}
.jsonblock {
	width: 85%;
	min-width: 300px;
	margin-left: 80px;
	margin-top:8px;
}
.method-wrapper {
	margin-left: 40px;
	white-space: nowrap;
	width: 100%;
}
.method {
	float: left;
	padding: 5px;
	font-weight: bold;
	font-size: 1.2em;
	width: 100px;
	text-align: center;
}
.method-text {
	width: 700px;
	float: left;
	padding: 5px;
	font-weight: bold;
	font-size: 1.2em;
}
.method-example {
	cursor: pointer;
}
.get .method  {
	background-color: #DBE9DB;
	color: #0F8011;
	border: 1px solid #DBE9DB;
}
.get .method-text  {
	background-color: #FFF;
	border: 1px solid #DBE9DB;
	color: #0F8011;
}
.post .method  {
	background-color: #DEEBF6;
	color: #2A97F0;
	border: 1px solid #DEEBF6;
}
.post .method-text  {
	background-color: #FFF;
	border: 1px solid #DEEBF6;
	color: #2A97F0;
}
.put .method  {
	background-color: #F8EDDD;
	color: #FCAF45;
	border: 1px solid #F8EDDD;
}
.put .method-text  {
	background-color: #FFF;
	border: 1px solid #F8EDDD;
	color: #FCAF45;
}
.patch .method  {
	background-color: #ffcaad;
	color: #e37335;
	border: 1px solid #ffcaad;
}
.patch .method-text  {
	background-color: #FFF;
	border: 1px solid #ffcaad;
	color: #e37335;
}
.delete .method  {
	background-color: #F6E1E0;
	color: #F23F3C;
	border: 1px solid #F6E1E0;
}
.delete .method-text  {
	background-color: #FFF;
	border: 1px solid #F6E1E0;
	color: #F23F3C;
}
.method-text div.codeicondiv {
	float: right;
	color: #666;
	font-weight:normal;
}
