﻿<a name="v1-member-loginurl"></a>
<section id="v1-member-loginurl">
	<h3>/member/loginurl</h3>
	<p>POST - Returns an serialized login link for a membernumber. Login links expire 5 minutes after they are generated.</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post method-example">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/loginurl</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Optional Request Details</div>
		<div class="jsonblock-info">
			The request may optionally contain a JSON object in the body with the following key:<br/>
			<ul>
			<li><i>returnurl</i> is the url you want to send the user upon login.</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Requests</div>
		<div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/loginurl HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock">
<pre class="prettyprint">
POST /v1/member/SAMPLE123456/loginurl HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 36

{
	"returnurl": "/?pg=listviewer",
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">401 UNAUTHORIZED</td><td>invalid member login credentials</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "loginurl":"LOGIN_LINK"
        "loginurlexpires":"MMMM, DD YYYY HH:MM:SS -0700"
        "membernumber":"SAMPLE123456"
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
401 UNAUTHORIZED

{
    "data": {},
    "error": true,
    "messages": [
        "Login failed."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>			

	<div class="delete">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>