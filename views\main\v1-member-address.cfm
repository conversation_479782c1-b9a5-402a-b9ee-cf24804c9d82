﻿<a name="v1-member-address"></a>
<section id="v1-member-address">
	<h3>/member/address</h3>
	<p>
		GET - Returns the member's addresses<br/>
		PUT - Updates a member's address<br/>
		DELETE - Removes a member's address<br/>
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/address</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-info">
			Each address listed in the response will have the following data:
			<ul>
				<li><i>type</i> - name of the address type</li>
				<li><i>attn</i> - the member's attention field for this address type, if enabled</li>
				<li><i>address1</i> - the member's address1 field for this address type</li>
				<li><i>address2</i> - the member's address2 field for this address type, if enabled</li>
				<li><i>address3</i> - the member's address3 field for this address type, if enabled</li>
				<li><i>city</i> - the member's city field for this address type</li>
				<li><i>stateprov</i> - the member's stateprov field for this address type</li>
				<li><i>postalcode</i> - the member's postalcode field for this address type</li>
				<li><i>country</i> - the member's country field for this address type</li>
				<li><i>county</i> - the member's county field for this address type, if enabled</li>
				<li><i>addresslastupdated</i> - the date the member's address was last updated</li>
				<li><i>addresslastvalidated</i> - the date the member's address was last validated, if enabled</li>
				<li><i>districtlastupdated</i> - the date the member's address districting was last updated, if enabled</li>
				<li><i>api_id</i> - unique identifer of the address type</li>
				<li><i>x-api-uri</i> - the URI of the member address type</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/address HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":2,
        "address": [
            {
                "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri":"/v1/member/SAMPLE123456/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "type":"Business Address",
                "attn":"",
                "address1":"123 AnyStreet",
                "address2":"",
                "address3":"",
                "city":"Anytown",
                "stateprov":"TX",
                "postalcode":"77777",
                "country":"United States",
                "county":"",
                "addresslastupdated":"1/1/2019"
            },
            {
                "api_id":"YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "x-api-uri":"/v1/member/SAMPLE123456/address/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
                "type":"Home Address",
                "attn":"",
                "address1":"678 OtherStreet",
                "address2":"",
                "address3":"",
                "city":"Othertown",
                "stateprov":"TX",
                "postalcode":"77777",
                "country":"United States",
                "county":"",
                "addresslastupdated":"1/1/2019"
            }
         ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/address/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			If the member has an address defined for this address type, the address listed in the response will have the same data as the /member/address GET call.<br/>
			If the member does not have an address defined for this address type, the response will be a 404 Member Address Not Found.
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/member/SAMPLE123456/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 ADDRESS TYPE NOT FOUND</td><td>invalid address api_id</td></tr>
			<tr><td class="rc">404 MEMBER ADDRESS NOT FOUND</td><td>member does not have an address for this address type</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":1,
        "address": {
            "api_id":"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri":"/v1/member/SAMPLE123456/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "type":"Business Address",
            "attn":"",
            "address1":"123 AnyStreet",
            "address2":"",
            "address3":"",
            "city":"Anytown",
            "stateprov":"TX",
            "postalcode":"77777",
            "country":"United States",
            "county":"",
            "addresslastupdated":"1/1/2019"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER ADDRESS NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member Address not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put method-example">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/address/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Required Request Details</div>
		<div class="jsonblock-info">
			The request must contain a JSON object in the body with the address parts to update.<br/>
			<div style="margin-left:30px;">
				<i>attn</i> - the member's attention field for this address type, if enabled<br/>
				<i>address1</i> - the member's address1 field for this address type<br/>
				<i>address2</i> - the member's address2 field for this address type, if enabled<br/>
				<i>address3</i> - the member's address3 field for this address type, if enabled<br/>
				<i>city</i> - the member's city field for this address type<br/>
				<i>stateprov</i> - the member's stateprov field for this address type<br/>
				<i>postalcode</i> - the member's postalcode field for this address type<br/>
				<i>country</i> - the member's country field for this address type<br/>
				<i>county</i> - the member's county field for this address type, if enabled<br/>
			</div>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 96

{
    "address1": "123 AnyStreet",
    "city": "Anytown",
    "stateprov": "TX",
    "postalCode": "77777"
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>no changes to process</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 ADDRESS TYPE NOT FOUND</td><td>invalid address api_id</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error updating address</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Business Address City changed from Othertown to Anytown",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (no changes)</div>
		<div class="jsonblock">
<pre class="prettyprint">
202 NOT UPDATED

{
    "data": {
        "result": "No changes to process.",
        "report": [
            "summary": [
                "1 data change was rejected and ignored.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [
                "LastName, NewFirstName (SAMPLE123456) - Business Address Country was not provided"
            ],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": []
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update member.",
        ...
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="delete method-example">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">/v1/member/{membernumber}/address/{api_id}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/member/SAMPLE123456/address/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">202 NOT UPDATED</td><td>member did not have an address for this address type</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 ADDRESS TYPE NOT FOUND</td><td>invalid address api_id</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error deleting address</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - Business Address Address1 changed from 123 AnyStreet to [blank]",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>