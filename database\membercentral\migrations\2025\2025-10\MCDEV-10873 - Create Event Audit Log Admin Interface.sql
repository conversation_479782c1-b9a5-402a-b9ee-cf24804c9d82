USE membercentral
GO

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	DECLARE @level1NavigationID INT, @level2NavigationID INT, @level3NavID INT, @toolTypeID INT, @resourceTypeID INT, @resourceTypeFunctionID INT;

	SELECT @level1NavigationID = navigationID
	FROM dbo.admin_navigation
	WHERE navName = 'Events' AND navAreaID = 1;

	SELECT @level2NavigationID = navigationID
	FROM dbo.admin_navigation
	WHERE navName = 'Events & Registration' AND navAreaID = 2 AND parentNavigationID = @level1NavigationID;

	BEGIN TRAN;

		SELECT @toolTypeID = dbo.fn_getAdminToolTypeID('EventAdmin');
		SELECT @resourceTypeID = dbo.fn_getResourceTypeID('Event');

		EXEC dbo.createAdminNavigation
			@navName = 'Event Audit Log',
			@navDesc = 'View event audit log entries',
			@parentNavigationID = @level2NavigationID,
			@navAreaID = 3,
			@cfcMethod = 'viewEventAuditLog',
			@isHeader = 0,
			@showInNav = 1,
			@helpLink = '',
			@iconClasses = '',
			@navigationID = @level3NavID OUTPUT;

		-- Use existing manageAssetTypes function for permissions (similar to how SW uses manageSWSettingsAll)
		SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID, dbo.fn_getResourceFunctionID('editEvent', @resourceTypeID));
		EXEC dbo.createAdminFunctionsDeterminingNav
			@resourceTypeFunctionID = @resourceTypeFunctionID,
			@toolTypeID = @toolTypeID,
			@navigationID = @level3NavID;

	COMMIT TRAN;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

-- Creating Admin Suite for all sites
DECLARE @siteID INT;
SELECT @siteID = MIN(siteID) FROM dbo.sites;
WHILE @siteID IS NOT NULL BEGIN
	EXEC dbo.createAdminSuite @siteID = @siteID;
	SELECT @siteID = MIN(siteID) FROM dbo.sites WHERE siteID > @siteID;
END
GO
