<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/event</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Optional Request Details</div>
	<div class="jsonblock-info">
		The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
		<ul>
		<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
		<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
		<li><i>search</i> is a structure of subkeys used to limit the events returned. Eligible subkeys are:<br/>
			<i>datefrom</i> - defaulted to today<br/>
			<i>dateto</i> - defaulted to 30 days from today<br/>
			<i>calendar</i> - the calendar of the event<br/>
			<i>category</i> - the category of the event<br/>
			<i>title</i> - the title of the event<br/>
			<i>eventcode</i> - the event code of the event<br/>
			<i>regtype</i> - the registration type of the event, either Reg or RSVP<br/>
			If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
		</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Requests</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 29

{
    "count": 5,
    "start": 10
}
</pre>
	</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/event HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 97

{
    "count": 5,
    "start": 10,
    "search": {
        "datefrom": "<cfoutput>#dateFormat(now(),"m/d/yyyy")#</cfoutput>",
        "dateto": "<cfoutput>#dateFormat(DateAdd("m",1,now()),"m/d/yyyy")#</cfoutput>",
        "calendar": "Association Calendar and Events",
        "category": "Special Event",
        "title": "Fall Regional Conference",
        "eventcode": "ABCDEFGH",
        "regtype": "Reg"
    }
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 10,
        "event": [
            {
                "title": "Sample Event",
                "startdate": "2020-02-01 17:00:00",
                "enddate": "2020-02-01 18:30:00",
                "timezone": "US/Central",
                "eventcode": "ABCDEFGH",
                "status": "Active",
                "alldayevent": 0,
                "hidefromcalendar": 0,
                "event_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri": "/v1/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>