<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<!--- variables --->
        <cfset variables.membershipDuesUID = "16EBB5C2-A205-486C-9300-DBF7A7B9F116"/>
        <cfset variables.subTypeSectionUID = "D30626F9-782F-47D3-8E66-A7F970B3F33E"/>
        <cfset variables.subTypeCommitteeUID = "518877D3-B0C2-43D2-AC59-0EB88E98A344"/>
        <cfset variables.subTypeUIDs = "#variables.subTypeSectionUID#,#variables.subTypeCommitteeUID#"/>

        <cfscript>
            
            /* ************************* */
            /* Custom Page Custom Fields */
            /* ************************* */
            local.arrCustomFields = [];				

            local.tmpField = { name="Step1Header", type="CONTENTOBJ", desc="Form title and instructions at the top of Step 1.", value="Please make changes below and click continue." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="NonMemberMessage", type="CONTENTOBJ", desc="Message displayed to users without Membership subscription.", value="Sections and committees are a benefit of Dallas Bar Membership. To join a section, you must first become a Dallas Bar Member. Please <a href='/?pg=join'>click here</a> here to join." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="NoSubsMessage", type="STRING", desc="Message displayed to users who have a Membership but do not currently have any Section subscriptions.", value="You are not currently a member of any Dallas Bar Sections or Committees." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="InstructionsRemove", type="CONTENTOBJ", desc="Instructions above Remove sections.", value="Your Dallas Bar membership includes the following sections or committees. To remove one, uncheck the box next to the group name." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="InstructionsAdd", type="CONTENTOBJ", desc="Instructions above Add sections.", value="If you would like to add or update the sections and committees to your Dallas Bar Membership, please check the box next to the section(s) you wish to join." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="Step2Header", type="CONTENTOBJ", desc="Instructions at the top of Step 2.", value="Please confirm your selections." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="PayMethodCredit", type="STRING", desc="Payment Profile Code of Credit Card gateway.", value="DBACreditCards" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="PayMethodCheck", type="STRING", desc="Payment Profile Code of Pay Later gateway.", value="" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);
            
            local.tmpField = { name="PayMethodACH",type="STRING",desc="Payment Profile Code of ACH gateway.",value="" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="ConfirmationMessage", type="CONTENTOBJ", desc="Message displayed on confirmation screen/email.", value="Thank you for updating your section  and committee subscriptions." }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="ConfirmationSub", type="STRING", desc="Subject line of emailed user confirmation", value="Section & Committee Update Receipt" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="StaffConfirmationSub", type="STRING", desc="Subject line of emailed staff confirmation", value="Section & Committee Update Form Submission" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="StaffConfirmationTo", type="STRING", desc="who do we send staff confirmations to", value="<EMAIL>" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            local.tmpField = { name="ConfirmationFrom", type="STRING", desc="who do we send member confirmations from", value="<EMAIL>" }; 
            arrayAppend(local.arrCustomFields, local.tmpField);

            variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);
        
            /* ***************** */
            /* set form defaults */
            /* ***************** */
            StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmUpdateSections',
                formNameDisplay='Section Update Form Submission',
                orgEmailTo=variables.strPageFields.StaffConfirmationTo,
                memberEmailFrom=variables.strPageFields.ConfirmationFrom
            ));
            
            variables.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=variables.memberID,orgID=variables.orgID);
        </cfscript>

         <!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "#cgi.SCRIPT_NAME#?#getBaseQueryString(false)#">        
		
    </cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
        <cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">
        
        <cfif isValidMember(variables.memberID) EQ false>
            <cfset local.returnHTML = showError(errorCode='notfound')>
        <cfelse>

            <cfswitch expression="#arguments.event.getValue('fa','showForm')#">
                <cfcase value="error">
                    <cfset local.returnHTML = showError(errorCode=arguments.event.getValue('sid',''))>
                </cfcase>
                <cfcase value="processStep1">
                    <cfset local.returnHTML = processStep1(event=arguments.event)>
                </cfcase>
				<cfcase value="paymentErr">
				   <cfset local.returnHTML = processStep1(event=arguments.event)>
				</cfcase>
                <cfcase value="processStep2">	
                    <cfset processStep2(event=arguments.event)>
                </cfcase>
                <cfcase value="complete">
                    <cfif NOT isDefined("session.invoice")>
                        <cflocation url="#variables.baselink#" addtoken="false">
                    <cfelse>
                        <cfsavecontent variable="local.returnHTML">
                            <cfoutput>
                                <p>#variables.strPageFields.ConfirmationMessage#</p><hr/>
                                #session.invoice#   
                                <br/>                             
                            </cfoutput>
                        </cfsavecontent>
                        <cfset StructDelete(session,"invoice")>
                    </cfif>
                </cfcase>
                <cfdefaultcase>
					<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
					  <cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
				   </cfif>
				   <cfif application.mcCacheManager.sessionValueExists("subReturn")>
					  <cfset application.mcCacheManager.sessionDeleteValue("subReturn")>
				   </cfif>
                    <cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=variables.memberNumber, customFields=['Contact Type'])>
                    <cfset local.subTypeUID = variables.subTypeUIDs/>
                     
                    <cfset local.qryCurrentSubscriptions = getCurrentSubscriptions(memberID = variables.memberID, subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>
                    <cfset local.qryAvailableSections = getAvailableSections(memberID = variables.memberID, existingSubsList = valueList(local.qryCurrentSubscriptions.subscriptionID), subTypeUID = local.subTypeUID, memberID = variables.memberID)>
  
                    <cfsavecontent variable="local.headCode">
                        <cfoutput>
                            <style>
                                ##updatePage input[type="checkbox"]{
                                    margin:0px!important;
                                }
                            </style>
                            <script type="text/javascript">
                                function hideAlert() { $('##issuemsg').html('').hide(); };
                                function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show(); };

                                function _FB_validateForm() {
                                    var thisForm = document.forms["#variables.formName#"];
                                    var arrReq = new Array();	
                                    hideAlert();

                                    var arrReq = new Array();
                                    var numSubsToRemove = $("[name='subsToExpire']:checked").length;
                                    var numSubsToAdd = $("[name='newSubs']:checked").length;

                                    if (numSubsToAdd==0 && numSubsToRemove==0) arrReq[arrReq.length] = 'You did not make any changes to your sections.';


                                    if (arrReq.length > 0) {
                                        var msg = 'Please address the following issues with your application:<br/><ul>';
                                        for (var i=0; i < arrReq.length; i++) msg += '<li>' + arrReq[i] + '</li>';
                                        msg += '</ul>';
                                        showAlert(msg);
                                        $('html,body').animate({scrollTop: $('##issuemsg').offset().top-100},500);		
                                        return false;
                                    }
                                    $("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
                                    return true;
                                }	

                                function getTotalAmount() {
                                    var totalAmount = 0;

                                    $("##subbox input[type=checkbox]:checked").each(function(){                                        
                                        totalAmount += parseFloat($(this).attr('data-price'));                                         
                                    });

                                    return totalAmount;
                                }	

                                function checkPrice(cb){
                                    $('##subbox input[type=checkbox]:checked').each(function(){
                                        if ($(this).attr('data-chkboxgroup') == cb.attr('data-chkboxgroup') && $(this).attr('id') != cb.attr('id'))
                                            $(this).prop("checked", false).attr('checked',false);
                                    });
                                    var totalAmount = getTotalAmount();
                                        totalAmount = totalAmount.toFixed(2).replace(/./g, function(c, i, a) {
                                            return i && c !== "." && ((a.length - i) % 3 === 0) ? ',' + c : c;
                                        });	
                                    $("##totalAmount").html("$ " + totalAmount);
                                    limitSelection();
                                }	
                                function changeSubsToExpire(cb){
                                    if(cb.is(':checked')){
                                        $("[name='subsToExpire'][value='"+cb.val()+"']").prop("checked", false).attr('checked',false);
                                        $(cb).siblings('i').hide();
                                    }else{
                                        $("[name='subsToExpire'][value='"+cb.val()+"']").prop("checked", true).attr('checked',true);
                                        $(cb).siblings('i').show();
                                    }
                                    limitSelection();
                                }

                                function limitSelection(){
                                    if($("[isCommittees='true']input:checked").length == 3){
                                        var optionValues = [];
                                        $("[isCommittees='true']input:checked").each(function() { optionValues.push($(this).val()) });
                                        var selectList = "[value!='" + optionValues.join("'][value!='") + "']";
                                        $("[isCommittees='true']"+selectList).attr('disabled',true);
                                    }else{
                                        $("[isCommittees='true']").attr('disabled',false);
                                    }
                                }		
                                
                                $(document).ready(function(){                                    
                                    var subsToExpire = $.trim("#arguments.event.getValue('subsToExpire','')#");
                                    var newSubs = $.trim("#arguments.event.getValue('newSubs','')#");
                                    var subsToExpireAry = [];
                                    var newSubsAry = [];
                                    if(subsToExpire.length){
                                        subsToExpireAry = subsToExpire.split(",");
                                    }
                                    if(newSubs.length){
                                        newSubsAry = newSubs.split(",");
                                    }
                                    $.each(newSubsAry, function( index, value ) {
                                        $("[value='"+value+"']").trigger("click");
                                    });
                                    $.each(subsToExpireAry, function( index, value ) {
                                        $("[name='subsToExpire'][value='"+value+"']").prop("checked", true).attr('checked',true);
                                        $("[name='tmpsubsToExpire'][value='"+value+"']").prop("checked", false).attr('checked',false);
                                    });
                                    limitSelection();
                                });	
                                					
                            </script>
                        </cfoutput>
                    </cfsavecontent>
                    <cfhtmlhead text="#local.headCode#">

                    <cfsavecontent variable="local.returnHTML">
                        <cfoutput>
                            <div id="updatePage">
                                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
                                    <cfinput type="hidden" name="fa" id="fa" value="processStep1">
                                
                                    <div class="row-fluid">
                                        <div class="span12">						
                                            <div id="issuemsg" style="display:none;padding:10px;"></div>
                                        </div>
                                    </div>
                                    <div class="row-fluid">
                                        <div class="span12">
                                            #variables.strPageFields.Step1Header#
                                        </div>
                                    </div>
                                    <div class="row-fluid">
                                        <div class="span12">
                                            #variables.strPageFields.InstructionsRemove#
                                        </div>
                                    </div>
                                    <cfquery name="local.subTypeSectionCurrentCount" dbtype="query">
                                        select count(*) as total from [local].qryCurrentSubscriptions where addonuid = '#variables.subTypeSectionUID#' 
                                    </cfquery>                        
                                    <cfquery name="local.subTypeCommitteeCurrentCount" dbtype="query">
                                        select count(*) as total from [local].qryCurrentSubscriptions where addonuid = '#variables.subTypeCommitteeUID#' 
                                    </cfquery>
                                    <cfquery name="local.subTypeSectionCount" dbtype="query">
                                        select count(*) as total from [local].qryAvailableSections where addonuid = '#variables.subTypeSectionUID#' 
                                    </cfquery>                        
                                    <cfquery name="local.subTypeCommitteeCount" dbtype="query">
                                        select count(*) as total from [local].qryAvailableSections where addonuid = '#variables.subTypeCommitteeUID#' 
                                    </cfquery>
                                    <div id="remsubbox">
                                        <div class="well">
                                            <legend>Your Current Sections</legend>
                                            <div class="row-fluid">
                                                <div class="span12">   
                                                    <cfif val(local.subTypeSectionCurrentCount.total) neq 0>                                 
                                                        <cfoutput query="local.qryCurrentSubscriptions">  
                                                            <cfif local.qryCurrentSubscriptions.addonuid eq variables.subTypeSectionUID>                                                                                                                          
                                                                <div>
                                                                    <input type="checkbox" style="visibility: hidden;" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"</cfif> name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
                                                                    value="#local.qryCurrentSubscriptions.subscriberID#"> 

                                                                    <input type="checkbox" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"<cfelse>checked="checked"</cfif> name="tmpsubsToExpire" id="tmpsubsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
                                                                    value="#local.qryCurrentSubscriptions.subscriberID#" onChange="changeSubsToExpire($(this));"> 
                                                                    #local.qryCurrentSubscriptions.subscriptionName# <i style="color:red" class="hide">This section membership will be expired.</i>
                                                                </div>
                                                            </cfif>                     									
                                                        </cfoutput> 
                                                    <cfelse>
                                                        #variables.strPageFields.NoSubsMessage#
                                                    </cfif>
                                                </div>                                  
                                            </div>
                                        </div>
                                         <div class="well">
                                            <legend>Your Current Committees</legend>
                                            <div class="row-fluid">
                                                <div class="span12">   
                                                    <cfif val(local.subTypeCommitteeCurrentCount.total) neq 0>                                 
                                                        <cfoutput query="local.qryCurrentSubscriptions">  
                                                            <cfif local.qryCurrentSubscriptions.addonuid eq variables.subTypeCommitteeUID>                                                                                                                          
                                                                <div>
                                                                    <input type="checkbox" style="visibility: hidden;" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"</cfif> name="subsToExpire" id="subsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
                                                                    value="#local.qryCurrentSubscriptions.subscriberID#"> 

                                                                    <input type="checkbox" <cfif ListFindNoCase('R,O',local.qryCurrentSubscriptions.childStatusCode) >disabled="disabled"<cfelse>checked="checked"</cfif> name="tmpsubsToExpire" id="tmpsubsToExpire#local.qryCurrentSubscriptions.subscriberID#" 
                                                                    value="#local.qryCurrentSubscriptions.subscriberID#" onChange="changeSubsToExpire($(this));" isCommittees="true">
                                                                    #local.qryCurrentSubscriptions.subscriptionName# <i style="color:red" class="hide">This section membership will be expired.</i>
                                                                </div>
                                                            </cfif>                     									
                                                        </cfoutput> 
                                                    <cfelse>
                                                        #variables.strPageFields.NoSubsMessage#
                                                    </cfif>
                                                </div>                                  
                                            </div>
                                        </div>                                        
                                    </div>
                                    <div class="row-fluid">
                                        <div class="span12">
                                            #variables.strPageFields.InstructionsAdd#
                                        </div>
                                    </div>
                                    <div id="subbox">
                                        <div class="well" >
                                            <legend>Sections</legend>
                                            <div class="row-fluid">                                               
                                                <cfset local.j = 1>                                        
                                                <cfoutput query="local.qryAvailableSections">  
                                                    <cfif local.qryAvailableSections.addonuid eq variables.subTypeSectionUID>    
                                                        <cfif local.j eq 1>
                                                            <div class="span6">
                                                        </cfif>                                  
                                                        <div>
                                                            <input type="checkbox" data-price="#local.qryAvailableSections.rateAmt#" value="#local.qryAvailableSections.subIDrateID#" 
                                                                data-chkboxgroup="sectionDivisionGrp#local.qryAvailableSections.subscriptionID#" name="newSubs" 
                                                                id="sectionDivision#local.qryAvailableSections.subIDrateID#" onChange="checkPrice($(this));"> 
                                                            #local.qryAvailableSections.subscriptionName# #dollarFormat(local.qryAvailableSections.rateAmt)#
                                                        </div>
                                                        <cfif (local.j) eq Round(val(local.subTypeSectionCount.total)/2)>
                                                            </div>
                                                            <div class="span6">
                                                        </cfif>
                                                        <cfset local.j = local.j+1>	
                                                    </cfif>                                         										
                                                </cfoutput>
                                                <cfif local.j GT 1>
                                                    </div>
                                                </cfif>  
                                            </div>
                                        </div>
                                        <div class="well">
                                            <legend>Committees</legend>
                                            <div class="row-fluid">
                                                <cfset local.j = 1>                                        
                                                <cfoutput query="local.qryAvailableSections">    
                                                    <cfif local.qryAvailableSections.addonuid eq variables.subTypeCommitteeUID>  
                                                        <cfif local.j eq 1>
                                                            <div class="span6">
                                                        </cfif>                                  
                                                        <div>
                                                            <input type="checkbox" data-price="#local.qryAvailableSections.rateAmt#" value="#local.qryAvailableSections.subIDrateID#" 
                                                                data-chkboxgroup="sectionDivisionGrp#local.qryAvailableSections.subscriptionID#" name="newSubs" 
                                                                id="sectionDivision#local.qryAvailableSections.subIDrateID#" onChange="checkPrice($(this));" isCommittees="true"> 
                                                            #local.qryAvailableSections.subscriptionName#
                                                        </div>
                                                        <cfif (local.j) eq Round(val(local.subTypeCommitteeCount.total)/2)>
                                                            </div>
                                                            <div class="span6">
                                                        </cfif>
                                                        <cfset local.j = local.j+1>	         
                                                    </cfif>                                										
                                                </cfoutput>
                                                <cfif local.j GT 1>
                                                    </div>
                                                </cfif>  
                                            </div>
                                        </div>
                                    </div>
                                    <div class="well">
                                        <div class="row-fluid">
                                            <div class="span10">
                                                <b>Amount Due:</b>
                                            </div>
                                            <div class="span2">
                                                <b><span id="totalAmount">$ 0.00</span></b>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row-fluid">
                                        <div class="span12">
                                            <button type="submit" class="btn btn-default pull-right" name="btnToStep2">Continue</button>
                                        </div>
                                    </div>
                                </cfform>
                            </div>                           
                        </cfoutput>
                    </cfsavecontent>
                </cfdefaultcase>
            </cfswitch>
        </cfif>        

        <cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>

    <cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

        <cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=variables.memberNumber, customFields=['Contact Type'])>
        <cfset local.subTypeUID = variables.subTypeUIDs/>

        <cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''), subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>
		
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
		  <cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire, subTypeUID=local.subTypeUID, subUID = variables.membershipDuesUID)>
	   <cfelse>
		  <cfset local.strNewSubs = application.mcCacheManager.sessionGetValue('strNewSubs',{})>
	   </cfif>

        <cfset local.paymentRequired = false>
        <cfif local.strNewSubs.newSubsAmount gt 0>

            <cfset local.arrPayMethods = []>
            <cfif len(variables.strPageFields.PayMethodCredit) gt 0 AND variables.strPageFields.PayMethodCredit neq 'NULL'>
                <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodCredit)>		
            </cfif>
            <cfif len(variables.strPageFields.PayMethodCheck) gt 0 AND variables.strPageFields.PayMethodCheck neq 'NULL'>
                <cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodCheck)>		
            </cfif>
            <cfif len(variables.strPageFields.PayMethodACH) gt 0 AND variables.strPageFields.PayMethodACH neq 'NULL'>
				<cfset ArrayAppend(local.arrPayMethods, variables.strPageFields.PayMethodACH)>		
			</cfif>

            <cfset local.strReturn = application.objCustomPageUtils.renderPaymentForm(
                    arrPayMethods=local.arrPayMethods, 
                    siteID=variables.siteID, 
                    memberID=variables.memberID, 
                    title="Choose Your Payment Method", 
                    formName=variables.formName, 
                    backStep="backSection")>
            <cfset local.paymentRequired = true>
		</cfif>

        <cfsavecontent variable="local.headCode">
            <cfoutput>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
                <style type="text/css">
                    div.tsAppSectionContentContainer{
                        border: 1px solid ##e3e3e3;
                        -webkit-border-radius: 4px;
                        -moz-border-radius: 4px;
                        border-radius: 4px;
                    }
                </style>
                
            	<script type="text/javascript"> 
                    function validatePaymentForm(isPaymentRequired) {
						
						if(isPaymentRequired == true){
							var arrReq = mccf_validatePPForm();
							if (arrReq.length > 0) {
								var msg = '';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
								showAlert(msg);
								return false;
							}
						}
						//$("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
                        
						return true;
					}		

                    function _FB_validateForm() {
                        var thisForm = document.forms["#variables.formName#"];
                        var arrReq = new Array();	
                        
                        $("button[type='submit'],input[type='submit']", thisForm).html("Please Wait...").attr('disabled', 'disabled');
                        return true;
                    }				
					
					function hideAlert() { $('##issuemsg').html('').hide(); };
					function showAlert(msg) { $('##issuemsg').html(msg).attr('class','alert-danger').show();$('html, body').animate({ scrollTop: 0 }, 500); };	
                    function mc_goBackForm(form,type){
                        $("[name='fa']").val('');
                        form.submit();
                    }
                    
                   $(document).ready(function(){
                        $('[name=mccf_payMeth]').change(function() {
                            var btnContinue = $("##mccfdiv_"+this.value.replace(/\s/g, '')).find("[name='btnContinue']");
                            btnContinue.removeClass('btn').addClass('btn');
                            $("##mccfdiv_"+this.value.replace(/\s/g, '')).find("[name='btnBack']").removeClass('btn').addClass('btn').html("Back to Previous Step");
                            if(this.value == "#variables.strPageFields.PayMethodCredit#"){
                                var meth = "#variables.strPageFields.PayMethodCredit#";
                                var btnContinue = $("##mccfdiv_"+meth.replace(/\s/g, '')).find("[name='btnContinue']");
                                btnContinue.removeClass('btn').addClass('btn');
                                $("##mccfdiv_"+meth.replace(/\s/g, '')).find("[name='btnBack']").removeClass('btn').addClass('btn').html("Back to Previous Step");                            
                                btnContinue.html("Confirm Changes & Pay");   
                                $("##mccfdiv_"+meth.replace(/\s/g, '')).css('overflow','unset');
                                $("##mccfdiv_"+meth.replace(/\s/g, '')).css('height','auto');
                                $("##mccfdiv_"+meth.replace(/\s/g, '')).show();
                                btnContinue.html("Confirm Changes & Pay");                            
                            }
                            else if(this.value == "#variables.strPageFields.PayMethodCheck#" || this.value == "#variables.strPageFields.PayMethodACH#"){
                                btnContinue.html("Confirm Changes");
                            }
                        });
						
						<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
							setTimeout(function(){ 
								$("button[name='btnBack']").remove();
								strErr = $('.payErrMsg').html()
								$("button[name='btnContinue']").closest('div').append(strErr);
								$("button[name='btnContinue']").closest('div').find('> p').addClass('payErrP');
							}, 1000);
							setTimeout(function(){
								$("button[name='btnContinue']").closest('div').find('.payErrP').remove();
							}, 10000);
						</cfif>
                    });
				</script>
            </cfoutput>
        </cfsavecontent>
        <cfif local.strNewSubs.newSubsAmount gt 0>
            <cfhtmlhead text="#local.headcode#">
        </cfif>
        <cfset local.subSummary = ""/>
        <cfsavecontent variable="local.returnHTML">
            <cfoutput>
                <cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm();">
                    <cfinput type="hidden" name="fa" id="fa" value="processStep2">
                    <cfinput type="hidden" name="subsToExpire" id="subsToExpire" value="#local.strsubsToExpire.subsToExpire#">
                    <cfinput type="hidden" name="newSubs" id="newSubs" value="#local.strNewSubs.newSubs#">
                    <div class="row-fluid">
                        <button name="btnBack" type="button" class="tsAppBodyButton btn pull-right" onclick="hideAlert();mc_goBackForm($('###variables.formName#'),'backSection');">Back to Previous Step</button>
                    </div>
                    <div class="row-fluid">
                        <div class="span12">						
                            <div id="issuemsg" style="display:none;padding:10px;margin-top: 10px;margin-bottom: 10px;"></div>
                        </div>
                    </div>
                    <div class="row-fluid">
                        <div class="span12">
                            #variables.strPageFields.Step2Header#
                        </div>
                    </div>

                    <div class="well">                   
                        <cfif listLen(local.strsubsToExpire.subsToExpire)>
                            <cfloop query="local.strSubsToExpire.qrySubsToExpire">
                                <div class="row-fluid">
                                    <div class="span12">
                                        REMOVE: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                        <cfset local.subSummary = local.subSummary & "REMOVE: #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                        "/>
                                    </div>
                                </div>
                            </cfloop>
                        </cfif>
                        <cfif listLen(local.strNewSubs.newSubs)>
                            <cfloop query="local.strNewSubs.qrySubsToAdd">
                                <div class="row-fluid">
                                    <div class="span12">
                                        <div class="span10">ADD: #local.strNewSubs.qrySubsToAdd.subscriptionName#</div>
                                        <div class="span2 pull-right">#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#</div>
                                        <cfset local.subSummary = local.subSummary & "ADD: #local.strNewSubs.qrySubsToAdd.subscriptionName# - #dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#
                                        "/>
                                    </div>
                                </div>
                            </cfloop>
                            <cfif local.strNewSubs.newSubsAmount gt 0>
                                <div class="row-fluid">
                                    <div class="span10"><b>Amount Due for New Sections:</b></div>
                                    <div class="span2 pull-right"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b></div>
                                </div>
                            </cfif>
                        </cfif>
                    </div>

                    <cfif local.strNewSubs.newSubsAmount gt 0>
                        #local.strReturn.paymentHTML#
                    <cfelse>
                        <div class="row-fluid">
                            <div class="span12">
                                <button name="btnContinue" type="submit" class="tsAppBodyButton btn" onclick="hideAlert();">Confirm Changes</button>
                                <button name="btnBack" type="button" class="tsAppBodyButton btn pull-right" onclick="hideAlert();mc_goBackForm($('###variables.formName#'),'backSection');">Back to Previous Step</button>
                            </div>  
                        </div>  
                    </cfif>
					<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
						<cfset local.paymentObj = application.mcCacheManager.sessionGetValue('paymentObj',{})>
						<span class="payErrMsg hide">#local.paymentObj.accResponseMessage#</span>
					</cfif>
                    <cfinput type="hidden" name="subSummary" id="subSummary" value="#local.subSummary#">
                </cfform>
                <cfif local.paymentRequired>
					#local.strReturn.headcode#
				</cfif>
            </cfoutput>
        </cfsavecontent>
                
		<cfreturn local.returnHTML>
	</cffunction>

	<cffunction name="processStep2" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">

        <cfset var local = structNew()>
        <cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=variables.memberNumber, customFields=['Contact Type'])>
        <cfset local.subTypeUID = variables.subTypeUIDs/>
 
        <cfset local.profileID = 0/>
        <cfset local.profileCode = ""/>
        <cfif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#">
            <cfset local.profileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.strPageFields.PayMethodCredit)>
            <cfset local.profileCode = variables.strPageFields.PayMethodCredit>
        <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCheck#">
            <cfset local.profileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.strPageFields.PayMethodCheck)>
            <cfset local.profileCode = variables.strPageFields.PayMethodCheck>
        <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodACH#">
            <cfset local.profileID = application.objCustomPageUtils.acct_getProfileID(siteid=variables.siteID,profileCode=variables.strPageFields.PayMethodACH)>
            <cfset local.profileCode = variables.strPageFields.PayMethodACH>
        </cfif>

		<cfset local.qryCurrentSubscriptions = getCurrentSubscriptions(memberID = variables.memberID, subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>
		<cfset local.strSubsToExpire = validateSubsToExpire(subsToExpire=arguments.event.getValue('subsToExpire',''), subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
			<cfset local.strNewSubs = validateNewSubs(newSubs=arguments.event.getValue('newSubs',''), subsToExpire=local.strsubsToExpire.subsToExpire, subTypeUID=local.subTypeUID, subUID = variables.membershipDuesUID)>
		<cfelse>
			<cfset local.strNewSubs = application.mcCacheManager.sessionGetValue('strNewSubs',{})>
		</cfif>	

		<cfif local.strNewSubs.newSubsAmount gt 0 and arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#" and NOT val(arguments.event.getTrimValue('p_#local.profileID#_mppid','0'))>
			<cflocation url="#variables.baselink#&fa=error&sid=paymentFailed" addtoken="no">
		</cfif>
        <cfset local.qryHistorySectionUpdate = application.objCustomPageUtils.mh_getCategory(siteID=variables.siteID, parentCode='General', subName='Section Committee Update')>
       
        <cfif local.strNewSubs.newSubsAmount gt 0>
            <cfset local.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.memberID, categoryID=local.qryHistorySectionUpdate.categoryID, 
													subCategoryID=local.qryHistorySectionUpdate.subCategoryID, description=arguments.event.getValue('subSummary',''), 
													enteredByMemberID=variables.memberID, newAccountsOnly=false,dollarAmt=local.strNewSubs.newSubsAmount)>	
        <cfelse>
            <cfset local.useHistoryID = application.objCustomPageUtils.mh_addHistory(memberID=variables.memberID, categoryID=local.qryHistorySectionUpdate.categoryID, 
													subCategoryID=local.qryHistorySectionUpdate.subCategoryID, description=arguments.event.getValue('subSummary',''), 
													enteredByMemberID=variables.memberID, newAccountsOnly=false)>	
        </cfif>


        <cfset local.qryMemberShip = getMembershipByStatus(statusCodeList='A,P,R,O',subUID = variables.membershipDuesUID)>
        <cfquery name="local.qryGetSubActive" dbtype="query">
            select * from [local].qryMemberShip where statusCode = 'A' 
        </cfquery>

        <cfquery name="local.qryGetSubAccepted" dbtype="query">
            select * from [local].qryMemberShip where statusCode = 'P' 
        </cfquery>

        <cfquery name="local.qryGetSubBilled" dbtype="query">
            select * from [local].qryMemberShip where statusCode in ('R','O') 
        </cfquery>

        <cfquery name="local.qryGetSubActiveBilled" dbtype="query">
            select * from [local].qryMemberShip where statusCode in ('A','R','O') 
        </cfquery>

        <cfset local.memberData = application.objMember.getMemberDataByMemberNumber(mcproxy_orgID=variables.orgID, memberNumber=variables.memberNumber, customFields=['Contact Type'])>
        <cfset local.subTypeUID = variables.subTypeUIDs/>
        
        <cfset local.qryCustomSubsToExpireArray = arrayNew()/>
        <cfset local.qryCustomSubsToExpireStatusArray = arrayNew()>
        <cfif local.qryGetSubBilled.recordcount>
            <cfset local.qryMemberShip = local.qryGetSubActiveBilled/>
            <cfset local.qryCustomSubsToExpire = getSubscriberFromParentSubscrptions(memberID = variables.memberID, subscriberIDList = local.strsubsToExpire.subsToExpire ,parentStatusCodeList ="A,R,O" , subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>	
            <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.qryCustomSubsToExpire.subscriberID))>
            <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.qryCustomSubsToExpire.statusCode))>
        <cfelseif local.qryGetSubAccepted.recordcount>
            <cfset local.qryMemberShip = local.qryGetSubAccepted/>
            <cfset local.qryCustomSubsToExpire = getSubscriberFromParentSubscrptions(memberID = variables.memberID, subscriberIDList = local.strsubsToExpire.subsToExpire ,parentStatusCodeList ="A,P" , subTypeUID = local.subTypeUID, subUID = variables.membershipDuesUID)>
            <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.qryCustomSubsToExpire.subscriberID))>
            <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.qryCustomSubsToExpire.statusCode))>
        <cfelseif len(local.strsubsToExpire.SUBSTOEXPIRE)>
            <cfset local.qryCustomSubsToExpireArray = listToArray(valueList(local.strsubsToExpire.QRYSUBSTOEXPIRE.subscriberID))/>
            <cfset local.qryCustomSubsToExpireStatusArray = listToArray(valueList(local.strsubsToExpire.QRYSUBSTOEXPIRE.childStatusCode))>
        </cfif>

        <!--- -------------- --->
		<!--- 1. Delete Subs --->
		<!--- -------------- --->

        <cfset local.strRemoveMessages = structNew()>
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
			<cfif listLen(local.strsubsToExpire.subsToExpire)>
				<cfset local.objSubAdmin = CreateObject("component","model.admin.subscriptions.subscriptions")>
				<cfloop from="1" to="#arrayLen(local.qryCustomSubsToExpireArray)#" index="local.i">
					<cfif local.qryCustomSubsToExpireStatusArray[local.i] EQ 'A'>
						<cfset local.strExpireResult = local.objSubAdmin.expireMemberSubscription(actorMemberID=variables.memberID,
														actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
														subscriberID=local.qryCustomSubsToExpireArray[local.i], siteID=variables.siteID,
														AROption='C')>
					<cfelse>
						<cfset local.strExpireResult = local.objSubAdmin.removeMemberSubscription(actorMemberID=variables.memberID,
														actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
														subscriberID=local.qryCustomSubsToExpireArray[local.i], siteID=variables.siteID,
														AROption='C')>
					</cfif>
				</cfloop>
			</cfif>

			<!--- -------------- --->
			<!--- 2. Create Subs --->
			<!--- -------------- --->

			<cfif listLen(local.strNewSubs.newSubs)>
				<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
				<cfset local.arrCreatedSubs = arrayNew(1)>

				<cfset local.subscriberIDArray = listToArray(valueList(local.qryMemberShip.subscriberID))>
				<cfset local.subscriberStatusCodeArray = listToArray(valueList(local.qryMemberShip.statusCode))>

				<cfif arrayLen(local.subscriberIDArray) gt 0>
					<cfloop from="1" to="#arrayLen(local.subscriberIDArray)#" index="local.q">
						<cfif local.subscriberStatusCodeArray[local.q] EQ 'A'>
							<cfset local.subExpired = getSubscriberExpiredFromParentSubscrptions(memberID = variables.memberID, parentSubscriberID = local.subscriberIDArray[local.q])>	
						</cfif>
					</cfloop>
				   
					<cfloop query="local.strNewSubs.qrySubsToAdd">
						<cfloop from="1" to="#arrayLen(local.subscriberIDArray)#" index="local.q">
							<cfif structKeyExists(local, "subExpired") and local.subExpired.recordCount>
								<cfquery name="local.subRemove" dbtype="query">
									select * from [local].subExpired where subscriptionID = #local.strNewSubs.qrySubsToAdd.subscriptionID#;
								</cfquery>
								<cfif local.subRemove.recordCount>
									<cfset local.objSubAdmin = CreateObject("component","model.admin.subscriptions.subscriptions")>
									<cfset local.objSubAdmin.removeMemberSubscription(actorMemberID=variables.memberID,
																actorStatsSessionID=session.cfcuser.statsSessionID, memberID=variables.memberID,
																subscriberID=local.subRemove.subscriberID, siteID=variables.siteID,
																AROption='C')>
								</cfif>
							</cfif>
						</cfloop>
						<cfset arrayAppend(local.arrCreatedSubs,
											local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.subscriberIDArray[1], 
													memberID=variables.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
													rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.subscriberStatusCodeArray[1],
													pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt,
													recordedByMemberID=variables.memberID, statsSessionID=session.cfcuser.statsSessionID))>

						<cfloop from="2" to="#arrayLen(local.subscriberIDArray)#" index="local.i">
							<cfset local.objSubReg.addSubscriberToTree(orgID=variables.orgID, parentSubscriberID=local.subscriberIDArray[local.i], 
													memberID=variables.memberID, subscriptionID=local.strNewSubs.qrySubsToAdd.subscriptionID,
													rateFrequencyID=local.strNewSubs.qrySubsToAdd.rfid, status=local.subscriberStatusCodeArray[local.i],
													pcFree=local.strNewSubs.qrySubsToAdd.rateAmt is 0, rateAmt=local.strNewSubs.qrySubsToAdd.rateAmt,
													recordedByMemberID=variables.memberID, statsSessionID=session.cfcuser.statsSessionID)/>
						</cfloop>
					</cfloop>
				</cfif>
			</cfif>
		<cfelse>
			<cfset local.arrCreatedSubs = application.mcCacheManager.sessionGetValue('arrCreatedSubs',{})>
		</cfif>
        
		<!--- ------------------------- --->
		<!--- 3. Payment and accounting --->
		<!--- ------------------------- --->
		<cfif local.strNewSubs.newSubsAmount gt 0>
			<cfset local.strAccTemp = { totalPaymentAmount=local.strNewSubs.newSubsAmount, assignedToMemberID=variables.memberID, recordedByMemberID=variables.memberID, rc=arguments.event.getCollection() } >
			<cfset local.strAccTemp.payment = { detail="#variables.organization# - #variables.formNameDisplay#", amount=local.strAccTemp.totalPaymentAmount, profileID=local.profileID, profileCode=local.profileCode }>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
			<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
			
			<cfif local.strACCResponse.paymentResponse.responseCode neq 1>
				<cfset application.mcCacheManager.sessionSetValue("paymentObj", local.strACCResponse)>
				<cfset application.mcCacheManager.sessionSetValue("strNewSubs", local.strNewSubs)>
				<cfset application.mcCacheManager.sessionSetValue("arrCreatedSubs", local.arrCreatedSubs)>
			<cfelse>
				<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
					<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
				</cfif>
				<cfif application.mcCacheManager.sessionValueExists("strNewSubs")>
					<cfset application.mcCacheManager.sessionDeleteValue("strNewSubs")>
				</cfif>
				<cfif application.mcCacheManager.sessionValueExists("arrCreatedSubs")>
					<cfset application.mcCacheManager.sessionDeleteValue("arrCreatedSubs")>
				</cfif>
			</cfif>

			<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>	
                <cfloop array="#local.arrCreatedSubs#" index="local.thisSub">
					<cfif local.thisSub.invoiceID gt 0 and QueryAddRow(local.objAccounting.invoicePool)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceid",local.thisSub.invoiceID)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"invoiceProfileID",local.thisSub.invoiceProfileID)>
						<cfset QuerySetCell(local.objAccounting.invoicePool,"amount",local.thisSub.invoiceAmount)>
                        <cfif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#">
                            <cfset application.objCustomPageUtils.sub_checkActivationsByMember(orgID=variables.orgID, memberID=variables.memberID, subscriberID=local.thisSub.SUBSCRIBERID, bypassQueue=1)>
                        </cfif>
					</cfif>
				</cfloop>
				<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=variables.memberID, transactionDate=now())>
			</cfif>
		<cfelse>
			<cfif application.mcCacheManager.sessionValueExists("paymentObj")>
				<cfset application.mcCacheManager.sessionDeleteValue("paymentObj")>
			</cfif>
			<cfif application.mcCacheManager.sessionValueExists("strNewSubs")>
				<cfset application.mcCacheManager.sessionDeleteValue("strNewSubs")>
			</cfif>
			<cfif application.mcCacheManager.sessionValueExists("arrCreatedSubs")>
				<cfset application.mcCacheManager.sessionDeleteValue("arrCreatedSubs")>
			</cfif>
		</cfif>

        <!--- -------------- --->
		<!--- 4. Email Staff --->
		<!--- -------------- --->
		<cfsavecontent variable="local.pageCSS">
			<cfoutput>
			<style type="text/css">
				body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
				.msgHeader{ background:rgba(10,34,56,0.90); font-weight:bold; padding:5px;color:white!important; }
				.frmText{ font-size:12pt; } 
				.b{ font-weight:bold; }
			</style>
			</cfoutput>
		</cfsavecontent>	

		<cfsavecontent variable="local.invoice">
			<cfoutput>
            #local.pageCSS#
			<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>

			<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
                <tr class="msgHeader"><td colspan="2" class="b">Member Information</td></tr>
                <tr><td class="frmText b">MemberNumber:</td><td class="frmText">#session.cfcuser.memberdata.membernumber#&nbsp;</td></tr>
                <tr><td class="frmText b">First Name:</td><td class="frmText">#session.cfcUser.memberData.firstName#&nbsp;</td></tr>	
                <tr><td class="frmText b">Last Name:</td><td class="frmText">#session.cfcUser.memberData.lastName#&nbsp;</td></tr>	
                <tr><td class="frmText b">Email:</td><td class="frmText">#session.cfcUser.memberData.email#&nbsp;</td></tr>
                <tr><td colspan="2">&nbsp;</td></tr>

                <tr class="msgHeader"><td colspan="2" class="b">Section Changes</td></tr>
                <cfif listLen(local.strsubsToExpire.subsToExpire)>
                    <cfloop query="local.strSubsToExpire.qrySubsToExpire">
                        <tr>
                            <td class="frmText b">Removed</td>
                            <td class="frmText">
                                #local.strSubsToExpire.qrySubsToExpire.subscriptionName#
                                <cfif structKeyExists(local.strRemoveMessages,local.strSubsToExpire.qrySubsToExpire.subscriberID)>
                                    <!--MSG#local.strSubsToExpire.qrySubsToExpire.subscriberID#-->
                                </cfif>
                            </td>
                        </tr>
                    </cfloop>
                </cfif>
                <cfif listLen(local.strNewSubs.newSubs)>
                    <cfloop query="local.strNewSubs.qrySubsToAdd">
                        <tr><td class="frmText b">Added</td><td class="frmText">#local.strNewSubs.qrySubsToAdd.subscriptionName# <cfif local.strNewSubs.qrySubsToAdd.rateAmt NEQ 0> - #local.strNewSubs.qrySubsToAdd.rateName# (#dollarFormat(local.strNewSubs.qrySubsToAdd.rateAmt)#)</cfif></td></tr>
                    </cfloop>
                </cfif>
                <tr><td colspan="2">&nbsp;</td></tr>

                <tr class="msgHeader"><td colspan="2" class="b">Payment Information</td></tr>
                <tr><td class="frmText b"><b>Amount Due for New Sections:</b> &nbsp;</td><td class="frmText"><b>#dollarFormat(local.strNewSubs.newSubsAmount)#</b>&nbsp;</td></tr>
                <tr><td colspan="2">&nbsp;</td></tr>

                <cfif local.strNewSubs.newSubsAmount gt 0>
                    <tr>
                        <td class="frmText b">Pay Method:</td><td class="frmText">
                            <cfif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCredit#">
                                Credit Card
                                <cfset arguments.event.setValue('p_#local.profileID#_mppid',int(val(arguments.event.getValue('p_#local.profileID#_mppid',0)))) />
                                
                                <cfif arguments.event.getValue('p_#local.profileID#_mppid') gt 0>
                                    <cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(
                                            mppid     = arguments.event.getValue('p_#local.profileID#_mppid'),
                                            memberID  = variables.memberID,
                                            profileID = local.profileID)>
                                    - #local.qrySavedInfoOnFile.detail#
                                </cfif>	
                                
                            <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodCheck#">
                                Check
                            <cfelseif arguments.event.getValue('mccf_payMeth','') eq "#variables.strPageFields.PayMethodACH#">
                                ACH                               
                            </cfif>                            					
                        </td>
                    </tr>
                </cfif>
			</table>
			</cfoutput>
		</cfsavecontent>
	
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>
			<!--- email submitter --->
			<cfset local.emailSentToUser = FALSE>

			<cfif len(session.cfcUser.memberData.email)>

				<cfsavecontent variable="local.mailContent">
					<cfoutput>						
						<p>#variables.strPageFields.ConfirmationMessage#</p><hr/>
						#local.invoice#	
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=variables.strPageFields.ConfirmationFrom},
					emailto=[{ name="", email=session.cfcUser.memberData.email }],
					emailreplyto=variables.strPageFields.StaffConfirmationTo,
					emailsubject=variables.strPageFields.ConfirmationSub,
					emailtitle="#arguments.event.getTrimValue('mc_siteinfo.sitename')# - #variables.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=variables.siteID,
					memberID=val(variables.memberID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
					)>

				<cfset local.emailSentToUser = local.responseStruct.success>
			<cfelse>
				<cfthrow>
			</cfif>
		 <cfelse>
			<cfset local.emailSentToUser = FALSE>
			<cfset local.response = 'paymentFailed'>
		</cfif>
        
		<cfset local.invoiceForStaff = local.invoice>
		<cfloop collection="#local.strRemoveMessages#" item="local.thisMsg">
			<cfset local.invoiceForStaff = replaceNoCase(local.invoiceForStaff, "<!--MSG#local.thisMsg#-->", "<span style='color:red'><b>#local.strRemoveMessages[local.thisMsg]#</b></span>")>
		</cfloop>

        <!--- email staff --->
        <cfsavecontent variable="local.mailContent">
            <cfoutput>
                <cfif NOT local.emailSentToUser>
                    <p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
                </cfif>
                #local.invoiceForStaff#
            </cfoutput>
        </cfsavecontent>

        <cfscript>
            local.arrEmailTo = [];
            variables.strPageFields.StaffConfirmationTo = replace(variables.strPageFields.StaffConfirmationTo,",",";","all");
            local.toEmailArr = listToArray(variables.strPageFields.StaffConfirmationTo,';');
            for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
                local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
            }
        </cfscript>

        <cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
            emailfrom={ name="", email=variables.strPageFields.ConfirmationFrom},
            emailto=local.arrEmailTo,
            emailreplyto=variables.strPageFields.ConfirmationFrom,
            emailsubject=variables.strPageFields.StaffConfirmationSub,
            emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
            emailhtmlcontent=local.mailContent,
            siteID=variables.siteID,
            memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
            sendingSiteResourceID=this.siteResourceID
        )>

		<!--- relocate to message page --->
		<cfset session.invoice = local.invoice>
		
		<cfif not application.mcCacheManager.sessionValueExists("paymentObj")>      
			<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
		<cfelse>
			<cflocation url="#variables.baselink#&fa=paymentErr" addtoken="false">
		</cfif>
	</cffunction>

    <cffunction name="isValidMember" access="private" output="false" returntype="string">
        <cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.isValid = false>

		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=arguments.memberID, orgID=variables.orgID)>

		<cfif local.qryMember.recordcount>

			<cfset local.qryActiveSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='A', distinct=true)>

			<cfif listFindNoCase(valueList(local.qryActiveSubs.uid), variables.membershipDuesUID)>
				<cfset local.isValid = true>
			<cfelse>
				<cfset local.qryAcceptedSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='P', distinct=true)>
				<cfif listFindNoCase(valueList(local.qryAcceptedSubs.uid), variables.membershipDuesUID)>
					<cfset local.isValid = true>
				<cfelse>
					<cfset local.qryBilledSubs = application.objCustomPageUtils.sub_getSubscriptions(mcproxy_siteID=variables.siteID, memberID=local.qryMember.memberID, status='O', distinct=true)>

					<cfif listFindNoCase(valueList(local.qryBilledSubs.uid), variables.membershipDuesUID)>
						<cfset local.isValid = true>
					</cfif>
				</cfif>	
			</cfif>
		</cfif>
		<cfreturn local.isValid>
	</cffunction>

    <cffunction name="getSubscriberExpiredFromParentSubscrptions" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="parentSubscriberID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberExpired">
            select distinct ssbq.subscriberID, ssbq.subscriptionID from dbo.sub_subscribers ssbq
            inner join dbo.sub_subscriptions sspq on sspq.subscriptionID = ssbq.subscriptionID
            inner join dbo.sub_statuses sttu on sttu.statusID = ssbq.statusID and sttu.statusCode='E'
            where 
            ssbq.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
            and
            ssbq.parentSubscriberID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentSubscriberID#" list="true">)
		</cfquery>
        
		<cfreturn local.qrySubscriberExpired>
	</cffunction>

    <cffunction name="getSubscriberFromParentSubscrptions" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="subscriberIDList" type="string" required="false" default="">
        <cfargument name="parentStatusCodeList" type="string" required="true">
        <cfargument name="subTypeUID" type="string" required="true">
        <cfargument name="subUID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrentSections">
			select sb.subscriberID, sttu.statusCode,stp.uid as addonuid 
            from dbo.sub_subscriptionSets st 
            inner join dbo.sub_sets s2 on st.setId = s2.setID 
            inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID 
            inner join (select distinct sspq.uid from dbo.sub_subscribers ssbq 
            inner join dbo.sub_subscriptions sspq on sspq.subscriptionID = ssbq.subscriptionID 
            where ssbq.subscriberID in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subscriberIDList#" list="true">)) scr on scr.uid =s.uid
            inner join dbo.sub_Types stp on stp.typeID = s.typeID
            inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID
            inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
            inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID           
            inner join dbo.sub_subscribers sb on sb.subscriptionID = s.subscriptionID and sb.rfid = rf.rfid
            inner join dbo.ams_members m on m.memberID = sb.memberID and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
            inner join dbo.sub_statuses stt on stt.statusID = sb.statusID 
            inner join  dbo.sub_subscribers ssb on ssb.subscriberID  = sb.parentSubscriberID 
            inner join dbo.sub_subscriptions ssp on ssp.subscriptionID = ssb.subscriptionID 
            inner join dbo.sub_statuses sttu on sttu.statusID = ssb.statusID and sttu.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentStatusCodeList#" list="true">)        
            WHERE 
                s2.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.siteID#"> 
                and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
                and ssp.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#">
		</cfquery>
        
		<cfreturn local.qryCurrentSections>
	</cffunction>

    <cffunction name="getCurrentSubscriptions" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
        <cfargument name="subsToExpire" type="string" required="false" default="">
        <cfargument name="subTypeUID" type="string" required="true">
        <cfargument name="subUID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrentSections">
			select sttu.statusCode as parentStatusCode,stt.statusCode as childStatusCode, sb.parentSubscriberID,sb.subscriberID, r.rateID, r.rateName, s.uid, stp.uid as addonuid,
            rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID
            from dbo.sub_subscriptionSets st
            inner join dbo.sub_sets s2 on st.setId = s2.setID
            inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID
            inner join dbo.sub_subscribers sb on sb.subscriptionID = s.subscriptionID 
            inner join dbo.sub_rateFrequencies rf on rf.rfid = sb.RFID 
            inner join dbo.sub_rates r on r.rateID = rf.rateID
            inner join dbo.sub_Types stp on stp.typeID = s.typeID
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
            inner join dbo.ams_members m on m.memberID = sb.memberID and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
            inner join dbo.sub_statuses stt on stt.statusID = sb.statusID and stt.statusCode in ('A','P','R','O')
            inner join  dbo.sub_subscribers ssb on ssb.subscriberID  = sb.parentSubscriberID 
            inner join dbo.sub_subscriptions ssp on ssp.subscriptionID = ssb.subscriptionID 
            inner join dbo.sub_statuses sttu on sttu.statusID = ssb.statusID and sttu.statusCode in ('A','P','R','O')        
            WHERE 
                s2.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.siteID#"> 
                and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
                and ssp.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#">
            <cfif listlen(arguments.subsToExpire)>
				and sb.subscriberID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subsToExpire#" list="true">)
			</cfif>
            order by subscriptionName, subscriptionID, rateName
		</cfquery>

        <cfquery name="local.qryGetSubActive" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode = 'A' 
        </cfquery>

        <cfquery name="local.qryGetSubAccepted" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode = 'P' 
        </cfquery>

        <cfquery name="local.qryGetSubBilled" dbtype="query">
            select * from [local].qryCurrentSections where parentStatusCode in ('R','O') 
        </cfquery>

        <cfif local.qryGetSubBilled.recordcount>
            <cfset local.qryCurrentSections = local.qryGetSubActive/>
        <cfelseif local.qryGetSubAccepted.recordcount>
            <cfset local.qryCurrentSections = local.qryGetSubAccepted/>
        </cfif>

		<cfreturn local.qryCurrentSections>
	</cffunction>

    <cffunction name="getAvailableSections" access="private" output="false" returntype="query">
        <cfargument name="memberID" type="numeric" required="true">
        <cfargument name="existingSubsList" type="string" required="false" default="">
		<cfargument name="subTypeUID" type="string" required="true">
        <cfargument name="newSubs" type="string" required="false" default="">

		<cfset var local = structNew()>
        <cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
        
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAvailableSections" result="local.qryAvailableSectionsResult">
            SET NOCOUNT ON;

            declare @FID int, @memberid int;
			set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;
			set @memberid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;

            select distinct r.rateID, r.rateName, s.uid, rf.rateAmt, s.subscriptionName, s.subscriptionID, rf.rfid, stp.uid as addonuid,
                cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) as subIDrateID
            from dbo.sub_subscriptionSets st
            inner join dbo.sub_sets s2 on st.setId = s2.setID
            inner join dbo.sub_subscriptions s on s.subscriptionID = st.subscriptionID and s.status = 'A'
            inner join dbo.sub_Types stp on stp.typeID = s.typeID and stp.status = 'A'
            inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID and sch.status = 'A'
            inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID and r.status = 'A' and r.isRenewalRate = 0 and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
            inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID and rf.status = 'A' and rf.allowfrontend = 1 and r.isRenewalRate = 0
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID and f.frequencyShortName = 'F' and f.status = 'A'
            inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = r.siteResourceID and srfrp.functionID = @FID
				and srfrp.siteID = <cfqueryparam value="#variables.siteID#" cfsqltype="cf_sql_integer">
            inner join dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
				and gprp.siteID = <cfqueryparam value="#variables.siteID#" cfsqltype="cf_sql_integer">
			inner join ams_members m on m.groupPrintID = gprp.groupPrintID and m.memberID = @memberID	
            WHERE 
                s2.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.siteID#"> 
                and stp.uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subTypeUID#" list="true">)
            <cfif listlen(arguments.existingSubsList)>
				and s.subscriptionID not in (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.existingSubsList#" list="true">)
			</cfif>
            <cfif listlen(arguments.newSubs)>
				and cast(s.subscriptionID as varchar(10)) + '_' + cast(r.rateID as varchar(10)) in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newSubs#" list="true">)
			</cfif>
            order by subscriptionName, subscriptionID, rateName;
		</cfquery>

		<cfreturn local.qryAvailableSections>
	</cffunction>
    
    <cffunction name="validateSubsToExpire" access="private" output="false" returntype="struct">
		<cfargument name="subsToExpire" type="string" required="true">
        <cfargument name="subTypeUID" type="string" required="true">
        <cfargument name="subUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.subsToExpire = arguments.subsToExpire>
		<cfif listlen(local.strReturn.subsToExpire)>
			<cfset local.strReturn.qrySubsToExpire = getCurrentSubscriptions(memberID = variables.memberID, subsToExpire=local.strReturn.subsToExpire, subTypeUID = arguments.subTypeUID, subUID = arguments.subUID)>
			<cfset local.strReturn.subsToExpire = valueList(local.strReturn.qrySubsToExpire.subscriberID)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

    <cffunction name="validateNewSubs" access="private" output="false" returntype="struct">
		<cfargument name="newSubs" type="string" required="true">
		<cfargument name="subsToExpire" type="string" required="true">
        <cfargument name="subTypeUID" type="string" required="true">
        <cfargument name="subUID" type="string" required="true">        

		<cfset var local = structNew()>
       
		<cfset local.strReturn = structNew()>

		<cfset local.strReturn.newSubsAmount = 0>
		<cfset local.strReturn.newSubs = arguments.newSubs>
		<cfif listlen(local.strReturn.newSubs)>
			<cfset local.qryCurrentSubscriptions = getCurrentSubscriptions(memberID = variables.memberID,subTypeUID = arguments.subTypeUID, subUID = arguments.subUID)>
			<cfset local.strReturn.qrySubsToAdd = getAvailableSections(memberID = variables.memberID, existingSubsList = valueList(local.qryCurrentSubscriptions.subscriptionID), subTypeUID = arguments.subTypeUID, memberID = variables.memberID, newSubs=local.strReturn.newSubs)>
            <cfset local.strReturn.newSubs = valueList(local.strReturn.qrySubsToAdd.subIdRateId)>		

			<cfquery name="local.qrySubsToAddTotal" dbtype="query">
				select sum(rateAmt) as total
				from [local].strReturn.qrySubsToAdd
			</cfquery>
			<cfset local.strReturn.newSubsAmount = val(local.qrySubsToAddTotal.total)>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

    <cffunction name="getMembershipByStatus" access="private" output="false" returntype="query">
		<cfargument name="statusCodeList" type="string" required="true">
        <cfargument name="subUID" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
			select sb.subscriberID, st.statusCode
			from dbo.sub_subscribers as sb
			inner join dbo.sub_statuses as st on st.statusID = sb.statusID 
				AND st.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusCodeList#" list="true">)
			inner join dbo.sub_subscriptions as s on s.subscriptionID = sb.subscriptionID
			inner join dbo.sub_types as t on t.typeID = s.typeID AND t.siteID = #variables.siteID# 
            inner join dbo.sub_rateSchedules as sch on sch.scheduleID = s.scheduleID 
            inner join dbo.sub_rates r on r.scheduleID = sch.scheduleID
            inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID and rf.rfid = sb.rfid 
            inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID  
			inner join dbo.ams_members as m on m.memberID = sb.memberID
				and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.memberID#">
			where sb.parentSubscriberID is null
                and s.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.subUID#">
			order by st.statusCode desc
		</cfquery>

		<cfreturn local.qrySubscriber>
	</cffunction>

    <cffunction name="showError" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
			<div class="tsAppSectionHeading">There was an issue continuing with your application.</div>
			<div class="tsAppSectionContentContainer">						
				<cfif arguments.errorCode eq "notfound">					
					#variables.strPageFields.NonMemberMessage#
                <cfelseif arguments.errorCode eq "paymentFailed">
                    This submission is missing information. Ensure you have entered all required fields and it is valid.
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnHTML>
	</cffunction>

</cfcomponent>