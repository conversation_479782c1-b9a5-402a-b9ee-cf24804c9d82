<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/email</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Each email type included in the response will have the same data structure as the GET /v1/email/{api_id} response.
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/email HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>	
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 2,
        "email": [
            {
                "type": "Personal Email",
                "description": "Personal Email",
                "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "x-api-uri": "/v1/email/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
            } 
            ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>