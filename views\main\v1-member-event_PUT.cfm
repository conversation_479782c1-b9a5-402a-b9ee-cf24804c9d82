<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/event/{api_id}/{registrant_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with one or more of the following keys:
		<ul>
			<li><i>isflagged</i> - optional; if provided, must be 1 or 0. 1 will flag the registration</li>
			<li><i>attended</i> - optional; if provided, must be 1 or 0. 1 will mark the registration as attended</li>
			<li>
				<i>credit</i> - optional; if provided, must contain complete array of credits to be awarded to registrant. All 4 subkeys are required for each credit. Any existing credits will be removed prior to assigning credit.<br/>
				<i>authoritycode</i> - the credit authority code<br/>
				<i>credittypecode</i> - the credit type code<br/>
				<i>creditawarded</i> - the numeric amount of credit awarded<br/>
				<i>idnumber</i> - may be blank; any authority specific ID number for this registrant<br/>
			</li>
		</ul>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/123456 HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{ 
    "attended": 1,
    "isflagged": 0,
    "credit": [
        {
            "authoritycode": "xxxxx",
            "credittypecode": "xxxxx",
            "creditawarded": 1.5,
            "idnumber": "xxxxx"
        }
    ]
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 UPDATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 EVENT NOT FOUND</td><td>invalid event api_id</td></tr>
			<tr><td class="rc">404 EVENT REGISTRATION NOT FOUND</td><td>member is not registered for this event/td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>error updating registration</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 UPDATED

{
  "data": {
      "count": 1,
      "event": {
          "title": "Fall Regional Conference",
          "subtitle": "Network and Collaborate",
          "startdate": "2019-03-01 17:00:00",
          "enddate": "2019-03-01 19:00:00",
          "eventcode": "ABCDEFGH",
          "alldayevent": 0,
          "event_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
          "registrant": [
              {
                  "x-api-uri": "/v1/member/SAMPLE123456/event/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/YYYYYYY",
                  "attended": 1,
                  "notes": "",
                  "isflagged": 0,
                  "dateregistered": "2019-01-15 08:48:32",
                  "rate_api_id": "ZZZZZZZZ-ZZZZ-ZZZZ-ZZZZ-ZZZZZZZZZZZZ",
                  "rate": "Sample Rate",
                  "ratereportcode": "LMNOPQ"
                  "amountbilled": "149.00",
                  "amountdue": "10.00",
                  "roles": [
                      {
                          "role": "Speaker",
                          "fields": {
                              "Speaker Type": "Panelist",
                              "Date Approved": "01/20/2019",
                              "Audio/Visual": "Handheld Mic|Tabletop Mic"
                              ...
                          }
                      }
                      ...
                  ],
                  "fields": {
                      "Want to be contacted": "Yes"
                      ...
                  },
                  "credit": [
                      {
                          "authorityname": "State Bar Of California",
                          "authoritycode": "CAStateBar",
                          "credittype": "General",
                          "credittypecode": "General",
                          "creditawarded": 3,
                          "idnumber": "CA123456"
                      }
                      ...
                  ]
              }
          ]
      }
    },
  "error":false,
  "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update registration."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>