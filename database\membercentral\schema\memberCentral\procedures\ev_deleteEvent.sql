ALTER PROC dbo.ev_deleteEvent
@siteID int,
@eventID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @eventSiteResourceID int, @calApplicationInstanceID int, @applicationTypeID int, @evStatus varchar(1),
		@orgID int, @msgjson varchar(max), @eventTitle varchar(200), @reportCode varchar(15), @calendarID int;
	SELECT @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');
	SELECT @evStatus = dbo.fn_setEventStatus(@eventID);

	SELECT @orgID = orgID
	FROM dbo.sites
	WHERE siteID = @siteID;

	SELECT TOP 1 @eventTitle = eventcontent.contentTitle, @reportCode = e.reportCode
	FROM dbo.ev_events as e 
	INNER JOIN dbo.cms_contentLanguages AS eventContent ON eventContent.contentID = e.eventContentID 
		AND eventContent.languageID = 1
	WHERE e.eventID = @eventID;

	SELECT @eventSiteResourceID = e.siteResourceID, @calendarID = c.calendarID, @calApplicationInstanceID = c.applicationInstanceID
	FROM dbo.ev_calendarEvents ce
	INNER JOIN dbo.ev_calendars c on c.siteID = @siteID and c.calendarID = ce.sourceCalendarID 
	INNER JOIN dbo.ev_events e on e.siteID = @siteID and e.eventID = ce.sourceEventID
	AND e.eventID = @eventID;

	BEGIN TRAN;
		UPDATE dbo.ev_events
		SET [status] = @evStatus
		WHERE siteID = @siteID 
		AND eventID = @eventID;

		DELETE FROM dbo.ev_subEvents
		WHERE eventID = @eventID;
	
		DELETE FROM dbo.ev_subEvents
		WHERE parentEventID = @eventID;

		-- create activity log entry
		EXEC platformstatsMC.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='remove',
			@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
			@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null;

		-- audit log
		DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '][Event Code: ' + @reportCode + '] has been deleted.', 'json');
		EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@enteredByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
