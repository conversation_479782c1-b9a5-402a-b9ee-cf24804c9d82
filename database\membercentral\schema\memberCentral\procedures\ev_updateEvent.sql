ALTER PROCEDURE dbo.ev_updateEvent
@eventID int,
@eventTypeID int,
@enteredByMemberID int,
@eventSubTitle varchar(200),
@lockTimeZoneID int,
@isAllDayEvent bit,
@status char(1),
@reportCode varchar(15),
@internalNotes varchar(max),
@hiddenFromCalendar bit,
@emailContactContent bit,
@emailLocationContent bit,
@emailCancelContent	bit,
@emailTravelContent	bit,
@remarketingURL varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @eventSiteResourceID int, @calApplicationInstanceID int, @applicationTypeID int, @oldReportCode varchar(15),
		@orgID int, @msgjson varchar(max), @eventTitle varchar(500), @defaultLanguageID int, @calendarID int;
	select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');

	select @siteID = siteID, @oldReportCode = reportCode
	from dbo.ev_events
	where eventID = @eventID;

	-- Get orgID and defaultLanguageID for audit logging
	SELECT @orgID = orgID, @defaultLanguageID = defaultLanguageID
	FROM dbo.sites
	WHERE siteID = @siteID;

	-- Get event title for audit logging
	SELECT @eventTitle = ISNULL(NULLIF(eventcontent.contentTitle, ''), 'Event ID ' + CAST(@eventID AS varchar(10)))
	FROM dbo.ev_events AS e
	CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) as eventcontent
	WHERE e.eventID = @eventID;

	IF NULLIF(@reportCode,'') IS NULL 
	OR ((@oldReportCode <> @reportCode) AND EXISTS(SELECT 1 FROM dbo.ev_events where siteID = @siteID and reportCode = @reportCode and [status] in ('A','I')))
		RAISERROR('Event Code must be unique.', 16, 1);

	-- Create audit log table for change detection
	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;
	CREATE TABLE #tmpAuditLogData ( [rowCode] varchar(20) PRIMARY KEY, [Event Sub Title] varchar(max), [Lock Time Zone ID] varchar(max), [All Day Event] varchar(max),
		[Status] varchar(max), [Report Code] varchar(max), [Internal Notes] varchar(max), [Hidden From Calendar] varchar(max),
		[Email Contact Content] varchar(max), [Email Location Content] varchar(max), [Email Cancel Content] varchar(max), [Email Travel Content] varchar(max),
		[Remarketing URL] varchar(max)
	);

	-- Insert data type codes for change detection
	INSERT INTO #tmpAuditLogData ([rowCode], [Event Sub Title], [Lock Time Zone ID], [All Day Event],
		[Status], [Report Code], [Internal Notes], [Hidden From Calendar], [Email Contact Content], [Email Location Content],
		[Email Cancel Content], [Email Travel Content], [Remarketing URL])
	VALUES ('DATATYPECODE', 'STRING', 'INTEGER', 'BIT', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT', 'BIT', 'BIT', 'BIT', 'STRING');

	-- Insert old values
	INSERT INTO #tmpAuditLogData ([rowCode], [Event Sub Title], [Lock Time Zone ID], [All Day Event],
		[Status], [Report Code], [Internal Notes], [Hidden From Calendar], [Email Contact Content], [Email Location Content],
		[Email Cancel Content], [Email Travel Content], [Remarketing URL])
	SELECT 'OLDVAL', ISNULL(eventSubTitle, ''), ISNULL(CAST(lockTimeZoneID AS varchar(50)), ''), CASE WHEN isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
		CASE WHEN [status] = 'A' THEN 'Active' ELSE 'Inactive' END, reportCode, ISNULL(internalNotes, ''), nullif(hiddenFromCalendar,''),
		nullif(emailContactContent,''), nullif(emailLocationContent,''), nullif(emailCancelContent,''), nullif(emailTravelContent,''), ISNULL(remarketingURL, '')
	FROM dbo.ev_events
	WHERE eventID = @eventID;
	
	update dbo.ev_events
	set eventTypeID = @eventTypeID,
		eventSubTitle = nullif(@eventSubTitle,''),
		lockTimeZoneID = @lockTimeZoneID,
		isAllDayEvent = @isAllDayEvent,
		[status] = @status,
		reportCode = @reportCode,
		internalNotes = @internalNotes,
		hiddenFromCalendar = nullif(@hiddenFromCalendar,''),
		emailContactContent = nullif(@emailContactContent,''),
		emailLocationContent = nullif(@emailLocationContent,''),
		emailCancelContent	= nullif(@emailCancelContent,''),
		emailTravelContent	= nullif(@emailTravelContent,''),
		remarketingURL = @remarketingURL
	where eventid = @eventID;

	-- Insert new values
	INSERT INTO #tmpAuditLogData ([rowCode], [Event Sub Title], [Lock Time Zone ID], [All Day Event],
		[Status], [Report Code], [Internal Notes], [Hidden From Calendar], [Email Contact Content], [Email Location Content],
		[Email Cancel Content], [Email Travel Content], [Remarketing URL])
	SELECT 'NEWVAL', ISNULL(eventSubTitle, ''), ISNULL(CAST(lockTimeZoneID AS varchar(50)), ''), CASE WHEN isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
		CASE WHEN [status] = 'A' THEN 'Active' ELSE 'Inactive' END, reportCode, ISNULL(internalNotes, ''), nullif(hiddenFromCalendar,''),
		nullif(emailContactContent,''), nullif(emailLocationContent,''), nullif(emailCancelContent,''), nullif(emailTravelContent,''), ISNULL(remarketingURL, '')
	FROM dbo.ev_events
	WHERE eventID = @eventID;

	select @eventSiteResourceID = e.siteResourceID, @calApplicationInstanceID = c.applicationInstanceID, @calendarID = c.calendarID
	from dbo.ev_calendarEvents ce
	inner join dbo.ev_calendars c on c.siteID = @siteID 
		and c.calendarID = ce.sourceCalendarID 
	inner join dbo.ev_events e on e.siteID = @siteID 
		and e.eventID = ce.sourceEventID
		AND e.eventID = @eventID;

	-- create activity log entry
	EXEC platformstatsMC.dbo.act_recordLog @memberID=@enteredByMemberID, @activityType='update', 
		@applicationTypeID=@applicationTypeID, @applicationInstanceID=@calApplicationInstanceID,
		@supportSiteResourceID=@eventSiteResourceID, @supportMemberID=null, @supportMessage=null;

	-- Generate audit log message for changes
	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msgjson OUTPUT;

	IF ISNULL(@msgjson, '') <> '' BEGIN
		DECLARE @crlf varchar(2) = CHAR(13) + CHAR(10);
		DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '] has been updated.', 'json') + @crlf
			+ 'The following changes have been made:' + @crlf + @msgjson;

		EXEC dbo.ev_insertAuditLog @orgID = @orgID, @siteID = @siteID, @areaCode = 'EVENT', @msgjson = @msgjson, @evKeyMapJSON = @evKeyMapJSON, @isImport = 0, @enteredByMemberID = @enteredByMemberID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
