USE membercentral
GO

ALTER PROC dbo.ev_moveCalendarEvent
@eventID int,
@fromCalendarID int,
@toCalendarID int,
@toCategoryIDList varchar(max),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#evMoveEvent') IS NOT NULL
		DROP TABLE #evMoveEvent;

	DECLARE @siteID int, @orgID int, @siteCode varchar(10), @inheritedRightsResourceID int, @minRowID int, @resourceID int, @include bit, 
		@functionID int, @roleID int, @groupID int, @inheritedRightsFunctionID int, @hasRegistrants bit, @SWParticipantID int, 
		@SWCalendarID int, @SWCategoryCount int, @SWFeaturedProgramID int, @previousCalendarIDList varchar(max),
		@eventTitle varchar(200), @sourceCalendarName varchar(250), @destCalendarName varchar(250), @msgjson varchar(max), @defaultLanguageID int;
	DECLARE @tmpEVCategories TABLE (categoryID int, categoryOrder int);
	DECLARE @tmpSWEventCategories TABLE (categoryID int);

	select @siteID = s.siteID, @orgID = s.orgID, @siteCode = s.siteCode, @defaultLanguageID = s.defaultLanguageID
	from dbo.ev_events as e
	inner join dbo.sites as s on s.siteID = e.siteID
	where e.eventID = @eventID;

	-- Get event title for audit logging
	SELECT @eventTitle = ISNULL(NULLIF(eventcontent.contentTitle, ''), 'Event ID ' + CAST(@eventID AS varchar(10)))
	FROM dbo.ev_events AS e
	CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) as eventcontent
	WHERE e.eventID = @eventID;

	-- Get source and destination calendar names for audit logging
	SELECT
		@sourceCalendarName = MAX(CASE WHEN c.calendarID = @fromCalendarID THEN
			ai.applicationInstanceName
			+ CASE WHEN communityInstances.applicationInstanceName IS NOT NULL
				   THEN ' (' + communityInstances.applicationInstanceName + ')'
				   ELSE '' END
		END),
		@destCalendarName = MAX(CASE WHEN c.calendarID = @toCalendarID THEN
			ai.applicationInstanceName
			+ CASE WHEN communityInstances.applicationInstanceName IS NOT NULL
				   THEN ' (' + communityInstances.applicationInstanceName + ')'
				   ELSE '' END
		END)
	FROM dbo.ev_calendars AS c
	INNER JOIN dbo.cms_applicationInstances AS ai
		ON ai.siteID = @siteID
	   AND c.applicationInstanceID = ai.applicationInstanceID
	INNER JOIN dbo.cms_siteResources AS sr
		ON sr.siteID = @siteID
	   AND ai.siteResourceID = sr.siteResourceID
	   AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.cms_siteResources AS parentResource
		ON parentResource.siteID = @siteID
	   AND parentResource.siteResourceID = sr.parentSiteResourceID
	   AND parentResource.siteResourceStatusID = 1
	LEFT JOIN dbo.cms_siteResources AS grandparentResource
		ON grandparentResource.siteID = @siteID
	   AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
	   AND grandparentResource.siteResourceStatusID = 1
	LEFT JOIN dbo.cms_applicationInstances AS communityInstances
		ON communityInstances.siteID = @siteID
	   AND communityInstances.applicationInstanceID = grandparentResource.applicationInstanceID
	WHERE c.calendarID IN (@fromCalendarID, @toCalendarID);

	-- current calendar & syndicated calendars on which this event is present right now
	SELECT @previousCalendarIDList =  COALESCE(@previousCalendarIDList + ',','') +  CAST(calendarID AS VARCHAR(10))
	FROM dbo.cache_calendarEvents
	WHERE eventID = @eventID
	GROUP BY calendarID;

	SELECT @SWParticipantID = seminarWeb.dbo.fn_getParticipantIDFromOrgcode(@siteCode);

	IF EXISTS(select 1 from dbo.ev_registrants as er 
				inner join dbo.ev_registration as r on r.registrationid = er.registrationid
				where r.eventID = @eventID
				and r.siteID = @siteID
				and r.[status] = 'A'
				and er.[status] = 'A')
		set @hasRegistrants = 1;
	ELSE 
		set @hasRegistrants = 0;


	INSERT INTO @tmpEVCategories (categoryID, categoryOrder)
	SELECT c.categoryID, MIN(li.autoid)
	FROM dbo.fn_intListToTable(@toCategoryIDList,',') as li
	INNER JOIN dbo.ev_categories as c on c.categoryID = li.listItem
	WHERE c.calendarID = @toCalendarID
	GROUP BY c.categoryID;

	IF @SWParticipantID IS NOT NULL
		SELECT @SWFeaturedProgramID = featuredID
		FROM seminarWeb.dbo.tblFeaturedPrograms
		WHERE participantID = @SWParticipantID
		AND eventID = @eventID;

	BEGIN TRAN;
		UPDATE dbo.ev_calendarEvents
		set calendarID = @toCalendarID
		WHERE sourceEventID = @eventID
		AND calendarID = @fromCalendarID;

		UPDATE dbo.ev_calendarEvents
		set sourceCalendarID = @toCalendarID
		WHERE sourceEventID = @eventID
		AND sourceCalendarID = @fromCalendarID;

		DELETE FROM dbo.ev_eventCategories
		WHERE eventID = @eventID;

		INSERT INTO dbo.ev_eventCategories (eventID, categoryID, categoryOrder)
		select @eventID, categoryID, categoryOrder
		from @tmpEVCategories;

		IF @SWFeaturedProgramID IS NOT NULL BEGIN
			SELECT @SWCalendarID = calendarID, @SWCategoryCount = COUNT(categoryID)
			FROM seminarWeb.dbo.tblParticipantEvents
			WHERE participantID = @SWParticipantID
			GROUP BY calendarID;

			IF @SWCategoryCount > 0
				INSERT INTO @tmpSWEventCategories (categoryID)
				select ec.categoryID
				from dbo.ev_categories as ec
				inner join seminarWeb.dbo.tblParticipantEvents as pe on pe.calendarID = @SWCalendarID 
					and pe.calendarID = ec.calendarID
					and pe.categoryID = ec.categoryID
				where pe.participantID = @SWParticipantID;
			ELSE
				INSERT INTO @tmpSWEventCategories (categoryID)
				select categoryID
				from dbo.ev_categories
				where calendarID = @SWCalendarID;

			IF NOT EXISTS (select 1 from @tmpEVCategories as evc inner join @tmpSWEventCategories as swevc on swevc.categoryID = evc.categoryID)
				EXEC seminarWeb.dbo.sw_toggleFeaturedProgram @orgCode=@siteCode, @programType='EV', @programID=@eventID, @isFeatured=0, @recordedByMemberID=@recordedByMemberID;
		END
			
		-- new siteresourceid
		select @inheritedRightsResourceID = ai.siteResourceID
		from dbo.ev_calendars c
		inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = c.applicationInstanceID 
		where c.siteid = @siteID 
		and c.calendarID = @toCalendarID;
				
		-- get all of the rights needing updating
		select srr.*
		into #evMoveEvent
		from dbo.cms_siteResourceRights srr
		inner join dbo.cms_siteResources sr on sr.siteResourceID =srr.resourceID and sr.siteID = @siteID
		inner join dbo.ev_events e on e.siteResourceID = sr.siteResourceID and e.eventID = @eventID
		where inheritedRightsResourceID is not null and srr.siteID = @siteID;
				
		select @minRowID = min(resourceRightsID) from #evMoveEvent;
		while @minRowID is not null BEGIN
			select @resourceID=resourceID, @include=[include], @functionID=functionID, @roleID=roleID, @groupID=groupID, 
				@inheritedRightsFunctionID=inheritedRightsFunctionID
			from #evMoveEvent
			where resourceRightsID = @minRowID;
				
			exec dbo.cms_deleteSiteResourceRight @siteID=@siteID, @siteResourceID=@resourceID, @siteResourceRightID=@minRowID;
				
			exec dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@resourceID, @include=@include, @functionIDList=@functionID, 
				@roleID=@roleID, @groupID=@groupID, @inheritedRightsResourceID=@inheritedRightsResourceID, @inheritedRightsFunctionID=@inheritedRightsFunctionID;
				
			select @minRowID = min(resourceRightsID) from #evMoveEvent where resourceRightsID > @minRowID;
		END

		-- audit log for calendar move
		DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@toCalendarID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '] moved from calendar [' + @sourceCalendarName + '] to calendar [' + @destCalendarName + '].', 'json');
		EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	EXEC dbo.ev_refreshCalendarEventsCache @siteID=@siteID;
	EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID, @eventID=@eventID, @calendarIDList=@previousCalendarIDList;

	IF @hasRegistrants = 1 BEGIN
		-- process conditions
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		select @orgID, null, conditionID
		from dbo.ams_virtualGroupConditions 
		where orgID = @orgID
		and fieldCode = 'ev_entry';

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL
			DROP TABLE #tblMCQRun;
	END

	IF OBJECT_ID('tempdb..#evMoveEvent') IS NOT NULL
		DROP TABLE #evMoveEvent;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ev_saveEventCategories
@eventID int,
@recordedByMemberID int,
@categoryIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteCode varchar(10), @SWFeaturedProgramID int, @SWParticipantID int, @SWCalendarID int, @SWCategoryCount int,
		@siteID int, @orgID int, @calendarID int, @msgjson varchar(max), @defaultLanguageID int, @eventTitle varchar(200),
		@oldCategoryList varchar(max), @newCategoryList varchar(max);
	DECLARE @tmpEVCategories TABLE (categoryID int, categoryOrder int);
	DECLARE @tmpSWEventCategories TABLE (categoryID int);

	SELECT @siteCode = s.siteCode, @siteID = s.siteID, @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
	FROM dbo.ev_events as ev
	INNER JOIN dbo.sites as s on s.siteID = ev.siteID
	WHERE ev.eventID = @eventID;

	-- Get calendar ID and event title for audit logging
	SELECT @calendarID = ce.sourceCalendarID
	FROM dbo.ev_calendarEvents ce
	WHERE ce.sourceEventID = @eventID;

	SELECT @eventTitle = ISNULL(NULLIF(eventcontent.contentTitle, ''), 'Event ID ' + CAST(@eventID AS varchar(10)))
	FROM dbo.ev_events AS e
	CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) as eventcontent
	WHERE e.eventID = @eventID;

	-- Capture old category list for audit logging
	SELECT @oldCategoryList = COALESCE(@oldCategoryList + ', ', '') + c.category
	FROM dbo.ev_eventCategories ec
	INNER JOIN dbo.ev_categories c ON c.categoryID = ec.categoryID
	WHERE ec.eventID = @eventID
	ORDER BY ec.categoryOrder, c.category;

	INSERT INTO @tmpEVCategories (categoryID, categoryOrder)
	SELECT c.categoryID, MIN(li.autoid)
	FROM dbo.fn_intListToTable(@categoryIDList,',') as li
	INNER JOIN dbo.ev_categories as c on c.categoryID = li.listItem
	GROUP BY c.categoryID;

	SELECT @SWFeaturedProgramID = featuredID, @SWParticipantID = participantID
	FROM seminarWeb.dbo.tblFeaturedPrograms
	WHERE eventID = @eventID;

	BEGIN TRAN;
		DELETE ec
		FROM dbo.ev_eventCategories AS ec
		LEFT OUTER JOIN @tmpEVCategories AS tmp ON tmp.categoryID = ec.categoryID
		WHERE ec.eventID = @eventID
		AND tmp.categoryID IS NULL;

		MERGE dbo.ev_eventCategories AS target USING @tmpEVCategories AS source ON source.categoryID = target.categoryID AND target.eventID = @eventID
		WHEN MATCHED AND target.categoryOrder <> source.categoryOrder THEN 
			UPDATE SET target.categoryOrder = source.categoryOrder
		WHEN NOT MATCHED THEN
			INSERT(eventID, categoryID, categoryOrder) 
			VALUES(@eventID, source.categoryID, source.categoryOrder);

		IF @SWFeaturedProgramID IS NOT NULL BEGIN
			SELECT @SWCalendarID = calendarID, @SWCategoryCount = COUNT(categoryID)
			FROM seminarWeb.dbo.tblParticipantEvents
			WHERE participantID = @SWParticipantID
			GROUP BY calendarID;

			IF @SWCategoryCount > 0
				INSERT INTO @tmpSWEventCategories (categoryID)
				select ec.categoryID
				from dbo.ev_categories as ec
				inner join seminarWeb.dbo.tblParticipantEvents as pe on pe.calendarID = @SWCalendarID 
					and pe.calendarID = ec.calendarID
					and pe.categoryID = ec.categoryID
				where pe.participantID = @SWParticipantID;
			ELSE
				INSERT INTO @tmpSWEventCategories (categoryID)
				select categoryID
				from dbo.ev_categories
				where calendarID = @SWCalendarID;

			IF NOT EXISTS (select 1 from @tmpEVCategories as evc inner join @tmpSWEventCategories as swevc on swevc.categoryID = evc.categoryID)
				EXEC seminarWeb.dbo.sw_toggleFeaturedProgram @orgCode=@siteCode, @programType='EV', @programID=@eventID, @isFeatured=0, @recordedByMemberID=@recordedByMemberID;
		END

		-- Generate new category list and audit log if categories changed
		SELECT @newCategoryList = COALESCE(@newCategoryList + ', ', '') + c.category
		FROM @tmpEVCategories tmp
		INNER JOIN dbo.ev_categories c ON c.categoryID = tmp.categoryID
		ORDER BY tmp.categoryOrder, c.category;

		IF ISNULL(@oldCategoryList, '') <> ISNULL(@newCategoryList, '') BEGIN
			DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
			SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '] categories updated from [' + ISNULL(@oldCategoryList, 'None') + '] to [' + ISNULL(@newCategoryList, 'None') + '].', 'json');
			EXEC dbo.ev_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='EVENT', @msgjson=@msgjson, @evKeyMapJSON=@evKeyMapJSON, @isImport=0, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
