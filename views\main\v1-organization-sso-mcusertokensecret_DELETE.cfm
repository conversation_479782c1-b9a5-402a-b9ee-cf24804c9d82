<div class="delete method-example">
	<div class="method-wrapper">
		<div class="method">DELETE</div>
		<div class="method-text">
			<div style="float:left;">/v1/organization/sso/mcusertokensecret</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
DELETE /v1/organization/sso/mcusertokensecret HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 SECRET NOT FOUND</td><td>secret is not defined</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error deleting secret</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 SECRET NOT FOUND

{
    "data": {},
    "error":true,
    "messages":[
        "MCUserToken Secret not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>