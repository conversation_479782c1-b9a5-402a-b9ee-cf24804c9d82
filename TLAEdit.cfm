<!--- Setup form variables --->
<cfscript>
if (isDefined("request.orgCode")) vorgCode = request.orgCode;
else vorgCode = "";
</cfscript>

<cfparam name="request.downloadExcel" default="0">

<cfif val(request.downloadExcel) eq 1>
	<cfset request.showDecomSites =  isDefined("request.showDecomSites") and request.showDecomSites is 1 ? 1 : 0>
<cfelseif isDefined("request.showDecomSites")>
	<cfset request.downloadExcel = 0>
	<cfset request.showDecomSites =  isDefined("request.showDecomSites") and request.showDecomSites is 1 ? 1 : 0>
<cfelse>
	<cfset request.showDecomSites = 0>
</cfif>

<!--- if we are saving the form --->
<cfif isdefined("request.update")>
	<cfquery name="getTLA" datasource="#application.settings.dsn.trialsmith.dsn#">
		select top 1 state 
		from depotla 
		where state = <cfqueryparam value="#trim(request.orgcode)#" cfsqltype="CF_SQL_VARCHAR">
	</cfquery>

	<cfset local.msg = 0>
	
	<cfif getTLA.recordcount is 1>
		<cfquery name="updateTLA" datasource="#application.settings.dsn.trialsmith.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgCode varchar(10) = <cfqueryparam value="#trim(request.orgcode)#" cfsqltype="CF_SQL_VARCHAR">;

			update dbo.depotla 
			set description = <cfqueryparam value="#trim(request.description)#" cfsqltype="CF_SQL_VARCHAR">,
				message = <cfqueryparam value="#trim(request.Message)#" cfsqltype="CF_SQL_VARCHAR">,
				shortname = <cfqueryparam value="#trim(request.shortname)#" cfsqltype="CF_SQL_VARCHAR">,
				url = <cfqueryparam value="#trim(request.websiteurl)#" cfsqltype="CF_SQL_VARCHAR">
			where state = @orgCode;
		</cfquery>

		<cfquery name="local.getSWParticipantID" datasource="#application.settings.dsn.seminarweb.dsn#">
			SELECT dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam value="#trim(request.orgcode)#" cfsqltype="CF_SQL_VARCHAR">) as participantID
		</cfquery>

		<cfif local.getSWParticipantID.participantID gt 0>
			<cfstoredproc datasource="#application.settings.dsn.seminarweb.dsn#" procedure="sw_CorrectCatalogURL">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.getSWParticipantID.participantID#">
			</cfstoredproc>
		</cfif>
		<cfset local.msg = 1>
	</cfif>

	<cflocation url="TLAEdit.cfm?orgCode=#trim(request.orgcode)#&msg=#local.msg#" addtoken="no">

<cfelseif request.downloadExcel is 1>	
	<cfsetting requesttimeout="300">

	<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="tsadmin")>

	<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="tla_getAssociations">
		<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\ManageAssociations.csv">
		<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#request.EffectiveDate#">
		<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#request.showDecomSites#">
	</cfstoredproc>

	<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/ManageAssociations.csv", 
		displayName="ManageAssociations.csv", deleteSourceFile=false, forceDownload=true)>
	<cfabort>

<cfelseif len(vorgCode) is 0>
	<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="tla_getAssociations">
		<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" null="true">
		<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#dateformat(now(),'m/d/yyyy')#">
		<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#request.showDecomSites#">
		<cfprocresult name="qdepotla">
	</cfstoredproc>

	<cfoutput>
	<style>
		.greenCheck { color: green; }
		.redCross { color: red; opacity: 0.33; }
		.redBadge {
			background-color: ##f83245;
			color: white;
			padding: 4px 8px;
			text-align: center;
			border-radius: 3px;
		}
	</style>
	<script>
		function filterTLAList() {
			var sds = document.getElementById('showDecomSites').checked;
			self.location.href='TLAEdit.cfm' + (sds == true ? '?showDecomSites=1' : '');
		}
	</script>

	<div id="crumbs">
		You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
		Manage Associations
	</div>
	<div id="pageTitle">Manage Associations</div>

	<div style="margin-bottom:10px;">
		<cfform action="TLAEdit.cfm" method="post" name="frmTLA">
			<input type="hidden" name="downloadExcel" value="1">
			<table>
				<tr>
					<td><input type="checkbox" name="showDecomSites" id="showDecomSites" value="1" <cfif request.showDecomSites eq 1>checked</cfif> onClick="filterTLAList();"></td>
					<td><label for="showDecomSites">Show Decommissioned Sites</label></td>
					<td width="100"></td>
					<td><input type="submit" name="export" value="Export CSV" > </td><td>using</td><td><script>DateInput('EffectiveDate', true, 'MM/DD/YYYY', '#DateFormat(now(),'m/d/yyyy')#')</script></td><td>as the effective date for billing fields.</td>
				</tr>
			</table>
		</cfform>
	</div>

	<table class="tblSlim">
	<tr>
		<th rowspan="2">&nbsp;</th>
		<th rowspan="2">OrgCode</th>
		<th rowspan="2">Org Name (Short Name)</th>
		<th rowspan="2">Billing<br/>Record</th>
		<th colspan="5" class="c" style="border-left:1px solid ##999;">MemberCentral</th>
		<th colspan="2" class="c" style="border-left:1px solid ##999;">SeminarWeb</th>
		<th colspan="2" class="c" style="border-left:1px solid ##999;">Mkt Lists</th>
	</tr>
	<tr>
		<th class="c" style="border-left:1px solid ##999;">Website</th>
		<th class="c">AMS</th>
		<th class="c">Admins</th>
		<th>Login Network</th>
		<th>Site PWD</th>
		<th class="c" style="border-left:1px solid ##999;">SWL</th>
		<th class="c">SWOD</th>
		<th class="c" style="border-left:1px solid ##999;">TS</th>
		<th class="c">SW</th>
	</tr>
	<cfloop query="qdepotla">
		<tr>
			<td>#qdepotla.currentRow#</td>
			<td><a class="b" href="TLAEdit.cfm?orgCode=#qdepotla.orgcode#">#qdepotla.orgcode#</a></td>
			<td>
				<span class="b">#qdepotla.description# <cfif len(qdepotla.shortname)>(#qdepotla.ShortName#)</cfif></span>
				<cfif len(qdepotla.HostedHostname)><span class="dim50 b"><br/>&nbsp; &nbsp; &nbsp; &nbsp; Hosted Hostname:</span> <span class="dim50">#qdepotla.HostedHostname#</span></cfif>
				<cfif len(qdepotla.AssnHomepage)><span class="dim50 b"><br/>&nbsp; &nbsp; &nbsp; &nbsp; Assn Homepage:</span> <span class="dim50">#qdepotla.AssnHomepage#</span></cfif>
			</td>
			<td>
				<cfif qdepotla.BillingDepoMemberDataID gt 0>
					<a href="/MemberEdit.cfm?depoMemberDataID=#qdepotla.BillingDepoMemberDataID#" target="_blank">#qdepotla.BillingDepoMemberDataID#</a> &nbsp;
				<cfelse>
					<span class="dim">N/A</span>
				</cfif>
			</td>

			<td class="c bl">
				<cfif qdepotla.siteResourceStatusID eq 3>
					<span class="redBadge">Decommissioned</span>
				<cfelseif qdepotla.MCCreated eq "Yes" and len(qdepotla.HostedHostname)>
					<a href="#qdepotla.HostedHostname#">Website</a>
				<cfelse>
					<span class="dim">N/A</span><br/>
				</cfif>
			</td>
			<td class="c">
				<cfif qdepotla.MCAMSClient eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif>
			</td>
			<td class="c">
				<cfif qdepotla.siteResourceStatusID neq 3 and qdepotla.MCCreated eq "Yes">
					<a href="#qdepotla.HostedHostname#/?pg=admin&mca_s=3&mca_a=20&mca_tt=11&mca_ta=edit&groupID=#qdepotla.siteAdminGroupID#" target="_blank">
					<cfif qdepotla.MCAMSClient eq "Yes" and (qdepotla.MCAdminThreshold is 0 or qdepotla.MCAdminCount gt qdepotla.MCAdminThreshold)>
						<span class="red b">#qdepotla.MCAdminCount# of <cfif qdepotla.MCAdminThreshold gt 0>#qdepotla.MCAdminThreshold#<cfelse>&infin;</cfif></span><br/><img src="media/warning-icon.png">
					<cfelse>
						#qdepotla.MCAdminCount# of <cfif qdepotla.MCAdminThreshold gt 0>#qdepotla.MCAdminThreshold#<cfelse>&infin;</cfif>
					</cfif>
					</a>
				<cfelse>
					<span class="dim">N/A</span><br/>
				</cfif>
			</td>
			<td>
				<cfif len(qdepotla.LoginNetwork)>
					#qdepotla.LoginNetwork#
				<cfelse>
					<span class="dim">N/A</span><br/>
				</cfif>
			</td>
			<td class="c">
				<cfif qdepotla.SiteSpecificPasswords eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif>
			</td>

			<td class="c bl"> <cfif qdepotla.SeminarWebSWL eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif> </td>
			<td class="c"> <cfif qdepotla.SeminarWebSWOD eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif></td>
			<td class="c bl"> <cfif qdepotla.MarketingTS eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif></td>
			<td class="c"> <cfif qdepotla.MarketingSW eq 'YES'><i class="fa-solid fa-check greenCheck"></i><cfelse><i class="fa-solid fa-xmark redCross"></i></cfif></td>
		</tr>
	</cfloop>
	</table>
	</cfoutput>

<!--- editing TLA --->
<cfelse>
	<cfstoredproc datasource="#application.settings.dsn.trialsmith.dsn#" procedure="tla_getAssociations">
		<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" null="true">
		<cfprocparam type="IN" cfsqltype="CF_SQL_DATE" value="#dateformat(now(),'m/d/yyyy')#">
		<cfprocresult name="qdepotla">
	</cfstoredproc>
	<cfset qryTLA = qdepotla.filter(function(row) { return row.orgcode eq vorgCode })>
	<cfoutput>
	<style>
	.alert { background:##fff6bf url(/media/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
	.info { background:##cff3f8 url(/media/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##bceff5; border-bottom:2px solid ##bceff5; }
	.success { background:##fff6bf url(/media/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0; }
	</style>
	<script>
	function validateAssociationFrm() {
		let arrReq = [];
		if (arrReq.length) {
			alert(arrReq.join('\n'));
			return false;
		}

		return true;
	}
	</script>

	<div id="crumbs">
		You are here: <a href="adm_depomenu.cfm">Admin menu</a> \
		<a href="TLAEdit.cfm">Manage Associations</a> \
		#qryTLA.orgcode# - #qryTLA.description#	
	</div>
	<div id="pageTitle">
		#qryTLA.orgcode# - #qryTLA.description#
	</div>

	<cfif isDefined("request.msg")>
		<cfswitch expression="#request.msg#">
			<cfcase value="1">
				<div class="success">Association updated successfully.</div>
			</cfcase>
			<cfdefaultcase>
				<div class="alert">Association was not found.</div>
			</cfdefaultcase>
		</cfswitch>
		<br/>
	</cfif>

	<cfform action="TLAEdit.cfm" method="post" name="frmTLA" onsubmit="return validateAssociationFrm();">
		<input type="hidden" name="orgcode" value="#qryTLA.orgcode#">
		<br/>
		<fieldset>
			<legend><b>Basic Info</b></legend>
			<table cellspacing="0" cellpadding="2">
			<tr>
				<td width="250" class="red">Org Code:</td>
				<td class="red">(R)</td>
				<td>#qryTLA.orgcode#</td>
			</tr>
			<tr>
				<td width="250" class="red">Description:</td>
				<td class="red">(R)</td>
				<td><cfinput type="text" name="description" required="Yes" message="You must enter a description" size="60" value="#qryTLA.description#"></td>
			</tr>
			<tr>
				<td width="250">Header Message (if not description):</td>
				<td></td>
				<td><cfinput type="text" name="message" required="No" size="60" value="#qryTLA.HeaderMessage#"></td>
			</tr>
			<tr>
				<td width="250" class="red">Short Name:</td>
				<td class="red">(R)</td>
				<td><cfinput type="text" name="shortname" required="No" size="15" value="#qryTLA.shortname#"></td>
			</tr>
			<tr>
				<td width="250">Website URL:</td>
				<td></td>
				<td><cfinput type="text" name="websiteurl" required="No" size="50" value="#qryTLA.AssnHomepage#"></td>
			</tr>
			<tr>
				<td colspan="2"></td>
				<td><input type="submit" name="update" value="Save Changes"></td>
			</tr>
			</table>
		</fieldset>
		<br/>
		<div style="padding-left: .5rem !important; margin-bottom: .5rem !important; align-content: center !important; align-items: center !important; display: flex !important; color: ##824224;
  background-color: ##fde4d5; border-color: ##fcd9c4; position: relative; padding: .75rem 1.25rem; border: 1px solid transparent; border-radius: .65rem;">
			<div style="margin-left: 1rem;">
				<strong style="display: block !important;margin-bottom: .5rem;">Reminder</strong>
				Billing Information and Fees has moved to 
				<cfif len(qryTLA.HostedHostname) and qryTLA.siteResourceStatusID is 1>
					<a href="#qryTLA.HostedHostname#/?pg=admin&jumpToTool=TrialSmithBilling" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-left:.3rem;padding-right:.3rem;"></i>Control Panel.</a> 
				<cfelse>
					Control Panel, however this association does not have an available Control Panel to link to.
				</cfif>
			</div>
		</div>
	</cfform>
	</cfoutput>
</cfif>