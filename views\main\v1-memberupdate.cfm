﻿<a name="v1-memberupdate"></a>
<section id="v1-memberupdate">	
	<h3>/memberupdate</h3>
	<p>
		GET - Returns the possible keys/columns to use when adding or updating member records.<br/>
		POST - Upload a member file to add/update multiple member records.
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/memberupdate</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/memberupdate HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
        <div class="jsonblock-table">
            <table>
            <tr><td class="rc">200 OK</td><td>success</td></tr>
            <tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
            <tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
            </table>
        </div>
        <div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock-note">Note: The array labels ("Demographics", "Websites", "Addresses", etc) are for clarity only and should not be passed when adding or updating member records.</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "Demographics": [
            "company",
            "firstname",
            "lastname",
            "mcaccountstatus",
            "mcaccounttype",
            "mcrecordtype",
            "membernumber",
            "middlename",
            "prefix",
            "suffix"
        ],
        "Websites": [
            "blog",
            "facebook",
            "website"
        ],
        "Emails": [
            "alternate email",
            "email"
        ],
        "Addresses": [
            "billingaddresstype",
            "primary address_address1",
            "primary address_address2",
            "primary address_address3",
            "primary address_attn",
            "primary address_city",
            "primary address_country",
            "primary address_county",
            "primary address_fax",
            "primary address_phone",
            "primary address_postalcode",
            "primary address_stateprov"
        ],
        "Professional Licenses": [
            "mystate_activedate",
            "mystate_licensenumber",
            "mystate_status"
        ],
        "Custom Fields": [
            "dateofbirth",
            "region"
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="post method-example">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">/v1/memberupdate</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Required Request Details</div>
		<div class="jsonblock-info">
			The request must contain a multi-part body with two form fields: <i>members</i> and <i>options</i>.<br/>
			<div style="margin-left:30px;">
				<i>members</i> is your members CSV file containing the members and columns you want to update.<br/>
				<i>options</i> is a JSON object of the options for this member update. The supported keys are:
				<div style="margin-left:30px;">
					<b>Required Key:</b><br/>
					<i>sync</i> = partial | full<br/>
					<div style="margin-left:30px;">
						Use "partial" to send a partial list of members to add or update.<br/>
						Use "full" to send a complete list of active members to sync. We will ensure all members in your file are set to Active and all Users not in your file are set to Inactive.<br/>
					</div>
                    <br/>
					<b>Optional Keys for Partial Sync:</b><br/>
					<i>setIncludedActive</i> = true | false (default is false)<br/>
					<div style="margin-left:30px;">
						If "true", we will ensure all members in the uploaded file are set to Active.<br/>
						If "false", status will be untouched.
					</div>
					<i>generateMemberNumber</i> = true | false (default is false)<br/>
					<div style="margin-left:30px;">
						If "true", we will ensure those without membernumbers in the uploaded file are added with auto-generated membernumbers.<br/>
						If "false", members missing membernumbers will prevent the update.
					</div>
                    <i>ignoreInvalidColumns</i> = true | false (default is true)<br/>
                    <div style="margin-left:30px;">
                        If "true", we will ignore invalid columns in the uploaded file.<br/>
                        If "false", we will prevent the update if any invalid columns are found in the uploaded file.
                    </div>
					<br/>
					<b>Optional Keys for Full Sync:</b><br/>
					<i>inactivateThreshold</i> = 500 (default is 500)<br/>
					<div style="margin-left:30px;">
						We will prevent the update if more than this number of members will be inactivated.
					</div>
                    <i>ignoreInvalidColumns</i> = true | false (default is true)<br/>
                    <div style="margin-left:30px;">
                        If "true", we will ignore invalid columns in the uploaded file.<br/>
                        If "false", we will prevent the update if any invalid columns are found in the uploaded file.
                    </div>
                </div>
			</div>			
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
POST /v1/memberupdate HTTP/1.1
Content-Type: multipart/form-data; boundary=---011000010111000001101001
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 221

-----011000010111000001101001
Content-Disposition: form-data; name="members"


-----011000010111000001101001
Content-Disposition: form-data; name="options"

{
    "sync":"partial"
}
-----011000010111000001101001--
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
        <div class="jsonblock-table">
            <table>
            <tr><td class="rc">200 OK</td><td>success</td></tr>
            <tr><td class="rc">202 NOT UPDATED</td><td>no changes to process</td></tr>
            <tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
            <tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
            <tr><td class="rc">406 NOT ACCEPTABLE</td><td>unable to process file</td></tr>
            <tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON object, missing file, or invalid file</td></tr>
            </table>
        </div>
        <div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK
{
    "data": {
        "result": "Upload accepted."
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - First name changed from Pat to NewFirstName",
                ...
            ]
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (no changes)</div>
		<div class="jsonblock">
<pre class="prettyprint">
202 NOT UPDATED

{
    "data": {
        "result": "No changes to process.",
        "report": [
            "summary": [
                "1 data change was rejected and ignored.",
                ...
            ],
            "files": [
               "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
               ...
            ],
            "rejected": [
                "LastName, NewFirstName (SAMPLE123456) - website is not valid: http://mysite"
            ],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": []
        ]
    },
    "error": false,
    "messages": []
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
406 NOT ACCEPTABLE

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to process member file.",
        ...
    ]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
417 EXPECTATION FAILED

{
    "data": {},
    "error": true,
    "messages": [
        "Invalid file type.",
        "Only CSV files are accepted.",
        ...
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>			

	<div class="delete">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>