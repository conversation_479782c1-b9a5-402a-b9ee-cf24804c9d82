<!--- any members to approve? --->
<cfquery name="qryApprovalNeeded" datasource="#application.settings.dsn.trialsmith.dsn#">
	SELECT count(m.depomemberdataID) as approveCount
	FROM dbo.depomemberdata m 
	WHERE m.Pending = '0'
</cfquery>

<!--- anyone in the merge queue from membercentral platform? --->
<cfquery name="qryMergeQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
	SELECT count(distinct mergeID) as mergeCount
	FROM dbo.pendingMerges 
</cfquery>

<!--- anyone have pending transactions but are no longer pending? --->
<cfquery name="qryPendingTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
	SELECT top 1 t.transactionID
	FROM dbo.depoTransactions as t
	INNER JOIN dbo.depoMemberData as d on d.depoMemberDataID = t.depoMemberDataID
	WHERE t.madeWhilePending = 1
	AND d.Pending = '1'
</cfquery>

<!--- documents pending? --->
<cfset local.objDocuments = CreateObject("component","models.tsadmin.act_documents")>
<cfset numDocumentsPending = local.objDocuments.getDocumentsPending()>

<!--- Crumb and title --->
<cfoutput>
<div id="crumbs">
	You are here: Administration Menu
</div>
</cfoutput>

<table border="0" cellpadding="5" cellspacing="0" width="100%" class="navtable">
<tr valign="top">
    <td width="25%">
		<div class="menuSectionTitle">Billing</div>
		<div>
		<div class="navrow"><button type="button" name="viewoutstanding" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Outstanding Balances</div>
		<div class="navrow"><button type="button" name="viewcreditbalance" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Credit Balances</div>
		<div class="navrow"><button type="button" name="generateinvoices" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Generate Statements</div>
		<div class="navrow"><button type="button" name="PurchaseCreditFind" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Find Purchase Credits</div>
		<div class="navrow"><button type="button" name="PurchaseCreditNegative" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Negative Purchase Credit Balances</div>
		<div class="navrow"><button type="button" name="importTransactions" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Import Transactions</div>
		<div class="navrow"><button type="button" name="manageSalesTaxProfiles" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Sales Tax Profiles</div>
		<div class="navrow"><button type="button" name="payablesReport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Monthly Billing Payables Report</div>

		<cfif qryPendingTrans.recordCount>
			<div class="pendingTransAlert"><img src="media/warning.gif" width="16" height="16"> 
				There are approved/denied members with pending transactions that need to be cleared. 
				<a href="MemberList.cfm?Pending=1&PendingTRX=1">View them</a>
			</div>
		</cfif>

		</div>
	</td>
	<td width="25%">
		<div class="menuSectionTitle">Members</div>
		<div>
		<div class="navrow"><button type="button" name="ApproveList" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> <span <cfif qryApprovalNeeded.approveCount>style="font-weight:bold;color:#f00;"</cfif>>Approve New Members <cfif qryApprovalNeeded.approveCount><cfoutput>(#qryApprovalNeeded.approveCount#)</cfoutput></cfif></span></div>
		<div class="navrow"><button type="button" name="MergeQueue" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> <span <cfif qryMergeQueue.mergeCount>style="font-weight:bold;color:#f00;"</cfif>>Review Merge Queue <cfif qryMergeQueue.mergeCount><cfoutput>(#qryMergeQueue.mergeCount#)</cfoutput></cfif></span></div>
		<div class="navrow"><button type="button" name="EditMember" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Find Member Accounts</div>
		<div class="navrow"><button type="button" name="memberExport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Export Member Data</div>
		<div class="navrow"><button type="button" name="CreateMem" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Create Member Account</div>
	    <div class="navrow"><button type="button" name="manageFirmPlans" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Firm Plans</div>
        <div class="navrow"><button type="button" name="expires" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Member Expiration List</div>
		</div>
	</td>
    <td width="25%">
		<div class="menuSectionTitle">Documents</div>
		<div>
		<div class="navrow"><button type="button" name="DocumentsFind" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> <span <cfif numDocumentsPending gt 0>style="font-weight:bold;color:#f00;"</cfif>>Find and Process Documents <cfif numDocumentsPending gt 0>(<cfoutput>#numDocumentsPending# Pending</cfoutput>)</cfif></span></div>
	    <div class="navrow"><button type="button" name="ContribList" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Documents Contributed List</div>
		<div class="navrow"><button type="button" name="GenReport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Document Contributions Report</div>
		<div class="navrow"><button type="button" name="DocumentViewsReport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Document Views Report</div>
		<div class="navrow"><button type="button" name="DocumentSettings" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Document Settings</div>
		<div class="navrow"><button type="button" name="OneClickPurchases" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> One-Click Purchases</div>
		</div>
	</td>
	<td width="25%">
		<div class="menuSectionTitle">Searching</div>
		<div>
        <div class="navrow"><button type="button" name="QueryHistory" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Search History</div>
		</div>
	</td>
</tr>
<tr valign="top">
    <td>
		<div class="menuSectionTitle">Reports</div>
		<div>
		<div class="navrow"><button type="button" name="UberReport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Sales Report</div>
		<div class="navrow"><button type="button" name="MTDYTDReport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> MTD/YTD Report</div>
		<div class="navrow"><button type="button" name="ListTrans" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Transaction Report</div>
		<div class="navrow"><button type="button" name="viewpayments" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Payment Report</div>
		</div>
	</td>
    <td>
		<div class="menuSectionTitle">Partner Associations</div>
		<div>
		<div class="navrow"><button type="button" name="managetlas" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Associations</div>
		<div class="navrow"><button type="button" name="ManageGroup" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Court Doc Banks</div>
		<div class="navrow"><button type="button" name="ManagePlans" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Plans</div>
		<div class="navrow"><button type="button" name="ManagePriceGroups" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Price Groups</div>
		<div class="navrow"><button type="button" name="ManagePromoCodes" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Manage Promo Codes</div>
		</div>
	</td>
	<td>
		<div class="menuSectionTitle">Lyris</div>
		<div>
		<div class="navrow"><button type="button" name="supersubmanger" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Super Subscription Manager</div>
		<div class="navrow"><button type="button" name="listsedit" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> List Integration Settings</div>
		<div class="navrow"><button type="button" name="listsheaderfooter" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> List Headers/Footers</div>
		<div class="navrow"><button type="button" name="messageremove" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Remove Archived Message</div>
		<div class="navrow"><button type="button" name="listintegrationimport" onclick="adminMenuJump(this.name);"><i class="fa-regular fa-angles-right"></i></button> Import List Integration Changes</div>
		</div>
	</td>
    <td></td>
</tr>
</table>

<br/><b>Tools Moved to Control Panel:</b><br/>
<table border="0" cellpadding="5" cellspacing="0">
<tr valign="top">
	<td style="line-height:2em;">
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=AmazonBucks%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Amazon Bucks Report</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=DocumentRoyalty%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Document Royalty Report</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=ImportDaubertUpdate%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Import Daubert Monthly Update</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=LyrisListReport%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Lyris List Reports</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=NATLEInFocus%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>NATLE InFocus Report</a></div>
	</td>
	<td width="20">&nbsp;</td>
	<td style="line-height:2em;">
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=SalesTaxFiling%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Sales Tax Filing Report</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=SiteAdmins%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Site Admins Report</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=SponsorMailingMarketing%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Sponsor Mailing Marketing Report</a></div>
		<div><a href="https://www.trialsmith.com/?pg=admin&jumpToTool=SubscriptionRoyalty%7Clist%7Clist" target="_blank"><i class="fa-solid fa-arrow-up-right-from-square" style="padding-right:6px;"></i>Subscription Royalty Report</a></div>
	</td>
</tr>
</table>
