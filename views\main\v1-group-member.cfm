<a name="v1-group-member"></a>
<section id="v1-group-member">
	<h3>/group/member</h3>
	<p>
		GET - Returns the group's member assignments<br/>
		PUT - Manually assigns the member to the group<br/>
		DELETE - Removes a member's manual assignment to the group<br/>
	</p>

	<h4>Required Request Headers</h4>
	<p>Authorization: Bearer YOUR_API_TOKEN</p>

	<h4>Methods</h4>
	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/group/{api_id}/member</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">Optional Request Details</div>
		<div class="jsonblock-info">
			The request may optionally contain a JSON object in the body with one or more of the following keys:<br/>
			<ul>
			<li><i>start</i> is the 0-based position of the overall resultset the results should start with. The default when not provided is 0.</li>
			<li><i>count</i> is the number of results to include in the response. The default when not provided is 10.</li>
			<li><i>datelastupdated</i> is the start date (m/d/yyyy) or datetime in 24-hour format (m/d/yyyy HH:mm) you'd like to limit members to. The default action is to not consider the date the member was last updated. This date is US Central Time.</li>
			<li>
				<i>search</i> is a structure of subkeys used to limit the members returned.<br/>
				Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
				If your JSON object contains invalid search subkeys, an <i>ignoredfields</i> array is returned in the response.
			</li>
			<li>
				<i>result</i> is an array of subkeys that specifies what information to return for each member. Subkeys defined here will be returned in addition to the standard data described below.<br/>
				Use the /v1/member/{membernumber} GET API reference for the list of eligible subkeys.<br/>
				If your JSON object contains invalid result subkeys, an <i>ignoredfields</i> array is returned in the response.
			</li>
			</ul>
		</div>

		<div class="jsonblock-head">Response Details</div>
		<div class="jsonblock-info">
			Each member listed in the response will have the same data as in the GET /members endpoint along with the following data:
			<ul>
				<li><i>x-member-api-uri</i> - the URI of the member</li>
				<li><i>x-api-uri</i> - the URI of the group assignment</li>
				<li><i>directmanual</i> - if the member is manually assigned to this group</li>
				<li><i>directvirtual</i> - if the member is assigned to this group via a group assignment rule</li>
				<li><i>indirectmanual</i> - if the member is manually assigned to a lower group in the group path</li>
				<li><i>indirectvirtual</i> - if the member is assigned to a lower group via a group assignment rule</li>
			</ul>
		</div>

		<div class="jsonblock-head">Sample Request</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/member HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
		</div>
		<div class="jsonblock">
<pre class="prettyprint">
GET /v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX/member HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 146

{
    "count": 5,
    "datelastupdated": <cfoutput>"#month(now())#/1/#year(now())# 16:30"</cfoutput>,
    "search": {
        "primary address_city": "Austin",
        "language": "Spanish"
    },
    "result": [
        "email",
        "primary address_state"
    ]
}
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 OK</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 GROUP NOT FOUND</td><td>invalid group api_id</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count":10,
        "members":[
            {
                "membernumber":"SAMPLE123456",
                "prefix":"Mrs.",
                "firstname":"Jane",
                "middlename":"",
                "lastname":"Doe",
                "suffix":"",
                "professionalsuffix":"",
                "company":"Law Office of Jane Doe",
                "datecreated":"August, 05 2017 09:57:31 -0700",
                "datelastupdated":"August, 09 2017 19:09:36 -0700",
                "mcaccountstatus":"Active",
                "mcaccounttype":"User",
                "mcrecordtype":"Individual",
                "username":"JaneDoe",
                "x-member-api-uri":"/v1/member/SAMPLE123456",
                "x-member-photo-uri":"https://www.yourwebsite.org/memberphotos/sample123456.jpg",
                "x-api-uri":"/v1/member/SAMPLE123456/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                "indirectmanual": 0,
                "directvirtual": 0,
                "indirectvirtual": 0,
                "directmanual": 1,
            } ...
        ]
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 GROUP NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Group not found."
    ]
}
</pre>
		</div>		
	</div>
	<div style="clear:both;padding-top:10px;"></div>	

	<div class="get method-example">
		<div class="method-wrapper">
			<div class="method">GET</div>
			<div class="method-text">
				<div style="float:left;">/v1/group/{api_id}/member/{membernumber}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">This call behaves the same as <a href="#v1-member-group">/v1/member/{membernumber}/group/{api_id}</a></div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="post">
		<div class="method-wrapper">
			<div class="method">POST</div>
			<div class="method-text">
				<div style="float:left;">not applicable</div>
			</div>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="put method-example">
		<div class="method-wrapper">
			<div class="method">PUT</div>
			<div class="method-text">
				<div style="float:left;">/v1/group/{api_id}/member/{membernumber}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">This call behaves the same as <a href="#v1-member-group">/v1/member/{membernumber}/group/{api_id}</a></div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>

	<div class="delete method-example">
		<div class="method-wrapper">
			<div class="method">DELETE</div>
			<div class="method-text">
				<div style="float:left;">/v1/group/{api_id}/member/{membernumber}</div>
				<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
			</div>
		</div>
	</div>
	<div class="method-exampleCode" style="display:none;">
		<div class="jsonblock-head">This call behaves the same as <a href="#v1-member-group">/v1/member/{membernumber}/group/{api_id}</a></div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>
</section>