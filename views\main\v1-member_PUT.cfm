<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with keys shown in the /v1/member/{membernumber} GET API reference.<br/>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456 HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 32

{
    "firstname": "NewFirstName"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">202 NOT UPDATED</td><td>no changes to process</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error updating member</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "result": "Member updated.",
        "report": [
            "summary": [
                "1 existing member will be updated.",
                ...
            ],
            "files": [
                "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
                ...
            ],
            "rejected": [],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": [
                "LastName, NewFirstName (SAMPLE123456) - First name changed from Pat to NewFirstName",
                ...
            ]
        ]
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (no changes)</div>
	<div class="jsonblock">
<pre class="prettyprint">
202 NOT UPDATED

{
    "data": {
        "result": "No changes to process.",
        "report": [
            "summary": [
                "1 data change was rejected and ignored.",
                ...
            ],
            "files": [
                "&lt;a href=\"http://www.mysite.org/LINK_TO_FILE\">Submitted Data</a>", 
                ...
            ],
            "rejected": [
                "LastName, NewFirstName (SAMPLE123456) - website is not valid: http://mysite"
            ],
            "newmembers": [],
            "guestmembers": [],
            "existingmembers": []
        ]
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update member.",
	    ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>