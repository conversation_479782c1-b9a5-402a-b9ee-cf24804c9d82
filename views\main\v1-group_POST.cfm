<div class="post method-example">
	<div class="method-wrapper">
		<div class="method">POST</div>
		<div class="method-text">
			<div style="float:left;">/v1/group</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the following keys. Only the required keys need to be provided.<br/>
		<div style="margin-left:30px;">
			<i>group</i> - name of the group (required)<br/>
			<i>groupcode</i> - unique group code for the group (optional)<br/>
			<i>description</i> - description of the group (optional)<br/>
			<i>manualassignments</i> - if manual assignments are allowed (optional, defaults to false)<br/>
			<i>badgebackgroundcolor</i> - badge background color (optional)<br/>
			<i>badgetextcolor</i> - badge text color (optional)<br/>
			<i>parentapi_id</i> - the new group would be a child of the provided parent group api_id (optional)<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
POST /v1/group HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{
    "group": "My Test Group A",
    "groupcode": "TestA",
    "parentapi_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">201 CREATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT CREATED</td><td>error creating group</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
201 CREATED

{
    "data": {
        "count":1,
        "result": "Group created.",
        "group": {
            "group": "My Test Group A"
            "groupcode": "TestA",
            "grouppath": "My Test Groups \\ My Test Group A",
            "description": "",
            "systemgroup": 0,
            "alertifpopulated": 0,
            "manualassignments": 0,
            "protected": 0,
            "membercount": 0,
            "badgebackgroundcolor": "#3b3e66",
            "badgetextcolor": "#ffffff",
            "api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "x-api-uri": "/v1/group/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
            "parentapi_id": "YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY",
            "x-parent-api-uri": "/v1/group/YYYYYYYY-YYYY-YYYY-YYYY-YYYYYYYYYYYY"
        }
    },
    "error": false,
    "messages": []
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT CREATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to create group.",
        ...
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>