<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/organization/sso/mcusertokensecret</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with one key, <i>mcusertokensecret</i>.<br/>
		<div style="margin-left:30px;">
			<i>mcusertokensecret</i> is the user token secret for calls to /member/{membernumber}/sso/mcusertokenjwt. Max length is 50 characters.
		</div>
	</div>

	<div class="jsonblock-head">Response Details</div>
	<div class="jsonblock-info">
		Upon saving the secret, the response will be the same as the call to GET /organization/sso/mcusertokensecret.
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/organization/sso/mcusertokensecret HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com

{
    "mcusertokensecret": "XXXX....XXXX"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 SECRET NOT FOUND</td><td>secret is not defined</td></tr>
		<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
		<tr><td class="rc">500 NOT UPDATED</td><td>error updating secret</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "mcusertokensecret": "XXXX...XXXX"
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error":true,
    "messages": [
        "MCUserToken Secret must be less than 50 characters."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>