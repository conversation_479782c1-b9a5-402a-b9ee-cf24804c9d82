<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/list/{listname}/{api_id}</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a JSON object in the body with the values to update. Only the keys with updated data should be provided to make changes.
		<div style="margin-left:30px;">
			<i>email</i> - e-mail address of the list membership<br/>
			<i>name</i> - member's name on the list<br/>
			<i>lockname</i> - 1 or 0, if the Lock Name flag to be set for this list membership<br/>
			<i>lockaddress</i> - 1 or 0, if the Lock Address flag to be set for this list membership<br/>
			<i>keepactive</i> - 1 or 0, if the Keep Active flag to be set for this list membership<br/>
			<i>membertype</i> - member type of the list membership.<br/>
			<i>subtype</i> - subscription type of the list membership.<br/>
		</div>
	</div>
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/list/samplelist/0000000 HTTP/1.1
Content-Type: application/json
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 111

{
    "name": "Jane Doe, Esq",
    "membertype": "normal",
    "subtype": "mail"
}
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
		<div class="jsonblock-table">
			<table>
			<tr><td class="rc">200 UPDATED</td><td>success</td></tr>
			<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
			<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
			<tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
			<tr><td class="rc">404 LIST NOT FOUND</td><td>invalid list name</td></tr>
			<tr><td class="rc">404 LIST MEMBERSHIP NOT FOUND</td><td>member has no membership for this list</td></tr>
			<tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON body</td></tr>
			<tr><td class="rc">500 NOT UPDATED</td><td>unable to update list membership</td></tr>
			</table>
		</div>
		<div class="jsonblock-head">Sample Response (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 UPDATED

{
    "data": {
        "count":1,
        "list": {
            "listname": "samplelist",
            "membertype": "normal",
            "subtype": "mail",
            "name": "Jane Doe, Esq",
            "email": "<EMAIL>",
            "lockname": 0,
            "lockaddress": 0,
            "keepactive": 0,
            "api_id": 0000000,
            "x-api-uri": "/v1/member/SAMPLE123456/list/samplelist/0000000"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
500 NOT UPDATED

{
    "data": {},
    "error": true,
    "messages": [
        "Unable to update list membership."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>