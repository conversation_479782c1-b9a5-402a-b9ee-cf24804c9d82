<div class="put method-example">
	<div class="method-wrapper">
		<div class="method">PUT</div>
		<div class="method-text">
			<div style="float:left;">/v1/member/{membernumber}/photo</div>
			<div class="codeicondiv"><i class="fa-light fa-square-code fa-lg pr-1"></i><span class="small">expand</span></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Required Request Details</div>
	<div class="jsonblock-info">
		The request must contain a multi-part body with two form fields: <i>image</i> and <i>options (optional)</i>.<br/>
		<div style="margin-left:30px;">
			<i>image</i> is your member photo. Only jpg images are supported now.<br/>
			<i>options</i> is a JSON object of the options for this member photo update. The supported keys are:
			<div style="margin-left:30px;">
				<i>x</i> - horizontal crop start point<br/>
				<i>y</i> - vertical crop start point<br/>
				<i>width</i> - crop width<br/>
				<i>height</i> - crop height<br/>
			</div>
		</div>			
	</div>

	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
PUT /v1/member/SAMPLE123456/photo HTTP/1.1
Content-Type: multipart/form-data; boundary=---011000010111000001101001
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
Content-Length: 221

-----011000010111000001101001
Content-Disposition: form-data; name="image"


-----011000010111000001101001
Content-Disposition: form-data; name="options"

{
    "x":"0",
    "y":"0",
    "width":"80",
    "height":"100"
}
-----011000010111000001101001--
</pre>
		</div>
		<div class="jsonblock-head">Possible Response Codes</div>
        <div class="jsonblock-table">
            <table>
            <tr><td class="rc">200 OK</td><td>success</td></tr>
            <tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
            <tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
            <tr><td class="rc">404 MEMBER NOT FOUND</td><td>invalid membernumber</td></tr>
            <tr><td class="rc">406 NOT ACCEPTABLE</td><td>unable to process member photo</td></tr>
            <tr><td class="rc">417 EXPECTATION FAILED</td><td>invalid JSON object, missing image, or invalid image</td></tr>
            </table>
        </div>
        <div class="jsonblock-head">Sample Response (success)</div>
		<div class="jsonblock">
<pre class="prettyprint">
200 OK
{
   "data": {
        "x-photo-uri":"https://www.yourwebsite.org/memberphotos/sample123456.jpg",
        "x-photothumb-uri":"https://www.yourwebsite.org/memberphotosth/sample123456.jpg"
    },
    "error":false,
    "messages":[]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
404 MEMBER NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Member not found."
    ]
}
</pre>
		</div>
		<div class="jsonblock-head">Sample Response (failure)</div>
		<div class="jsonblock">
<pre class="prettyprint">
417 EXPECTATION FAILED

{
    "data": {},
    "error": true,
    "messages": [
        "Invalid file type.",
        "Only JPG images are accepted.",
        ...
    ]
}
</pre>
		</div>
	</div>
	<div style="clear:both;padding-top:10px;"></div>